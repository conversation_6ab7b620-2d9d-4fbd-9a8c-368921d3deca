import { createClient } from '@supabase/supabase-js';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project-ref.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

// 创建Supabase客户端
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 数据库表类型定义
export interface Database {
  public: {
    Tables: {
      projects: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          database_url: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          database_url?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          database_url?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      authors: {
        Row: {
          id: string;
          project_id: string;
          name: string;
          email: string | null;
          bio: string | null;
          avatar_url: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          name: string;
          email?: string | null;
          bio?: string | null;
          avatar_url?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          name?: string;
          email?: string | null;
          bio?: string | null;
          avatar_url?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      prompts: {
        Row: {
          id: string;
          project_id: string;
          name: string;
          content: string;
          category: string;
          language: string;
          variables: any | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          name: string;
          content: string;
          category?: string;
          language?: string;
          variables?: any | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          name?: string;
          content?: string;
          category?: string;
          language?: string;
          variables?: any | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      blog_series: {
        Row: {
          id: string;
          project_id: string;
          name: string;
          description: string | null;
          summary: string | null;
          total_posts: number;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          name: string;
          description?: string | null;
          summary?: string | null;
          total_posts?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          name?: string;
          description?: string | null;
          summary?: string | null;
          total_posts?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      blog_posts: {
        Row: {
          id: string;
          project_id: string;
          author_id: string | null;
          series_id: string | null;
          prompt_id: string | null;
          title: string;
          content: string;
          excerpt: string | null;
          language: string;
          seo_title: string | null;
          seo_description: string | null;
          seo_keywords: string[] | null;
          category: string | null;
          tags: string[] | null;
          status: 'draft' | 'published' | 'scheduled' | 'archived';
          scheduled_at: string | null;
          published_at: string | null;
          created_at: string;
          updated_at: string;
          generation_params: any | null;
          series_context: string | null;
          word_count: number | null;
          reading_time: number | null;
        };
        Insert: {
          id?: string;
          project_id: string;
          author_id?: string | null;
          series_id?: string | null;
          prompt_id?: string | null;
          title: string;
          content: string;
          excerpt?: string | null;
          language?: string;
          seo_title?: string | null;
          seo_description?: string | null;
          seo_keywords?: string[] | null;
          category?: string | null;
          tags?: string[] | null;
          status?: 'draft' | 'published' | 'scheduled' | 'archived';
          scheduled_at?: string | null;
          published_at?: string | null;
          created_at?: string;
          updated_at?: string;
          generation_params?: any | null;
          series_context?: string | null;
          word_count?: number | null;
          reading_time?: number | null;
        };
        Update: {
          id?: string;
          project_id?: string;
          author_id?: string | null;
          series_id?: string | null;
          prompt_id?: string | null;
          title?: string;
          content?: string;
          excerpt?: string | null;
          language?: string;
          seo_title?: string | null;
          seo_description?: string | null;
          seo_keywords?: string[] | null;
          category?: string | null;
          tags?: string[] | null;
          status?: 'draft' | 'published' | 'scheduled' | 'archived';
          scheduled_at?: string | null;
          published_at?: string | null;
          created_at?: string;
          updated_at?: string;
          generation_params?: any | null;
          series_context?: string | null;
          word_count?: number | null;
          reading_time?: number | null;
        };
      };
      generation_history: {
        Row: {
          id: string;
          post_id: string;
          prompt_used: string;
          input_keywords: string[] | null;
          input_title: string | null;
          generation_params: any | null;
          ai_model: string | null;
          response_time: number | null;
          success: boolean;
          error_message: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          post_id: string;
          prompt_used: string;
          input_keywords?: string[] | null;
          input_title?: string | null;
          generation_params?: any | null;
          ai_model?: string | null;
          response_time?: number | null;
          success?: boolean;
          error_message?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          post_id?: string;
          prompt_used?: string;
          input_keywords?: string[] | null;
          input_title?: string | null;
          generation_params?: any | null;
          ai_model?: string | null;
          response_time?: number | null;
          success?: boolean;
          error_message?: string | null;
          created_at?: string;
        };
      };
      sync_logs: {
        Row: {
          id: string;
          source_project_id: string;
          target_project_id: string;
          entity_type: string;
          entity_id: string;
          sync_status: 'pending' | 'success' | 'failed';
          error_message: string | null;
          synced_at: string;
        };
        Insert: {
          id?: string;
          source_project_id: string;
          target_project_id: string;
          entity_type: string;
          entity_id: string;
          sync_status?: 'pending' | 'success' | 'failed';
          error_message?: string | null;
          synced_at?: string;
        };
        Update: {
          id?: string;
          source_project_id?: string;
          target_project_id?: string;
          entity_type?: string;
          entity_id?: string;
          sync_status?: 'pending' | 'success' | 'failed';
          error_message?: string | null;
          synced_at?: string;
        };
      };
    };
  };
}

// 类型定义导出
export type Project = Database['public']['Tables']['projects']['Row'];
export type Author = Database['public']['Tables']['authors']['Row'];
export type Prompt = Database['public']['Tables']['prompts']['Row'];
export type BlogSeries = Database['public']['Tables']['blog_series']['Row'];
export type BlogPost = Database['public']['Tables']['blog_posts']['Row'];
export type GenerationHistory = Database['public']['Tables']['generation_history']['Row'];
export type SyncLog = Database['public']['Tables']['sync_logs']['Row'];

// 插入类型
export type ProjectInsert = Database['public']['Tables']['projects']['Insert'];
export type AuthorInsert = Database['public']['Tables']['authors']['Insert'];
export type PromptInsert = Database['public']['Tables']['prompts']['Insert'];
export type BlogSeriesInsert = Database['public']['Tables']['blog_series']['Insert'];
export type BlogPostInsert = Database['public']['Tables']['blog_posts']['Insert'];
export type GenerationHistoryInsert = Database['public']['Tables']['generation_history']['Insert'];
export type SyncLogInsert = Database['public']['Tables']['sync_logs']['Insert'];

// 更新类型
export type ProjectUpdate = Database['public']['Tables']['projects']['Update'];
export type AuthorUpdate = Database['public']['Tables']['authors']['Update'];
export type PromptUpdate = Database['public']['Tables']['prompts']['Update'];
export type BlogSeriesUpdate = Database['public']['Tables']['blog_series']['Update'];
export type BlogPostUpdate = Database['public']['Tables']['blog_posts']['Update'];
export type GenerationHistoryUpdate = Database['public']['Tables']['generation_history']['Update'];
export type SyncLogUpdate = Database['public']['Tables']['sync_logs']['Update'];

// 工具函数
export const calculateReadingTime = (content: string): number => {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
};

export const generateExcerpt = (content: string, maxLength: number = 150): string => {
  const plainText = content.replace(/<[^>]*>/g, '').replace(/\n/g, ' ');
  if (plainText.length <= maxLength) return plainText;
  return plainText.substring(0, maxLength).trim() + '...';
};

export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};