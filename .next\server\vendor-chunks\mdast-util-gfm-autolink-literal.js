"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-autolink-literal";
exports.ids = ["vendor-chunks/mdast-util-gfm-autolink-literal"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-autolink-literal/lib/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-autolink-literal/lib/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmAutolinkLiteralFromMarkdown: () => (/* binding */ gfmAutolinkLiteralFromMarkdown),\n/* harmony export */   gfmAutolinkLiteralToMarkdown: () => (/* binding */ gfmAutolinkLiteralToMarkdown)\n/* harmony export */ });\n/* harmony import */ var ccount__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ccount */ \"(ssr)/./node_modules/ccount/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var mdast_util_find_and_replace__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-find-and-replace */ \"(ssr)/./node_modules/mdast-util-find-and-replace/lib/index.js\");\n/**\n * @import {RegExpMatchObject, ReplaceFunction} from 'mdast-util-find-and-replace'\n * @import {CompileContext, Extension as FromMarkdownExtension, Handle as FromMarkdownHandle, Transform as FromMarkdownTransform} from 'mdast-util-from-markdown'\n * @import {ConstructName, Options as ToMarkdownExtension} from 'mdast-util-to-markdown'\n * @import {Link, PhrasingContent} from 'mdast'\n */ \n\n\n\n/** @type {ConstructName} */ const inConstruct = \"phrasing\";\n/** @type {Array<ConstructName>} */ const notInConstruct = [\n    \"autolink\",\n    \"link\",\n    \"image\",\n    \"label\"\n];\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM autolink\n * literals in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM autolink literals.\n */ function gfmAutolinkLiteralFromMarkdown() {\n    return {\n        transforms: [\n            transformGfmAutolinkLiterals\n        ],\n        enter: {\n            literalAutolink: enterLiteralAutolink,\n            literalAutolinkEmail: enterLiteralAutolinkValue,\n            literalAutolinkHttp: enterLiteralAutolinkValue,\n            literalAutolinkWww: enterLiteralAutolinkValue\n        },\n        exit: {\n            literalAutolink: exitLiteralAutolink,\n            literalAutolinkEmail: exitLiteralAutolinkEmail,\n            literalAutolinkHttp: exitLiteralAutolinkHttp,\n            literalAutolinkWww: exitLiteralAutolinkWww\n        }\n    };\n}\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM autolink\n * literals in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM autolink literals.\n */ function gfmAutolinkLiteralToMarkdown() {\n    return {\n        unsafe: [\n            {\n                character: \"@\",\n                before: \"[+\\\\-.\\\\w]\",\n                after: \"[\\\\-.\\\\w]\",\n                inConstruct,\n                notInConstruct\n            },\n            {\n                character: \".\",\n                before: \"[Ww]\",\n                after: \"[\\\\-.\\\\w]\",\n                inConstruct,\n                notInConstruct\n            },\n            {\n                character: \":\",\n                before: \"[ps]\",\n                after: \"\\\\/\",\n                inConstruct,\n                notInConstruct\n            }\n        ]\n    };\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterLiteralAutolink(token) {\n    this.enter({\n        type: \"link\",\n        title: null,\n        url: \"\",\n        children: []\n    }, token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterLiteralAutolinkValue(token) {\n    this.config.enter.autolinkProtocol.call(this, token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitLiteralAutolinkHttp(token) {\n    this.config.exit.autolinkProtocol.call(this, token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitLiteralAutolinkWww(token) {\n    this.config.exit.data.call(this, token);\n    const node = this.stack[this.stack.length - 1];\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === \"link\");\n    node.url = \"http://\" + this.sliceSerialize(token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitLiteralAutolinkEmail(token) {\n    this.config.exit.autolinkEmail.call(this, token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitLiteralAutolink(token) {\n    this.exit(token);\n}\n/** @type {FromMarkdownTransform} */ function transformGfmAutolinkLiterals(tree) {\n    (0,mdast_util_find_and_replace__WEBPACK_IMPORTED_MODULE_1__.findAndReplace)(tree, [\n        [\n            /(https?:\\/\\/|www(?=\\.))([-.\\w]+)([^ \\t\\r\\n]*)/gi,\n            findUrl\n        ],\n        [\n            /(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)/gu,\n            findEmail\n        ]\n    ], {\n        ignore: [\n            \"link\",\n            \"linkReference\"\n        ]\n    });\n}\n/**\n * @type {ReplaceFunction}\n * @param {string} _\n * @param {string} protocol\n * @param {string} domain\n * @param {string} path\n * @param {RegExpMatchObject} match\n * @returns {Array<PhrasingContent> | Link | false}\n */ // eslint-disable-next-line max-params\nfunction findUrl(_, protocol, domain, path, match) {\n    let prefix = \"\";\n    // Not an expected previous character.\n    if (!previous(match)) {\n        return false;\n    }\n    // Treat `www` as part of the domain.\n    if (/^w/i.test(protocol)) {\n        domain = protocol + domain;\n        protocol = \"\";\n        prefix = \"http://\";\n    }\n    if (!isCorrectDomain(domain)) {\n        return false;\n    }\n    const parts = splitUrl(domain + path);\n    if (!parts[0]) return false;\n    /** @type {Link} */ const result = {\n        type: \"link\",\n        title: null,\n        url: prefix + protocol + parts[0],\n        children: [\n            {\n                type: \"text\",\n                value: protocol + parts[0]\n            }\n        ]\n    };\n    if (parts[1]) {\n        return [\n            result,\n            {\n                type: \"text\",\n                value: parts[1]\n            }\n        ];\n    }\n    return result;\n}\n/**\n * @type {ReplaceFunction}\n * @param {string} _\n * @param {string} atext\n * @param {string} label\n * @param {RegExpMatchObject} match\n * @returns {Link | false}\n */ function findEmail(_, atext, label, match) {\n    if (// Not an expected previous character.\n    !previous(match, true) || // Label ends in not allowed character.\n    /[-\\d_]$/.test(label)) {\n        return false;\n    }\n    return {\n        type: \"link\",\n        title: null,\n        url: \"mailto:\" + atext + \"@\" + label,\n        children: [\n            {\n                type: \"text\",\n                value: atext + \"@\" + label\n            }\n        ]\n    };\n}\n/**\n * @param {string} domain\n * @returns {boolean}\n */ function isCorrectDomain(domain) {\n    const parts = domain.split(\".\");\n    if (parts.length < 2 || parts[parts.length - 1] && (/_/.test(parts[parts.length - 1]) || !/[a-zA-Z\\d]/.test(parts[parts.length - 1])) || parts[parts.length - 2] && (/_/.test(parts[parts.length - 2]) || !/[a-zA-Z\\d]/.test(parts[parts.length - 2]))) {\n        return false;\n    }\n    return true;\n}\n/**\n * @param {string} url\n * @returns {[string, string | undefined]}\n */ function splitUrl(url) {\n    const trailExec = /[!\"&'),.:;<>?\\]}]+$/.exec(url);\n    if (!trailExec) {\n        return [\n            url,\n            undefined\n        ];\n    }\n    url = url.slice(0, trailExec.index);\n    let trail = trailExec[0];\n    let closingParenIndex = trail.indexOf(\")\");\n    const openingParens = (0,ccount__WEBPACK_IMPORTED_MODULE_2__.ccount)(url, \"(\");\n    let closingParens = (0,ccount__WEBPACK_IMPORTED_MODULE_2__.ccount)(url, \")\");\n    while(closingParenIndex !== -1 && openingParens > closingParens){\n        url += trail.slice(0, closingParenIndex + 1);\n        trail = trail.slice(closingParenIndex + 1);\n        closingParenIndex = trail.indexOf(\")\");\n        closingParens++;\n    }\n    return [\n        url,\n        trail\n    ];\n}\n/**\n * @param {RegExpMatchObject} match\n * @param {boolean | null | undefined} [email=false]\n * @returns {boolean}\n */ function previous(match, email) {\n    const code = match.input.charCodeAt(match.index - 1);\n    return (match.index === 0 || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.unicodeWhitespace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.unicodePunctuation)(code)) && // If it’s an email, the previous character should not be a slash.\n    (!email || code !== 47);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-autolink-literal/lib/index.js\n");

/***/ })

};
;