import { 
  supabase,
  calculateReadingTime,
  generateExcerpt
} from './supabase';
import type { 
  Project, 
  Author, 
  Prompt, 
  BlogSeries, 
  BlogPost, 
  GenerationHistory,
  SyncLog,
  ProjectInsert,
  AuthorInsert,
  PromptInsert,
  BlogSeriesInsert,
  BlogPostInsert,
  GenerationHistoryInsert,
  SyncLogInsert,
  ProjectUpdate,
  AuthorUpdate,
  PromptUpdate,
  BlogSeriesUpdate,
  BlogPostUpdate
} from './supabase';

// 项目相关操作
export const projectService = {
  // 获取所有项目
  async getAll(): Promise<Project[]> {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw new Error(`获取项目列表失败: ${error.message}`);
    return data || [];
  },

  // 根据ID获取项目
  async getById(id: string): Promise<Project | null> {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // 未找到记录
      throw new Error(`获取项目失败: ${error.message}`);
    }
    return data;
  },

  // 创建项目
  async create(project: ProjectInsert): Promise<Project> {
    const { data, error } = await supabase
      .from('projects')
      .insert(project)
      .select()
      .single();
    
    if (error) throw new Error(`创建项目失败: ${error.message}`);
    return data;
  },

  // 更新项目
  async update(id: string, updates: ProjectUpdate): Promise<Project> {
    const { data, error } = await supabase
      .from('projects')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw new Error(`更新项目失败: ${error.message}`);
    return data;
  },

  // 删除项目
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', id);
    
    if (error) throw new Error(`删除项目失败: ${error.message}`);
  }
};

// 作者相关操作
export const authorService = {
  // 获取项目下的所有作者
  async getByProject(projectId: string): Promise<Author[]> {
    const { data, error } = await supabase
      .from('authors')
      .select('*')
      .eq('project_id', projectId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    
    if (error) throw new Error(`获取作者列表失败: ${error.message}`);
    return data || [];
  },

  // 根据ID获取作者
  async getById(id: string): Promise<Author | null> {
    const { data, error } = await supabase
      .from('authors')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw new Error(`获取作者信息失败: ${error.message}`);
    }
    return data;
  },

  // 创建作者
  async create(author: AuthorInsert): Promise<Author> {
    const { data, error } = await supabase
      .from('authors')
      .insert(author)
      .select()
      .single();
    
    if (error) throw new Error(`创建作者失败: ${error.message}`);
    return data;
  },

  // 更新作者
  async update(id: string, updates: AuthorUpdate): Promise<Author> {
    const { data, error } = await supabase
      .from('authors')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw new Error(`更新作者信息失败: ${error.message}`);
    return data;
  },

  // 删除作者（软删除）
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('authors')
      .update({ is_active: false })
      .eq('id', id);
    
    if (error) throw new Error(`删除作者失败: ${error.message}`);
  }
};

// 提示词相关操作
export const promptService = {
  // 获取项目下的所有提示词
  async getByProject(projectId: string): Promise<Prompt[]> {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('project_id', projectId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    
    if (error) throw new Error(`获取提示词列表失败: ${error.message}`);
    return data || [];
  },

  // 根据ID获取提示词
  async getById(id: string): Promise<Prompt | null> {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw new Error(`获取提示词失败: ${error.message}`);
    }
    return data;
  },

  // 根据类别获取提示词
  async getByCategory(projectId: string, category: string): Promise<Prompt[]> {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('project_id', projectId)
      .eq('category', category)
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    
    if (error) throw new Error(`获取提示词失败: ${error.message}`);
    return data || [];
  },

  // 创建提示词
  async create(prompt: PromptInsert): Promise<Prompt> {
    const { data, error } = await supabase
      .from('prompts')
      .insert(prompt)
      .select()
      .single();
    
    if (error) throw new Error(`创建提示词失败: ${error.message}`);
    return data;
  },

  // 更新提示词
  async update(id: string, updates: PromptUpdate): Promise<Prompt> {
    const { data, error } = await supabase
      .from('prompts')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw new Error(`更新提示词失败: ${error.message}`);
    return data;
  },

  // 删除提示词（软删除）
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('prompts')
      .update({ is_active: false })
      .eq('id', id);
    
    if (error) throw new Error(`删除提示词失败: ${error.message}`);
  }
};

// 博文系列相关操作
export const blogSeriesService = {
  // 获取项目下的所有系列
  async getByProject(projectId: string): Promise<BlogSeries[]> {
    const { data, error } = await supabase
      .from('blog_series')
      .select('*')
      .eq('project_id', projectId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    
    if (error) throw new Error(`获取博文系列失败: ${error.message}`);
    return data || [];
  },

  // 根据ID获取系列
  async getById(id: string): Promise<BlogSeries | null> {
    const { data, error } = await supabase
      .from('blog_series')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw new Error(`获取博文系列失败: ${error.message}`);
    }
    return data;
  },

  // 创建系列
  async create(series: BlogSeriesInsert): Promise<BlogSeries> {
    const { data, error } = await supabase
      .from('blog_series')
      .insert(series)
      .select()
      .single();
    
    if (error) throw new Error(`创建博文系列失败: ${error.message}`);
    return data;
  },

  // 更新系列
  async update(id: string, updates: BlogSeriesUpdate): Promise<BlogSeries> {
    const { data, error } = await supabase
      .from('blog_series')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw new Error(`更新博文系列失败: ${error.message}`);
    return data;
  },

  // 更新系列总结
  async updateSummary(id: string, summary: string): Promise<BlogSeries> {
    return this.update(id, { summary });
  },

  // 获取系列的历史总结（用于生成上下文）
  async getSeriesContext(seriesId: string): Promise<string> {
    const series = await this.getById(seriesId);
    if (!series) return '';

    // 获取系列内的所有已发布博文
    const { data: posts, error } = await supabase
      .from('blog_posts')
      .select('title, excerpt, seo_keywords, created_at')
      .eq('series_id', seriesId)
      .eq('status', 'published')
      .order('created_at', { ascending: true });

    if (error) {
      console.error('获取系列博文失败:', error);
      return series.summary || '';
    }

    let context = `系列：${series.name}\n`;
    if (series.description) {
      context += `系列描述：${series.description}\n`;
    }
    if (series.summary) {
      context += `系列总结：${series.summary}\n`;
    }
    
    if (posts && posts.length > 0) {
      context += `\n已发布的博文：\n`;
      posts.forEach((post: any, index: number) => {
        context += `${index + 1}. ${post.title}`;
        if (post.excerpt) {
          context += ` - ${post.excerpt}`;
        }
        if (post.seo_keywords && post.seo_keywords.length > 0) {
          context += ` (关键词: ${post.seo_keywords.join(', ')})`;
        }
        context += '\n';
      });
    }

    return context;
  },

  // 删除系列（软删除）
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('blog_series')
      .update({ is_active: false })
      .eq('id', id);
    
    if (error) throw new Error(`删除博文系列失败: ${error.message}`);
  }
};

// 博文相关操作
export const blogPostService = {
  // 获取项目下的所有博文
  async getByProject(projectId: string, status?: string): Promise<BlogPost[]> {
    let query = supabase
      .from('blog_posts')
      .select(`
        *,
        authors:author_id(name, email),
        blog_series:series_id(name),
        prompts:prompt_id(name)
      `)
      .eq('project_id', projectId)
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;
    
    if (error) throw new Error(`获取博文列表失败: ${error.message}`);
    return data || [];
  },

  // 根据ID获取博文
  async getById(id: string): Promise<BlogPost | null> {
    const { data, error } = await supabase
      .from('blog_posts')
      .select(`
        *,
        authors:author_id(name, email, bio),
        blog_series:series_id(name, description),
        prompts:prompt_id(name, content)
      `)
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw new Error(`获取博文失败: ${error.message}`);
    }
    return data;
  },

  // 根据系列获取博文
  async getBySeries(seriesId: string): Promise<BlogPost[]> {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('series_id', seriesId)
      .order('created_at', { ascending: true });
    
    if (error) throw new Error(`获取系列博文失败: ${error.message}`);
    return data || [];
  },

  // 创建博文
  async create(post: BlogPostInsert): Promise<BlogPost> {
    // 自动计算字数和阅读时间
    const wordCount = post.content.split(/\s+/).length;
    const readingTime = Math.ceil(wordCount / 200); // 假设每分钟200字
    
    // 自动生成摘要
    const excerpt = post.excerpt || generateExcerpt(post.content);

    const enrichedPost = {
      ...post,
      word_count: wordCount,
      reading_time: readingTime,
      excerpt
    };

    const { data, error } = await supabase
      .from('blog_posts')
      .insert(enrichedPost)
      .select()
      .single();
    
    if (error) throw new Error(`创建博文失败: ${error.message}`);
    return data;
  },

  // 更新博文
  async update(id: string, updates: BlogPostUpdate): Promise<BlogPost> {
    // 如果更新了内容，重新计算字数和阅读时间
    if (updates.content) {
      const wordCount = updates.content.split(/\s+/).length;
      updates.word_count = wordCount;
      updates.reading_time = Math.ceil(wordCount / 200);
      
      if (!updates.excerpt) {
        updates.excerpt = generateExcerpt(updates.content);
      }
    }

    const { data, error } = await supabase
      .from('blog_posts')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw new Error(`更新博文失败: ${error.message}`);
    return data;
  },

  // 发布博文
  async publish(id: string): Promise<BlogPost> {
    return this.update(id, { status: 'published' });
  },

  // 归档博文
  async archive(id: string): Promise<BlogPost> {
    return this.update(id, { status: 'archived' });
  },

  // 删除博文
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('blog_posts')
      .delete()
      .eq('id', id);
    
    if (error) throw new Error(`删除博文失败: ${error.message}`);
  },

  // 搜索博文
  async search(projectId: string, keyword: string, filters?: {
    status?: string;
    authorId?: string;
    seriesId?: string;
    tags?: string[];
  }): Promise<BlogPost[]> {
    let query = supabase
      .from('blog_posts')
      .select(`
        *,
        authors:author_id(name),
        blog_series:series_id(name)
      `)
      .eq('project_id', projectId);

    // 关键词搜索（标题、内容、SEO关键词）
    if (keyword) {
      query = query.or(`title.ilike.%${keyword}%,content.ilike.%${keyword}%,seo_keywords.cs.{${keyword}}`);
    }

    // 状态过滤
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    // 作者过滤
    if (filters?.authorId) {
      query = query.eq('author_id', filters.authorId);
    }

    // 系列过滤
    if (filters?.seriesId) {
      query = query.eq('series_id', filters.seriesId);
    }

    // 标签过滤
    if (filters?.tags && filters.tags.length > 0) {
      query = query.overlaps('tags', filters.tags);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;
    
    if (error) throw new Error(`搜索博文失败: ${error.message}`);
    return data || [];
  }
};

// 生成历史相关操作
export const generationHistoryService = {
  // 记录生成历史
  async create(history: GenerationHistoryInsert): Promise<GenerationHistory> {
    const { data, error } = await supabase
      .from('generation_history')
      .insert(history)
      .select()
      .single();
    
    if (error) throw new Error(`记录生成历史失败: ${error.message}`);
    return data;
  },

  // 获取博文的生成历史
  async getByPost(postId: string): Promise<GenerationHistory[]> {
    const { data, error } = await supabase
      .from('generation_history')
      .select('*')
      .eq('post_id', postId)
      .order('created_at', { ascending: false });
    
    if (error) throw new Error(`获取生成历史失败: ${error.message}`);
    return data || [];
  }
};

// 项目同步相关操作
export const syncService = {
  // 同步博文到其他项目
  async syncBlogPost(
    sourceProjectId: string, 
    targetProjectId: string, 
    postId: string
  ): Promise<void> {
    try {
      // 获取源博文
      const sourcePost = await blogPostService.getById(postId);
      if (!sourcePost) {
        throw new Error('源博文不存在');
      }

      // 获取目标项目信息
      const targetProject = await projectService.getById(targetProjectId);
      if (!targetProject) {
        throw new Error('目标项目不存在');
      }

      // 创建同步记录
      const syncLog: SyncLogInsert = {
        source_project_id: sourceProjectId,
        target_project_id: targetProjectId,
        entity_type: 'blog_post',
        entity_id: postId,
        sync_status: 'pending'
      };

      const { data: logData } = await supabase
        .from('sync_logs')
        .insert(syncLog)
        .select()
        .single();

      try {
        // 如果有外部数据库URL，这里应该连接到外部数据库
        // 目前简化为在同一数据库中创建副本
        const newPost: BlogPostInsert = {
          project_id: targetProjectId,
          title: sourcePost.title,
          content: sourcePost.content,
          excerpt: sourcePost.excerpt,
          language: sourcePost.language,
          seo_title: sourcePost.seo_title,
          seo_description: sourcePost.seo_description,
          seo_keywords: sourcePost.seo_keywords,
          category: sourcePost.category,
          tags: sourcePost.tags,
          status: 'draft', // 同步的博文默认为草稿状态
          generation_params: sourcePost.generation_params,
          word_count: sourcePost.word_count,
          reading_time: sourcePost.reading_time
        };

        await blogPostService.create(newPost);

        // 更新同步状态为成功
        if (logData) {
          await supabase
            .from('sync_logs')
            .update({ sync_status: 'success' })
            .eq('id', logData.id);
        }

      } catch (syncError) {
        // 更新同步状态为失败
        if (logData) {
          await supabase
            .from('sync_logs')
            .update({ 
              sync_status: 'failed',
              error_message: syncError instanceof Error ? syncError.message : '同步失败'
            })
            .eq('id', logData.id);
        }
        throw syncError;
      }

    } catch (error) {
      throw new Error(`同步博文失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  },

  // 同步作者到其他项目
  async syncAuthor(
    sourceProjectId: string, 
    targetProjectId: string, 
    authorId: string
  ): Promise<void> {
    try {
      const sourceAuthor = await authorService.getById(authorId);
      if (!sourceAuthor) {
        throw new Error('源作者不存在');
      }

      const newAuthor: AuthorInsert = {
        project_id: targetProjectId,
        name: sourceAuthor.name,
        email: sourceAuthor.email,
        bio: sourceAuthor.bio,
        avatar_url: sourceAuthor.avatar_url
      };

      await authorService.create(newAuthor);

      // 记录同步日志
      await supabase
        .from('sync_logs')
        .insert({
          source_project_id: sourceProjectId,
          target_project_id: targetProjectId,
          entity_type: 'author',
          entity_id: authorId,
          sync_status: 'success'
        });

    } catch (error) {
      await supabase
        .from('sync_logs')
        .insert({
          source_project_id: sourceProjectId,
          target_project_id: targetProjectId,
          entity_type: 'author',
          entity_id: authorId,
          sync_status: 'failed',
          error_message: error instanceof Error ? error.message : '同步失败'
        });
      
      throw new Error(`同步作者失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  },

  // 获取同步日志
  async getSyncLogs(projectId: string): Promise<SyncLog[]> {
    const { data, error } = await supabase
      .from('sync_logs')
      .select('*')
      .or(`source_project_id.eq.${projectId},target_project_id.eq.${projectId}`)
      .order('synced_at', { ascending: false });
    
    if (error) throw new Error(`获取同步日志失败: ${error.message}`);
    return data || [];
  }
};