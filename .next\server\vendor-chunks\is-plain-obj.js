"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/is-plain-obj";
exports.ids = ["vendor-chunks/is-plain-obj"];
exports.modules = {

/***/ "(ssr)/./node_modules/is-plain-obj/index.js":
/*!********************************************!*\
  !*** ./node_modules/is-plain-obj/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isPlainObject)\n/* harmony export */ });\nfunction isPlainObject(value) {\n    if (typeof value !== \"object\" || value === null) {\n        return false;\n    }\n    const prototype = Object.getPrototypeOf(value);\n    return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in value) && !(Symbol.iterator in value);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaXMtcGxhaW4tb2JqL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxjQUFjQyxLQUFLO0lBQzFDLElBQUksT0FBT0EsVUFBVSxZQUFZQSxVQUFVLE1BQU07UUFDaEQsT0FBTztJQUNSO0lBRUEsTUFBTUMsWUFBWUMsT0FBT0MsY0FBYyxDQUFDSDtJQUN4QyxPQUFPLENBQUNDLGNBQWMsUUFBUUEsY0FBY0MsT0FBT0QsU0FBUyxJQUFJQyxPQUFPQyxjQUFjLENBQUNGLGVBQWUsSUFBRyxLQUFNLENBQUVHLENBQUFBLE9BQU9DLFdBQVcsSUFBSUwsS0FBSSxLQUFNLENBQUVJLENBQUFBLE9BQU9FLFFBQVEsSUFBSU4sS0FBSTtBQUMxSyIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2lzLXBsYWluLW9iai9pbmRleC5qcz8wZDk2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGlzUGxhaW5PYmplY3QodmFsdWUpIHtcblx0aWYgKHR5cGVvZiB2YWx1ZSAhPT0gJ29iamVjdCcgfHwgdmFsdWUgPT09IG51bGwpIHtcblx0XHRyZXR1cm4gZmFsc2U7XG5cdH1cblxuXHRjb25zdCBwcm90b3R5cGUgPSBPYmplY3QuZ2V0UHJvdG90eXBlT2YodmFsdWUpO1xuXHRyZXR1cm4gKHByb3RvdHlwZSA9PT0gbnVsbCB8fCBwcm90b3R5cGUgPT09IE9iamVjdC5wcm90b3R5cGUgfHwgT2JqZWN0LmdldFByb3RvdHlwZU9mKHByb3RvdHlwZSkgPT09IG51bGwpICYmICEoU3ltYm9sLnRvU3RyaW5nVGFnIGluIHZhbHVlKSAmJiAhKFN5bWJvbC5pdGVyYXRvciBpbiB2YWx1ZSk7XG59XG4iXSwibmFtZXMiOlsiaXNQbGFpbk9iamVjdCIsInZhbHVlIiwicHJvdG90eXBlIiwiT2JqZWN0IiwiZ2V0UHJvdG90eXBlT2YiLCJTeW1ib2wiLCJ0b1N0cmluZ1RhZyIsIml0ZXJhdG9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/is-plain-obj/index.js\n");

/***/ })

};
;