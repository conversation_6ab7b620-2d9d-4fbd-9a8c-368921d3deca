"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-footnote";
exports.ids = ["vendor-chunks/mdast-util-gfm-footnote"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-footnote/lib/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/mdast-util-gfm-footnote/lib/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmFootnoteFromMarkdown: () => (/* binding */ gfmFootnoteFromMarkdown),\n/* harmony export */   gfmFootnoteToMarkdown: () => (/* binding */ gfmFootnoteToMarkdown)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/**\n * @import {\n *   CompileContext,\n *   Extension as FromMarkdownExtension,\n *   Handle as FromMarkdownHandle\n * } from 'mdast-util-from-markdown'\n * @import {ToMarkdownOptions} from 'mdast-util-gfm-footnote'\n * @import {\n *   Handle as ToMarkdownHandle,\n *   Map,\n *   Options as ToMarkdownExtension\n * } from 'mdast-util-to-markdown'\n * @import {FootnoteDefinition, FootnoteReference} from 'mdast'\n */ \n\nfootnoteReference.peek = footnoteReferencePeek;\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterFootnoteCallString() {\n    this.buffer();\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterFootnoteCall(token) {\n    this.enter({\n        type: \"footnoteReference\",\n        identifier: \"\",\n        label: \"\"\n    }, token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterFootnoteDefinitionLabelString() {\n    this.buffer();\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterFootnoteDefinition(token) {\n    this.enter({\n        type: \"footnoteDefinition\",\n        identifier: \"\",\n        label: \"\",\n        children: []\n    }, token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitFootnoteCallString(token) {\n    const label = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === \"footnoteReference\");\n    node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token)).toLowerCase();\n    node.label = label;\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitFootnoteCall(token) {\n    this.exit(token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitFootnoteDefinitionLabelString(token) {\n    const label = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === \"footnoteDefinition\");\n    node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token)).toLowerCase();\n    node.label = label;\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitFootnoteDefinition(token) {\n    this.exit(token);\n}\n/** @type {ToMarkdownHandle} */ function footnoteReferencePeek() {\n    return \"[\";\n}\n/**\n * @type {ToMarkdownHandle}\n * @param {FootnoteReference} node\n */ function footnoteReference(node, _, state, info) {\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"[^\");\n    const exit = state.enter(\"footnoteReference\");\n    const subexit = state.enter(\"reference\");\n    value += tracker.move(state.safe(state.associationId(node), {\n        after: \"]\",\n        before: value\n    }));\n    subexit();\n    exit();\n    value += tracker.move(\"]\");\n    return value;\n}\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown`.\n */ function gfmFootnoteFromMarkdown() {\n    return {\n        enter: {\n            gfmFootnoteCallString: enterFootnoteCallString,\n            gfmFootnoteCall: enterFootnoteCall,\n            gfmFootnoteDefinitionLabelString: enterFootnoteDefinitionLabelString,\n            gfmFootnoteDefinition: enterFootnoteDefinition\n        },\n        exit: {\n            gfmFootnoteCallString: exitFootnoteCallString,\n            gfmFootnoteCall: exitFootnoteCall,\n            gfmFootnoteDefinitionLabelString: exitFootnoteDefinitionLabelString,\n            gfmFootnoteDefinition: exitFootnoteDefinition\n        }\n    };\n}\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @param {ToMarkdownOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown`.\n */ function gfmFootnoteToMarkdown(options) {\n    // To do: next major: change default.\n    let firstLineBlank = false;\n    if (options && options.firstLineBlank) {\n        firstLineBlank = true;\n    }\n    return {\n        handlers: {\n            footnoteDefinition,\n            footnoteReference\n        },\n        // This is on by default already.\n        unsafe: [\n            {\n                character: \"[\",\n                inConstruct: [\n                    \"label\",\n                    \"phrasing\",\n                    \"reference\"\n                ]\n            }\n        ]\n    };\n    /**\n   * @type {ToMarkdownHandle}\n   * @param {FootnoteDefinition} node\n   */ function footnoteDefinition(node, _, state, info) {\n        const tracker = state.createTracker(info);\n        let value = tracker.move(\"[^\");\n        const exit = state.enter(\"footnoteDefinition\");\n        const subexit = state.enter(\"label\");\n        value += tracker.move(state.safe(state.associationId(node), {\n            before: value,\n            after: \"]\"\n        }));\n        subexit();\n        value += tracker.move(\"]:\");\n        if (node.children && node.children.length > 0) {\n            tracker.shift(4);\n            value += tracker.move((firstLineBlank ? \"\\n\" : \" \") + state.indentLines(state.containerFlow(node, tracker.current()), firstLineBlank ? mapAll : mapExceptFirst));\n        }\n        exit();\n        return value;\n    }\n}\n/** @type {Map} */ function mapExceptFirst(line, index, blank) {\n    return index === 0 ? line : mapAll(line, index, blank);\n}\n/** @type {Map} */ function mapAll(line, index, blank) {\n    return (blank ? \"\" : \"    \") + line;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-footnote/lib/index.js\n");

/***/ })

};
;