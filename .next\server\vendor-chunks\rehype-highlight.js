"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-highlight";
exports.ids = ["vendor-chunks/rehype-highlight"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-highlight/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/rehype-highlight/lib/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeHighlight)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var lowlight__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lowlight */ \"(ssr)/./node_modules/lowlight/lib/common.js\");\n/* harmony import */ var lowlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lowlight */ \"(ssr)/./node_modules/lowlight/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/**\n * @import {ElementContent, Element, Root} from 'hast'\n * @import {LanguageFn} from 'lowlight'\n * @import {VFile} from 'vfile'\n */ /**\n * @typedef Options\n *   Configuration (optional).\n * @property {Readonly<Record<string, ReadonlyArray<string> | string>> | null | undefined} [aliases={}]\n *   Register more aliases (optional);\n *   passed to `lowlight.registerAlias`.\n * @property {boolean | null | undefined} [detect=false]\n *   Highlight code without language classes by guessing its programming\n *   language (default: `false`).\n * @property {Readonly<Record<string, LanguageFn>> | null | undefined} [languages]\n *   Register languages (default: `common`);\n *   passed to `lowlight.register`.\n * @property {ReadonlyArray<string> | null | undefined} [plainText=[]]\n *   List of language names to not highlight (optional);\n *   note you can also add `no-highlight` classes.\n * @property {string | null | undefined} [prefix='hljs-']\n *   Class prefix (default: `'hljs-'`).\n * @property {ReadonlyArray<string> | null | undefined} [subset]\n *   Names of languages to check when detecting (default: all registered\n *   languages).\n */ \n\n\n/** @type {Options} */ const emptyOptions = {};\n/**\n * Apply syntax highlighting.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */ function rehypeHighlight(options) {\n    const settings = options || emptyOptions;\n    const aliases = settings.aliases;\n    const detect = settings.detect || false;\n    const languages = settings.languages || lowlight__WEBPACK_IMPORTED_MODULE_0__.grammars;\n    const plainText = settings.plainText;\n    const prefix = settings.prefix;\n    const subset = settings.subset;\n    let name = \"hljs\";\n    const lowlight = (0,lowlight__WEBPACK_IMPORTED_MODULE_1__.createLowlight)(languages);\n    if (aliases) {\n        lowlight.registerAlias(aliases);\n    }\n    if (prefix) {\n        const pos = prefix.indexOf(\"-\");\n        name = pos === -1 ? prefix : prefix.slice(0, pos);\n    }\n    /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @param {VFile} file\n   *   File.\n   * @returns {undefined}\n   *   Nothing.\n   */ return function(tree, file) {\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)(tree, \"element\", function(node, _, parent) {\n            if (node.tagName !== \"code\" || !parent || parent.type !== \"element\" || parent.tagName !== \"pre\") {\n                return;\n            }\n            const lang = language(node);\n            if (lang === false || !lang && !detect || lang && plainText && plainText.includes(lang)) {\n                return;\n            }\n            if (!Array.isArray(node.properties.className)) {\n                node.properties.className = [];\n            }\n            if (!node.properties.className.includes(name)) {\n                node.properties.className.unshift(name);\n            }\n            const text = (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(node, {\n                whitespace: \"pre\"\n            });\n            /** @type {Root} */ let result;\n            try {\n                result = lang ? lowlight.highlight(lang, text, {\n                    prefix\n                }) : lowlight.highlightAuto(text, {\n                    prefix,\n                    subset\n                });\n            } catch (error) {\n                const cause = /** @type {Error} */ error;\n                if (lang && /Unknown language/.test(cause.message)) {\n                    file.message(\"Cannot highlight as `\" + lang + \"`, it’s not registered\", {\n                        ancestors: [\n                            parent,\n                            node\n                        ],\n                        cause,\n                        place: node.position,\n                        ruleId: \"missing-language\",\n                        source: \"rehype-highlight\"\n                    });\n                    /* c8 ignore next 5 -- throw arbitrary hljs errors */ return;\n                }\n                throw cause;\n            }\n            if (!lang && result.data && result.data.language) {\n                node.properties.className.push(\"language-\" + result.data.language);\n            }\n            if (result.children.length > 0) {\n                node.children = /** @type {Array<ElementContent>} */ result.children;\n            }\n        });\n    };\n}\n/**\n * Get the programming language of `node`.\n *\n * @param {Element} node\n *   Node.\n * @returns {false | string | undefined}\n *   Language or `undefined`, or `false` when an explikcit `no-highlight` class\n *   is used.\n */ function language(node) {\n    const list = node.properties.className;\n    let index = -1;\n    if (!Array.isArray(list)) {\n        return;\n    }\n    /** @type {string | undefined} */ let name;\n    while(++index < list.length){\n        const value = String(list[index]);\n        if (value === \"no-highlight\" || value === \"nohighlight\") {\n            return false;\n        }\n        if (!name && value.slice(0, 5) === \"lang-\") {\n            name = value.slice(5);\n        }\n        if (!name && value.slice(0, 9) === \"language-\") {\n            name = value.slice(9);\n        }\n    }\n    return name;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-highlight/lib/index.js\n");

/***/ })

};
;