"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-task-list-item";
exports.ids = ["vendor-chunks/mdast-util-gfm-task-list-item"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-task-list-item/lib/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-task-list-item/lib/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItemFromMarkdown: () => (/* binding */ gfmTaskListItemFromMarkdown),\n/* harmony export */   gfmTaskListItemToMarkdown: () => (/* binding */ gfmTaskListItemToMarkdown)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var mdast_util_to_markdown__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-to-markdown */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\");\n/**\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n */ \n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM task\n * list items in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown` to enable GFM task list items.\n */ function gfmTaskListItemFromMarkdown() {\n    return {\n        exit: {\n            taskListCheckValueChecked: exitCheck,\n            taskListCheckValueUnchecked: exitCheck,\n            paragraph: exitParagraphWithTaskListItem\n        }\n    };\n}\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM task list\n * items in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM task list items.\n */ function gfmTaskListItemToMarkdown() {\n    return {\n        unsafe: [\n            {\n                atBreak: true,\n                character: \"-\",\n                after: \"[:|-]\"\n            }\n        ],\n        handlers: {\n            listItem: listItemWithTaskListItem\n        }\n    };\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitCheck(token) {\n    // We’re always in a paragraph, in a list item.\n    const node = this.stack[this.stack.length - 2];\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === \"listItem\");\n    node.checked = token.type === \"taskListCheckValueChecked\";\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitParagraphWithTaskListItem(token) {\n    const parent = this.stack[this.stack.length - 2];\n    if (parent && parent.type === \"listItem\" && typeof parent.checked === \"boolean\") {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === \"paragraph\");\n        const head = node.children[0];\n        if (head && head.type === \"text\") {\n            const siblings = parent.children;\n            let index = -1;\n            /** @type {Paragraph | undefined} */ let firstParaghraph;\n            while(++index < siblings.length){\n                const sibling = siblings[index];\n                if (sibling.type === \"paragraph\") {\n                    firstParaghraph = sibling;\n                    break;\n                }\n            }\n            if (firstParaghraph === node) {\n                // Must start with a space or a tab.\n                head.value = head.value.slice(1);\n                if (head.value.length === 0) {\n                    node.children.shift();\n                } else if (node.position && head.position && typeof head.position.start.offset === \"number\") {\n                    head.position.start.column++;\n                    head.position.start.offset++;\n                    node.position.start = Object.assign({}, head.position.start);\n                }\n            }\n        }\n    }\n    this.exit(token);\n}\n/**\n * @type {ToMarkdownHandle}\n * @param {ListItem} node\n */ function listItemWithTaskListItem(node, parent, state, info) {\n    const head = node.children[0];\n    const checkable = typeof node.checked === \"boolean\" && head && head.type === \"paragraph\";\n    const checkbox = \"[\" + (node.checked ? \"x\" : \" \") + \"] \";\n    const tracker = state.createTracker(info);\n    if (checkable) {\n        tracker.move(checkbox);\n    }\n    let value = mdast_util_to_markdown__WEBPACK_IMPORTED_MODULE_1__.handle.listItem(node, parent, state, {\n        ...info,\n        ...tracker.current()\n    });\n    if (checkable) {\n        value = value.replace(/^(?:[*+-]|\\d+\\.)([\\r\\n]| {1,3})/, check);\n    }\n    return value;\n    /**\n   * @param {string} $0\n   * @returns {string}\n   */ function check($0) {\n        return $0 + checkbox;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-task-list-item/lib/index.js\n");

/***/ })

};
;