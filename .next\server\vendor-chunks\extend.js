"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/extend";
exports.ids = ["vendor-chunks/extend"];
exports.modules = {

/***/ "(ssr)/./node_modules/extend/index.js":
/*!**************************************!*\
  !*** ./node_modules/extend/index.js ***!
  \**************************************/
/***/ ((module) => {

eval("\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\nvar defineProperty = Object.defineProperty;\nvar gOPD = Object.getOwnPropertyDescriptor;\nvar isArray = function isArray(arr) {\n    if (typeof Array.isArray === \"function\") {\n        return Array.isArray(arr);\n    }\n    return toStr.call(arr) === \"[object Array]\";\n};\nvar isPlainObject = function isPlainObject(obj) {\n    if (!obj || toStr.call(obj) !== \"[object Object]\") {\n        return false;\n    }\n    var hasOwnConstructor = hasOwn.call(obj, \"constructor\");\n    var hasIsPrototypeOf = obj.constructor && obj.constructor.prototype && hasOwn.call(obj.constructor.prototype, \"isPrototypeOf\");\n    // Not own constructor property must be Object\n    if (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n        return false;\n    }\n    // Own properties are enumerated firstly, so to speed up,\n    // if last one is own, then all properties are own.\n    var key;\n    for(key in obj){}\n    return typeof key === \"undefined\" || hasOwn.call(obj, key);\n};\n// If name is '__proto__', and Object.defineProperty is available, define __proto__ as an own property on target\nvar setProperty = function setProperty(target, options) {\n    if (defineProperty && options.name === \"__proto__\") {\n        defineProperty(target, options.name, {\n            enumerable: true,\n            configurable: true,\n            value: options.newValue,\n            writable: true\n        });\n    } else {\n        target[options.name] = options.newValue;\n    }\n};\n// Return undefined instead of __proto__ if '__proto__' is not an own property\nvar getProperty = function getProperty(obj, name) {\n    if (name === \"__proto__\") {\n        if (!hasOwn.call(obj, name)) {\n            return void 0;\n        } else if (gOPD) {\n            // In early versions of node, obj['__proto__'] is buggy when obj has\n            // __proto__ as an own property. Object.getOwnPropertyDescriptor() works.\n            return gOPD(obj, name).value;\n        }\n    }\n    return obj[name];\n};\nmodule.exports = function extend() {\n    var options, name, src, copy, copyIsArray, clone;\n    var target = arguments[0];\n    var i = 1;\n    var length = arguments.length;\n    var deep = false;\n    // Handle a deep copy situation\n    if (typeof target === \"boolean\") {\n        deep = target;\n        target = arguments[1] || {};\n        // skip the boolean and the target\n        i = 2;\n    }\n    if (target == null || typeof target !== \"object\" && typeof target !== \"function\") {\n        target = {};\n    }\n    for(; i < length; ++i){\n        options = arguments[i];\n        // Only deal with non-null/undefined values\n        if (options != null) {\n            // Extend the base object\n            for(name in options){\n                src = getProperty(target, name);\n                copy = getProperty(options, name);\n                // Prevent never-ending loop\n                if (target !== copy) {\n                    // Recurse if we're merging plain objects or arrays\n                    if (deep && copy && (isPlainObject(copy) || (copyIsArray = isArray(copy)))) {\n                        if (copyIsArray) {\n                            copyIsArray = false;\n                            clone = src && isArray(src) ? src : [];\n                        } else {\n                            clone = src && isPlainObject(src) ? src : {};\n                        }\n                        // Never move original objects, clone them\n                        setProperty(target, {\n                            name: name,\n                            newValue: extend(deep, clone, copy)\n                        });\n                    // Don't bring in undefined values\n                    } else if (typeof copy !== \"undefined\") {\n                        setProperty(target, {\n                            name: name,\n                            newValue: copy\n                        });\n                    }\n                }\n            }\n        }\n    }\n    // Return the modified object\n    return target;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/extend/index.js\n");

/***/ })

};
;