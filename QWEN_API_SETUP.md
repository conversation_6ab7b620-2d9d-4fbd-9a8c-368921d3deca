# Qwen API 配置指南

## 概述

本系统已集成 Qwen API 用于博文生成。如果未配置 API 密钥，系统将自动使用模拟数据作为备用方案。

## 配置步骤

### 1. 获取 Qwen API 密钥

1. 访问 [阿里云 DashScope 控制台](https://dashscope.console.aliyun.com/)
2. 创建账户并登录
3. 在 API-KEY 管理页面创建新的 API 密钥
4. 复制生成的 API 密钥

### 2. 配置环境变量

在项目根目录的 `.env.local` 文件中添加：

```bash
QWEN_API_KEY=your-api-key-here
```

如果 `.env.local` 文件不存在，请创建它。

### 3. 验证配置

1. 重启开发服务器：`npm run dev`
2. 在博文生成页面输入关键词并生成博文
3. 查看生成历史中的模型信息：
   - 如果显示 `qwen-turbo`，说明 API 配置成功
   - 如果显示 `mock-generator`，说明使用的是模拟数据

## API 功能特性

### 支持的功能

- ✅ 基于关键词生成博文
- ✅ 支持自定义标题
- ✅ 多语言支持（中文、英文等）
- ✅ 系列文章上下文支持
- ✅ 自动 SEO 优化
- ✅ 使用统计记录

### 模型配置

当前使用模型：`qwen-turbo`

模型参数：
- `temperature`: 0.7 (创造性)
- `top_p`: 0.8 (多样性)
- `max_tokens`: 2000 (最大输出长度)

### 备用方案

如果 Qwen API 不可用（未配置密钥或API调用失败），系统会：

1. 自动切换到模拟生成模式
2. 在控制台输出警告信息
3. 生成结构化的模拟内容
4. 在生成历史中标记为 `mock-generator`

## 错误排查

### 常见问题

1. **API 密钥无效**
   - 检查 `.env.local` 文件中的密钥是否正确
   - 确认密钥在阿里云控制台中是否有效

2. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置

3. **配额限制**
   - 检查 API 调用配额是否超限
   - 查看阿里云控制台的使用统计

### 调试方法

1. 查看浏览器开发者工具的网络选项卡
2. 检查服务器控制台日志
3. 查看生成历史中的错误信息

## 费用说明

- Qwen API 按调用次数和Token使用量计费
- 建议在阿里云控制台设置费用提醒
- 开发阶段可以使用免费额度

## 高级配置

### 自定义模型参数

修改 `app/api/generate-blog/route.ts` 中的参数：

```typescript
parameters: {
  temperature: 0.7,    // 0.0-1.0，越高越有创造性
  top_p: 0.8,         // 0.0-1.0，越高越多样化
  max_tokens: 2000,   // 最大输出tokens
  result_format: 'message'
}
```

### 切换模型

支持的模型：
- `qwen-turbo` (快速，推荐)
- `qwen-plus` (更强能力)
- `qwen-max` (最强能力，费用较高)

在 API 路由中修改 `model` 参数即可。

## 支持

如果遇到问题，请：
1. 查看本文档的错误排查部分
2. 检查阿里云DashScope文档
3. 联系技术支持