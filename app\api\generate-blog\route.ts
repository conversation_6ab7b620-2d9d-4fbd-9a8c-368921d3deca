import { NextRequest, NextResponse } from 'next/server';

interface GenerateRequest {
  keywords: string;
  title?: string;
  prompt: string;
  language: string;
  seriesContext?: string;
  wordCount?: string;
}

interface QwenResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export async function POST(request: NextRequest) {
  try {
    // 确保请求体正确解析
    const body: GenerateRequest = await request.json();
    const { keywords, title, prompt, language, seriesContext, wordCount } = body;

    // 验证必需参数
    if (!keywords || !prompt) {
      return NextResponse.json(
        { error: '缺少必需参数：keywords 和 prompt' },
        { status: 400 }
      );
    }

    // 检查API密钥
    const apiKey = process.env.QWEN_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: '未配置QWEN_API_KEY，请联系管理员配置API密钥' },
        { status: 500 }
      );
    }

    // 构建发送给Qwen的提示词
    const targetWordCount = wordCount ? parseInt(wordCount) : 1500;
    const wordCountRange = `${Math.max(targetWordCount - 200, 500)}-${targetWordCount + 200}`;

    const systemPrompt = `你是一个专业的博客写手，擅长创作高质量、SEO优化的博文内容。请根据用户提供的信息生成一篇完整的博文。

要求：
1. 内容要原创、有价值、结构清晰
2. 标题要吸引人且包含关键词
3. 内容要包含适当的标题层级（H1, H2, H3等）
4. 适当使用markdown格式
5. 内容长度要求：约${targetWordCount}字（${wordCountRange}字范围内）
6. 语言：${language === 'zh' ? '中文' : language === 'en' ? 'English' : language}

${seriesContext ? `系列上下文：\n${seriesContext}\n请确保本文与系列其他文章保持连贯性。\n` : ''}

请直接返回博文内容，不要包含额外的说明。`;

    const userPrompt = `${prompt}

关键词：${keywords}
${title ? `建议标题：${title}` : ''}

请生成一篇关于"${keywords}"的博文。`;

    // 调用Qwen API (兼容模式)
    const requestBody = JSON.stringify({
      model: 'qwen-turbo',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: userPrompt
        }
      ],
      temperature: 0.7,
      top_p: 0.8,
      max_tokens: 2000
    });

    const qwenResponse = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: requestBody
    });

    if (!qwenResponse.ok) {
      const errorText = await qwenResponse.text();
      console.error('Qwen API错误:', errorText);
      throw new Error(`Qwen API调用失败: ${qwenResponse.status} ${errorText}`);
    }

    const qwenData: QwenResponse = await qwenResponse.json();
    
    if (!qwenData.choices || qwenData.choices.length === 0) {
      throw new Error('Qwen API返回数据格式错误');
    }

    const generatedContent = qwenData.choices[0].message.content;

    // 从生成的内容中提取标题和正文
    const { extractedTitle, content } = extractTitleAndContent(generatedContent, keywords, title);

    // 生成SEO信息
    const seoTitle = generateSEOTitle(extractedTitle, keywords);
    const seoDescription = generateSEODescription(content, keywords);

    const response = {
      title: extractedTitle,
      content: content,
      seoTitle: seoTitle,
      seoDescription: seoDescription,
      usage: qwenData.usage,
      model: 'qwen-turbo'
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('博文生成错误:', error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '生成博文失败' },
      { status: 500 }
    );
  }
}

// 从生成内容中提取标题和正文
function extractTitleAndContent(generatedContent: string, keywords: string, suggestedTitle?: string) {
  const lines = generatedContent.trim().split('\n');
  let extractedTitle = suggestedTitle || `关于${keywords}的完整指南`;
  let content = generatedContent;

  // 尝试从第一行提取标题
  if (lines[0] && (lines[0].startsWith('#') || lines[0].includes(keywords))) {
    extractedTitle = lines[0].replace(/^#+\s*/, '').trim();
    content = lines.slice(1).join('\n').trim();
  }

  return { extractedTitle, content };
}

// 生成SEO标题
function generateSEOTitle(title: string, keywords: string): string {
  const year = new Date().getFullYear();
  if (title.includes(year.toString())) {
    return title;
  }
  return `${title} - 专业指南 | ${year}`;
}

// 生成SEO描述
function generateSEODescription(content: string, keywords: string): string {
  // 从内容中提取前200个字符作为描述
  const plainText = content
    .replace(/#+\s/g, '') // 移除markdown标题标记
    .replace(/\*\*/g, '') // 移除加粗标记
    .replace(/\n+/g, ' ') // 替换换行为空格
    .trim();
  
  const description = plainText.length > 160 
    ? plainText.substring(0, 157) + '...'
    : plainText;
    
  return description || `详细了解${keywords}的概念、应用和最佳实践。专业的技术指南，帮助您快速掌握相关知识。`;
}

