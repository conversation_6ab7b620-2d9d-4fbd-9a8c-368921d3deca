import { NextRequest, NextResponse } from 'next/server';

interface GenerateRequest {
  keywords: string;
  title?: string;
  prompt: string;
  language: string;
  seriesContext?: string;
}

interface QwenResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export async function POST(request: NextRequest) {
  try {
    // 确保请求体正确解析
    const body: GenerateRequest = await request.json();
    const { keywords, title, prompt, language, seriesContext } = body;

    // 验证必需参数
    if (!keywords || !prompt) {
      return NextResponse.json(
        { error: '缺少必需参数：keywords 和 prompt' },
        { status: 400 }
      );
    }

    // 检查API密钥
    const apiKey = process.env.QWEN_API_KEY;
    if (!apiKey) {
      console.warn('未配置QWEN_API_KEY，使用模拟数据');
      return generateMockContent(body);
    }

    // 构建发送给Qwen的提示词
    const systemPrompt = `你是一个专业的博客写手，擅长创作高质量、SEO优化的博文内容。请根据用户提供的信息生成一篇完整的博文。

要求：
1. 内容要原创、有价值、结构清晰
2. 标题要吸引人且包含关键词
3. 内容要包含适当的标题层级（H1, H2, H3等）
4. 适当使用markdown格式
5. 内容长度适中（1000-2000字）
6. 语言：${language === 'zh' ? '中文' : language === 'en' ? 'English' : language}

${seriesContext ? `系列上下文：\n${seriesContext}\n请确保本文与系列其他文章保持连贯性。\n` : ''}

请直接返回博文内容，不要包含额外的说明。`;

    const userPrompt = `${prompt}

关键词：${keywords}
${title ? `建议标题：${title}` : ''}

请生成一篇关于"${keywords}"的博文。`;

    // 调用Qwen API
    const requestBody = JSON.stringify({
      model: 'qwen-turbo',
      input: {
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ]
      },
      parameters: {
        temperature: 0.7,
        top_p: 0.8,
        max_tokens: 2000,
        result_format: 'message'
      }
    });

    const qwenResponse = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json; charset=utf-8',
      },
      body: requestBody
    });

    if (!qwenResponse.ok) {
      const errorText = await qwenResponse.text();
      console.error('Qwen API错误:', errorText);
      throw new Error(`Qwen API调用失败: ${qwenResponse.status} ${errorText}`);
    }

    const qwenData: QwenResponse = await qwenResponse.json();
    
    if (!qwenData.choices || qwenData.choices.length === 0) {
      throw new Error('Qwen API返回数据格式错误');
    }

    const generatedContent = qwenData.choices[0].message.content;

    // 从生成的内容中提取标题和正文
    const { extractedTitle, content } = extractTitleAndContent(generatedContent, keywords, title);

    // 生成SEO信息
    const seoTitle = generateSEOTitle(extractedTitle, keywords);
    const seoDescription = generateSEODescription(content, keywords);

    const response = {
      title: extractedTitle,
      content: content,
      seoTitle: seoTitle,
      seoDescription: seoDescription,
      usage: qwenData.usage,
      model: 'qwen-turbo'
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('博文生成错误:', error);

    // 检查是否是编码相关错误
    if (error instanceof Error && (
      error.message.includes('ByteString') ||
      error.message.includes('character') ||
      error.message.includes('greater than 255')
    )) {
      console.log('检测到编码错误，使用模拟数据');
      try {
        const body: GenerateRequest = await request.json();
        return generateMockContent(body);
      } catch {
        return NextResponse.json(
          { error: '请求数据格式错误' },
          { status: 400 }
        );
      }
    }

    // 如果API调用失败，回退到模拟数据
    if (error instanceof Error && error.message.includes('API')) {
      console.log('API调用失败，使用模拟数据');
      try {
        const body: GenerateRequest = await request.json();
        return generateMockContent(body);
      } catch {
        return NextResponse.json(
          { error: '请求数据格式错误' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '生成博文失败' },
      { status: 500 }
    );
  }
}

// 从生成内容中提取标题和正文
function extractTitleAndContent(generatedContent: string, keywords: string, suggestedTitle?: string) {
  const lines = generatedContent.trim().split('\n');
  let extractedTitle = suggestedTitle || `关于${keywords}的完整指南`;
  let content = generatedContent;

  // 尝试从第一行提取标题
  if (lines[0] && (lines[0].startsWith('#') || lines[0].includes(keywords))) {
    extractedTitle = lines[0].replace(/^#+\s*/, '').trim();
    content = lines.slice(1).join('\n').trim();
  }

  return { extractedTitle, content };
}

// 生成SEO标题
function generateSEOTitle(title: string, keywords: string): string {
  const year = new Date().getFullYear();
  if (title.includes(year.toString())) {
    return title;
  }
  return `${title} - 专业指南 | ${year}`;
}

// 生成SEO描述
function generateSEODescription(content: string, keywords: string): string {
  // 从内容中提取前200个字符作为描述
  const plainText = content
    .replace(/#+\s/g, '') // 移除markdown标题标记
    .replace(/\*\*/g, '') // 移除加粗标记
    .replace(/\n+/g, ' ') // 替换换行为空格
    .trim();
  
  const description = plainText.length > 160 
    ? plainText.substring(0, 157) + '...'
    : plainText;
    
  return description || `详细了解${keywords}的概念、应用和最佳实践。专业的技术指南，帮助您快速掌握相关知识。`;
}

// 生成模拟内容（备用方案）
async function generateMockContent(body: GenerateRequest) {
  const { keywords, title, seriesContext } = body;
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  const generatedTitle = title || `关于${keywords}的完整指南`;
  const content = `# ${generatedTitle}

## 简介

${seriesContext ? '基于本系列前面的内容，' : ''}本文将深入探讨${keywords}相关的重要概念和实践方法。

## 主要内容

### 1. 基础概念
在了解${keywords}之前，我们需要掌握一些基础概念。${keywords}作为当前技术领域的重要组成部分，具有以下特点：

- 高效性：能够显著提升工作效率
- 可扩展性：支持灵活的扩展和定制
- 易用性：提供友好的用户体验

### 2. 实践应用
${keywords}在实际应用中有着广泛的用途：

**应用场景一：**
在企业级应用中，${keywords}可以帮助团队更好地协作和管理项目。

**应用场景二：**
对于个人用户，${keywords}提供了便捷的解决方案来处理日常任务。

### 3. 最佳实践
以下是使用${keywords}的一些最佳实践：

1. **规划先行**：在开始之前，制定详细的实施计划
2. **逐步推进**：采用迭代的方式，逐步完善功能
3. **持续优化**：根据使用反馈，不断改进和优化
4. **团队协作**：确保团队成员都能熟练使用相关工具
5. **文档记录**：维护完善的文档，便于后续维护

### 4. 注意事项
在使用${keywords}过程中，需要注意以下几点：

- 数据安全：确保敏感信息得到妥善保护
- 性能优化：定期检查和优化系统性能
- 备份策略：建立完善的数据备份机制

## 实施建议

为了更好地应用${keywords}，建议按照以下步骤进行：

1. **需求分析**：明确具体的业务需求和目标
2. **技术选型**：选择合适的技术栈和工具
3. **原型开发**：快速构建原型验证可行性
4. **正式开发**：基于原型进行正式的开发工作
5. **测试部署**：充分测试后进行生产环境部署
6. **运维监控**：建立完善的运维和监控体系

## 总结

通过本文的介绍，我们深入了解了${keywords}的核心概念、实践应用和最佳实践。${keywords}作为一个强大的工具，能够为我们的工作和生活带来显著的便利。

在实际应用中，建议：
- 根据自身需求选择合适的方案
- 重视团队培训和知识分享
- 保持技术更新和持续学习

${seriesContext ? '\n在下一篇文章中，我们将继续探讨${keywords}的高级应用和未来发展趋势。' : ''}

希望这些内容对您有所帮助。如果您有任何问题或建议，欢迎随时交流讨论。`;

  return NextResponse.json({
    title: generatedTitle,
    content: content,
    seoTitle: `${generatedTitle} - 专业指南 | ${new Date().getFullYear()}`,
    seoDescription: `详细了解${keywords}的概念、应用和最佳实践。专业的技术指南，帮助您快速掌握相关知识。`,
    usage: {
      prompt_tokens: 100,
      completion_tokens: 800,
      total_tokens: 900
    },
    model: 'mock-generator'
  });
}