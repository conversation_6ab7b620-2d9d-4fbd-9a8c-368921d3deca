"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark";
exports.ids = ["vendor-chunks/micromark"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark/dev/lib/constructs.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/constructs.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attentionMarkers: () => (/* binding */ attentionMarkers),\n/* harmony export */   contentInitial: () => (/* binding */ contentInitial),\n/* harmony export */   disable: () => (/* binding */ disable),\n/* harmony export */   document: () => (/* binding */ document),\n/* harmony export */   flow: () => (/* binding */ flow),\n/* harmony export */   flowInitial: () => (/* binding */ flowInitial),\n/* harmony export */   insideSpan: () => (/* binding */ insideSpan),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/list.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/block-quote.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/definition.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-indented.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-flow.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-reference.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/line-ending.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/attention.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/autolink.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-text.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-text.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/**\n * @import {Extension} from 'micromark-util-types'\n */ \n\n\n/** @satisfies {Extension['document']} */ const document = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit0]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit1]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit2]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit3]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit4]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit5]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit6]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit7]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit8]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit9]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.greaterThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.blockQuote\n};\n/** @satisfies {Extension['contentInitial']} */ const contentInitial = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__.definition\n};\n/** @satisfies {Extension['flowInitial']} */ const flowInitial = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented\n};\n/** @satisfies {Extension['flow']} */ const flow = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.numberSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__.headingAtx,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__.htmlFlow,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.equalsTo]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced\n};\n/** @satisfies {Extension['string']} */ const string = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape\n};\n/** @satisfies {Extension['text']} */ const text = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__.labelStartImage,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__.autolink,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__.htmlText\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__.labelStartLink,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__.hardBreakEscape,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__.labelEnd,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__.codeText\n};\n/** @satisfies {Extension['insideSpan']} */ const insideSpan = {\n    null: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n        _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__.resolver\n    ]\n};\n/** @satisfies {Extension['attentionMarkers']} */ const attentionMarkers = {\n    null: [\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore\n    ]\n};\n/** @satisfies {Extension['disable']} */ const disable = {\n    null: []\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/constructs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js":
/*!************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/create-tokenizer.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTokenizer: () => (/* binding */ createTokenizer)\n/* harmony export */ });\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(ssr)/./node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/values.js\");\n/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */ /**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */ \n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_0__(\"micromark\");\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */ function createTokenizer(parser, initialize, from) {\n    /** @type {Point} */ let point = {\n        _bufferIndex: -1,\n        _index: 0,\n        line: from && from.line || 1,\n        column: from && from.column || 1,\n        offset: from && from.offset || 0\n    };\n    /** @type {Record<string, number>} */ const columnStart = {};\n    /** @type {Array<Construct>} */ const resolveAllConstructs = [];\n    /** @type {Array<Chunk>} */ let chunks = [];\n    /** @type {Array<Token>} */ let stack = [];\n    /** @type {boolean | undefined} */ let consumed = true;\n    /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */ const effects = {\n        attempt: constructFactory(onsuccessfulconstruct),\n        check: constructFactory(onsuccessfulcheck),\n        consume,\n        enter,\n        exit,\n        interrupt: constructFactory(onsuccessfulcheck, {\n            interrupt: true\n        })\n    };\n    /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */ const context = {\n        code: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n        containerState: {},\n        defineSkip,\n        events: [],\n        now,\n        parser,\n        previous: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n        sliceSerialize,\n        sliceStream,\n        write\n    };\n    /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */ let state = initialize.tokenize.call(context, effects);\n    /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */ let expectedCode;\n    if (initialize.resolveAll) {\n        resolveAllConstructs.push(initialize);\n    }\n    return context;\n    /** @type {TokenizeContext['write']} */ function write(slice) {\n        chunks = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(chunks, slice);\n        main();\n        // Exit if we’re not done, resolve might change stuff.\n        if (chunks[chunks.length - 1] !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            return [];\n        }\n        addResult(initialize, 0);\n        // Otherwise, resolve, and exit.\n        context.events = (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(resolveAllConstructs, context.events, context);\n        return context.events;\n    }\n    //\n    // Tools.\n    //\n    /** @type {TokenizeContext['sliceSerialize']} */ function sliceSerialize(token, expandTabs) {\n        return serializeChunks(sliceStream(token), expandTabs);\n    }\n    /** @type {TokenizeContext['sliceStream']} */ function sliceStream(token) {\n        return sliceChunks(chunks, token);\n    }\n    /** @type {TokenizeContext['now']} */ function now() {\n        // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n        const { _bufferIndex, _index, line, column, offset } = point;\n        return {\n            _bufferIndex,\n            _index,\n            line,\n            column,\n            offset\n        };\n    }\n    /** @type {TokenizeContext['defineSkip']} */ function defineSkip(value) {\n        columnStart[value.line] = value.column;\n        accountForPotentialSkip();\n        debug(\"position: define skip: `%j`\", point);\n    }\n    //\n    // State management.\n    //\n    /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */ function main() {\n        /** @type {number} */ let chunkIndex;\n        while(point._index < chunks.length){\n            const chunk = chunks[point._index];\n            // If we’re in a buffer chunk, loop through it.\n            if (typeof chunk === \"string\") {\n                chunkIndex = point._index;\n                if (point._bufferIndex < 0) {\n                    point._bufferIndex = 0;\n                }\n                while(point._index === chunkIndex && point._bufferIndex < chunk.length){\n                    go(chunk.charCodeAt(point._bufferIndex));\n                }\n            } else {\n                go(chunk);\n            }\n        }\n    }\n    /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */ function go(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(consumed === true, \"expected character to be consumed\");\n        consumed = undefined;\n        debug(\"main: passing `%s` to %s\", code, state && state.name);\n        expectedCode = code;\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof state === \"function\", \"expected state\");\n        state = state(code);\n    }\n    /** @type {Effects['consume']} */ function consume(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected given code to equal expected code\");\n        debug(\"consume: `%s`\", code);\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(consumed === undefined, \"expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === null ? context.events.length === 0 || context.events[context.events.length - 1][0] === \"exit\" : context.events[context.events.length - 1][0] === \"enter\", \"expected last token to be open\");\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n            point.line++;\n            point.column = 1;\n            point.offset += code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed ? 2 : 1;\n            accountForPotentialSkip();\n            debug(\"position: after eol: `%j`\", point);\n        } else if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace) {\n            point.column++;\n            point.offset++;\n        }\n        // Not in a string chunk.\n        if (point._bufferIndex < 0) {\n            point._index++;\n        } else {\n            point._bufferIndex++;\n            // At end of string chunk.\n            if (point._bufferIndex === // Points w/ non-negative `_bufferIndex` reference\n            // strings.\n            /** @type {string} */ chunks[point._index].length) {\n                point._bufferIndex = -1;\n                point._index++;\n            }\n        }\n        // Expose the previous character.\n        context.previous = code;\n        // Mark as consumed.\n        consumed = true;\n    }\n    /** @type {Effects['enter']} */ function enter(type, fields) {\n        /** @type {Token} */ // @ts-expect-error Patch instead of assign required fields to help GC.\n        const token = fields || {};\n        token.type = type;\n        token.start = now();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === \"string\", \"expected string type\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, \"expected non-empty string\");\n        debug(\"enter: `%s`\", type);\n        context.events.push([\n            \"enter\",\n            token,\n            context\n        ]);\n        stack.push(token);\n        return token;\n    }\n    /** @type {Effects['exit']} */ function exit(type) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === \"string\", \"expected string type\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, \"expected non-empty string\");\n        const token = stack.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(token, \"cannot close w/o open tokens\");\n        token.end = now();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type === token.type, \"expected exit token to match current token\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(!(token.start._index === token.end._index && token.start._bufferIndex === token.end._bufferIndex), \"expected non-empty token (`\" + type + \"`)\");\n        debug(\"exit: `%s`\", token.type);\n        context.events.push([\n            \"exit\",\n            token,\n            context\n        ]);\n        return token;\n    }\n    /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */ function onsuccessfulconstruct(construct, info) {\n        addResult(construct, info.from);\n    }\n    /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */ function onsuccessfulcheck(_, info) {\n        info.restore();\n    }\n    /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */ function constructFactory(onreturn, fields) {\n        return hook;\n        /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */ function hook(constructs, returnState, bogusState) {\n            /** @type {ReadonlyArray<Construct>} */ let listOfConstructs;\n            /** @type {number} */ let constructIndex;\n            /** @type {Construct} */ let currentConstruct;\n            /** @type {Info} */ let info;\n            return Array.isArray(constructs) ? /* c8 ignore next 1 */ handleListOfConstructs(constructs) : \"tokenize\" in constructs ? handleListOfConstructs([\n                /** @type {Construct} */ constructs\n            ]) : handleMapOfConstructs(constructs);\n            /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */ function handleMapOfConstructs(map) {\n                return start;\n                /** @type {State} */ function start(code) {\n                    const left = code !== null && map[code];\n                    const all = code !== null && map.null;\n                    const list = [\n                        // To do: add more extension tests.\n                        /* c8 ignore next 2 */ ...Array.isArray(left) ? left : left ? [\n                            left\n                        ] : [],\n                        ...Array.isArray(all) ? all : all ? [\n                            all\n                        ] : []\n                    ];\n                    return handleListOfConstructs(list)(code);\n                }\n            }\n            /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */ function handleListOfConstructs(list) {\n                listOfConstructs = list;\n                constructIndex = 0;\n                if (list.length === 0) {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(bogusState, \"expected `bogusState` to be given\");\n                    return bogusState;\n                }\n                return handleConstruct(list[constructIndex]);\n            }\n            /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */ function handleConstruct(construct) {\n                return start;\n                /** @type {State} */ function start(code) {\n                    // To do: not needed to store if there is no bogus state, probably?\n                    // Currently doesn’t work because `inspect` in document does a check\n                    // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n                    // by not storing.\n                    info = store();\n                    currentConstruct = construct;\n                    if (!construct.partial) {\n                        context.currentConstruct = construct;\n                    }\n                    // Always populated by defaults.\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(context.parser.constructs.disable.null, \"expected `disable.null` to be populated\");\n                    if (construct.name && context.parser.constructs.disable.null.includes(construct.name)) {\n                        return nok(code);\n                    }\n                    return construct.tokenize.call(// If we do have fields, create an object w/ `context` as its\n                    // prototype.\n                    // This allows a “live binding”, which is needed for `interrupt`.\n                    fields ? Object.assign(Object.create(context), fields) : context, effects, ok, nok)(code);\n                }\n            }\n            /** @type {State} */ function ok(code) {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected code\");\n                consumed = true;\n                onreturn(currentConstruct, info);\n                return returnState;\n            }\n            /** @type {State} */ function nok(code) {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected code\");\n                consumed = true;\n                info.restore();\n                if (++constructIndex < listOfConstructs.length) {\n                    return handleConstruct(listOfConstructs[constructIndex]);\n                }\n                return bogusState;\n            }\n        }\n    }\n    /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */ function addResult(construct, from) {\n        if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n            resolveAllConstructs.push(construct);\n        }\n        if (construct.resolve) {\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(context.events, from, context.events.length - from, construct.resolve(context.events.slice(from), context));\n        }\n        if (construct.resolveTo) {\n            context.events = construct.resolveTo(context.events, context);\n        }\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(construct.partial || context.events.length === 0 || context.events[context.events.length - 1][0] === \"exit\", \"expected last token to end\");\n    }\n    /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */ function store() {\n        const startPoint = now();\n        const startPrevious = context.previous;\n        const startCurrentConstruct = context.currentConstruct;\n        const startEventsIndex = context.events.length;\n        const startStack = Array.from(stack);\n        return {\n            from: startEventsIndex,\n            restore\n        };\n        /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */ function restore() {\n            point = startPoint;\n            context.previous = startPrevious;\n            context.currentConstruct = startCurrentConstruct;\n            context.events.length = startEventsIndex;\n            stack = startStack;\n            accountForPotentialSkip();\n            debug(\"position: restore: `%j`\", point);\n        }\n    }\n    /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */ function accountForPotentialSkip() {\n        if (point.line in columnStart && point.column < 2) {\n            point.column = columnStart[point.line];\n            point.offset += columnStart[point.line] - 1;\n        }\n    }\n}\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */ function sliceChunks(chunks, token) {\n    const startIndex = token.start._index;\n    const startBufferIndex = token.start._bufferIndex;\n    const endIndex = token.end._index;\n    const endBufferIndex = token.end._bufferIndex;\n    /** @type {Array<Chunk>} */ let view;\n    if (startIndex === endIndex) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(endBufferIndex > -1, \"expected non-negative end buffer index\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex > -1, \"expected non-negative start buffer index\");\n        // @ts-expect-error `_bufferIndex` is used on string chunks.\n        view = [\n            chunks[startIndex].slice(startBufferIndex, endBufferIndex)\n        ];\n    } else {\n        view = chunks.slice(startIndex, endIndex);\n        if (startBufferIndex > -1) {\n            const head = view[0];\n            if (typeof head === \"string\") {\n                view[0] = head.slice(startBufferIndex);\n            /* c8 ignore next 4 -- used to be used, no longer */ } else {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex === 0, \"expected `startBufferIndex` to be `0`\");\n                view.shift();\n            }\n        }\n        if (endBufferIndex > 0) {\n            // @ts-expect-error `_bufferIndex` is used on string chunks.\n            view.push(chunks[endIndex].slice(0, endBufferIndex));\n        }\n    }\n    return view;\n}\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */ function serializeChunks(chunks, expandTabs) {\n    let index = -1;\n    /** @type {Array<string>} */ const result = [];\n    /** @type {boolean | undefined} */ let atTab;\n    while(++index < chunks.length){\n        const chunk = chunks[index];\n        /** @type {string} */ let value;\n        if (typeof chunk === \"string\") {\n            value = chunk;\n        } else switch(chunk){\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturn:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lineFeed:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab:\n                {\n                    value = expandTabs ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.ht;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace:\n                {\n                    if (!expandTabs && atTab) continue;\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space;\n                    break;\n                }\n            default:\n                {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof chunk === \"number\", \"expected number\");\n                    // Currently only replacement character.\n                    value = String.fromCharCode(chunk);\n                }\n        }\n        atTab = chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab;\n        result.push(value);\n    }\n    return result.join(\"\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvY3JlYXRlLXRva2VuaXplci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBOzs7Ozs7Ozs7Ozs7OztDQWNDLEdBRUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQXFCQyxHQUU4QjtBQUNJO0FBQ3dCO0FBQ1I7QUFDRTtBQUNGO0FBRW5ELE1BQU1TLFFBQVFULGtDQUFXQSxDQUFDO0FBRTFCOzs7Ozs7Ozs7Ozs7Ozs7OztDQWlCQyxHQUNNLFNBQVNVLGdCQUFnQkMsTUFBTSxFQUFFQyxVQUFVLEVBQUVDLElBQUk7SUFDdEQsa0JBQWtCLEdBQ2xCLElBQUlDLFFBQVE7UUFDVkMsY0FBYyxDQUFDO1FBQ2ZDLFFBQVE7UUFDUkMsTUFBTSxRQUFTSixLQUFLSSxJQUFJLElBQUs7UUFDN0JDLFFBQVEsUUFBU0wsS0FBS0ssTUFBTSxJQUFLO1FBQ2pDQyxRQUFRLFFBQVNOLEtBQUtNLE1BQU0sSUFBSztJQUNuQztJQUNBLG1DQUFtQyxHQUNuQyxNQUFNQyxjQUFjLENBQUM7SUFDckIsNkJBQTZCLEdBQzdCLE1BQU1DLHVCQUF1QixFQUFFO0lBQy9CLHlCQUF5QixHQUN6QixJQUFJQyxTQUFTLEVBQUU7SUFDZix5QkFBeUIsR0FDekIsSUFBSUMsUUFBUSxFQUFFO0lBQ2QsZ0NBQWdDLEdBQ2hDLElBQUlDLFdBQVc7SUFFZjs7OztHQUlDLEdBQ0QsTUFBTUMsVUFBVTtRQUNkQyxTQUFTQyxpQkFBaUJDO1FBQzFCQyxPQUFPRixpQkFBaUJHO1FBQ3hCQztRQUNBQztRQUNBQztRQUNBQyxXQUFXUCxpQkFBaUJHLG1CQUFtQjtZQUFDSSxXQUFXO1FBQUk7SUFDakU7SUFFQTs7OztHQUlDLEdBQ0QsTUFBTUMsVUFBVTtRQUNkQyxNQUFNN0Isd0RBQUtBLENBQUM4QixHQUFHO1FBQ2ZDLGdCQUFnQixDQUFDO1FBQ2pCQztRQUNBQyxRQUFRLEVBQUU7UUFDVkM7UUFDQTlCO1FBQ0ErQixVQUFVbkMsd0RBQUtBLENBQUM4QixHQUFHO1FBQ25CTTtRQUNBQztRQUNBQztJQUNGO0lBRUE7Ozs7R0FJQyxHQUNELElBQUlDLFFBQVFsQyxXQUFXbUMsUUFBUSxDQUFDQyxJQUFJLENBQUNiLFNBQVNWO0lBRTlDOzs7O0dBSUMsR0FDRCxJQUFJd0I7SUFFSixJQUFJckMsV0FBV04sVUFBVSxFQUFFO1FBQ3pCZSxxQkFBcUJqQixJQUFJLENBQUNRO0lBQzVCO0lBRUEsT0FBT3VCO0lBRVAscUNBQXFDLEdBQ3JDLFNBQVNVLE1BQU1LLEtBQUs7UUFDbEI1QixTQUFTbEIsNERBQUlBLENBQUNrQixRQUFRNEI7UUFFdEJDO1FBRUEsc0RBQXNEO1FBQ3RELElBQUk3QixNQUFNLENBQUNBLE9BQU84QixNQUFNLEdBQUcsRUFBRSxLQUFLN0Msd0RBQUtBLENBQUM4QixHQUFHLEVBQUU7WUFDM0MsT0FBTyxFQUFFO1FBQ1g7UUFFQWdCLFVBQVV6QyxZQUFZO1FBRXRCLGdDQUFnQztRQUNoQ3VCLFFBQVFLLE1BQU0sR0FBR2xDLHNFQUFVQSxDQUFDZSxzQkFBc0JjLFFBQVFLLE1BQU0sRUFBRUw7UUFFbEUsT0FBT0EsUUFBUUssTUFBTTtJQUN2QjtJQUVBLEVBQUU7SUFDRixTQUFTO0lBQ1QsRUFBRTtJQUVGLDhDQUE4QyxHQUM5QyxTQUFTRyxlQUFlVyxLQUFLLEVBQUVDLFVBQVU7UUFDdkMsT0FBT0MsZ0JBQWdCWixZQUFZVSxRQUFRQztJQUM3QztJQUVBLDJDQUEyQyxHQUMzQyxTQUFTWCxZQUFZVSxLQUFLO1FBQ3hCLE9BQU9HLFlBQVluQyxRQUFRZ0M7SUFDN0I7SUFFQSxtQ0FBbUMsR0FDbkMsU0FBU2I7UUFDUCxpRkFBaUY7UUFDakYsTUFBTSxFQUFDMUIsWUFBWSxFQUFFQyxNQUFNLEVBQUVDLElBQUksRUFBRUMsTUFBTSxFQUFFQyxNQUFNLEVBQUMsR0FBR0w7UUFDckQsT0FBTztZQUFDQztZQUFjQztZQUFRQztZQUFNQztZQUFRQztRQUFNO0lBQ3BEO0lBRUEsMENBQTBDLEdBQzFDLFNBQVNvQixXQUFXbUIsS0FBSztRQUN2QnRDLFdBQVcsQ0FBQ3NDLE1BQU16QyxJQUFJLENBQUMsR0FBR3lDLE1BQU14QyxNQUFNO1FBQ3RDeUM7UUFDQWxELE1BQU0sK0JBQStCSztJQUN2QztJQUVBLEVBQUU7SUFDRixvQkFBb0I7SUFDcEIsRUFBRTtJQUVGOzs7Ozs7Ozs7O0dBVUMsR0FDRCxTQUFTcUM7UUFDUCxtQkFBbUIsR0FDbkIsSUFBSVM7UUFFSixNQUFPOUMsTUFBTUUsTUFBTSxHQUFHTSxPQUFPOEIsTUFBTSxDQUFFO1lBQ25DLE1BQU1TLFFBQVF2QyxNQUFNLENBQUNSLE1BQU1FLE1BQU0sQ0FBQztZQUVsQywrQ0FBK0M7WUFDL0MsSUFBSSxPQUFPNkMsVUFBVSxVQUFVO2dCQUM3QkQsYUFBYTlDLE1BQU1FLE1BQU07Z0JBRXpCLElBQUlGLE1BQU1DLFlBQVksR0FBRyxHQUFHO29CQUMxQkQsTUFBTUMsWUFBWSxHQUFHO2dCQUN2QjtnQkFFQSxNQUNFRCxNQUFNRSxNQUFNLEtBQUs0QyxjQUNqQjlDLE1BQU1DLFlBQVksR0FBRzhDLE1BQU1ULE1BQU0sQ0FDakM7b0JBQ0FVLEdBQUdELE1BQU1FLFVBQVUsQ0FBQ2pELE1BQU1DLFlBQVk7Z0JBQ3hDO1lBQ0YsT0FBTztnQkFDTCtDLEdBQUdEO1lBQ0w7UUFDRjtJQUNGO0lBRUE7Ozs7Ozs7R0FPQyxHQUNELFNBQVNDLEdBQUcxQixJQUFJO1FBQ2RsQywwQ0FBTUEsQ0FBQ3NCLGFBQWEsTUFBTTtRQUMxQkEsV0FBV3dDO1FBQ1h2RCxNQUFNLDRCQUE0QjJCLE1BQU1VLFNBQVNBLE1BQU1tQixJQUFJO1FBQzNEaEIsZUFBZWI7UUFDZmxDLDBDQUFNQSxDQUFDLE9BQU80QyxVQUFVLFlBQVk7UUFDcENBLFFBQVFBLE1BQU1WO0lBQ2hCO0lBRUEsK0JBQStCLEdBQy9CLFNBQVNMLFFBQVFLLElBQUk7UUFDbkJsQywwQ0FBTUEsQ0FBQ2tDLFNBQVNhLGNBQWM7UUFFOUJ4QyxNQUFNLGlCQUFpQjJCO1FBRXZCbEMsMENBQU1BLENBQ0pzQixhQUFhd0MsV0FDYjtRQUVGOUQsMENBQU1BLENBQ0prQyxTQUFTLE9BQ0xELFFBQVFLLE1BQU0sQ0FBQ1ksTUFBTSxLQUFLLEtBQ3hCakIsUUFBUUssTUFBTSxDQUFDTCxRQUFRSyxNQUFNLENBQUNZLE1BQU0sR0FBRyxFQUFFLENBQUMsRUFBRSxLQUFLLFNBQ25EakIsUUFBUUssTUFBTSxDQUFDTCxRQUFRSyxNQUFNLENBQUNZLE1BQU0sR0FBRyxFQUFFLENBQUMsRUFBRSxLQUFLLFNBQ3JEO1FBR0YsSUFBSWpELDRFQUFrQkEsQ0FBQ2lDLE9BQU87WUFDNUJ0QixNQUFNRyxJQUFJO1lBQ1ZILE1BQU1JLE1BQU0sR0FBRztZQUNmSixNQUFNSyxNQUFNLElBQUlpQixTQUFTN0Isd0RBQUtBLENBQUMyRCxzQkFBc0IsR0FBRyxJQUFJO1lBQzVEUDtZQUNBbEQsTUFBTSw2QkFBNkJLO1FBQ3JDLE9BQU8sSUFBSXNCLFNBQVM3Qix3REFBS0EsQ0FBQzRELFlBQVksRUFBRTtZQUN0Q3JELE1BQU1JLE1BQU07WUFDWkosTUFBTUssTUFBTTtRQUNkO1FBRUEseUJBQXlCO1FBQ3pCLElBQUlMLE1BQU1DLFlBQVksR0FBRyxHQUFHO1lBQzFCRCxNQUFNRSxNQUFNO1FBQ2QsT0FBTztZQUNMRixNQUFNQyxZQUFZO1lBRWxCLDBCQUEwQjtZQUMxQixJQUNFRCxNQUFNQyxZQUFZLEtBR0ksa0RBRjRCO1lBQ2xELFdBQVc7WUFDWCxtQkFBbUIsR0FBSU8sTUFBTSxDQUFDUixNQUFNRSxNQUFNLENBQUMsQ0FBRW9DLE1BQU0sRUFDbkQ7Z0JBQ0F0QyxNQUFNQyxZQUFZLEdBQUcsQ0FBQztnQkFDdEJELE1BQU1FLE1BQU07WUFDZDtRQUNGO1FBRUEsaUNBQWlDO1FBQ2pDbUIsUUFBUU8sUUFBUSxHQUFHTjtRQUVuQixvQkFBb0I7UUFDcEJaLFdBQVc7SUFDYjtJQUVBLDZCQUE2QixHQUM3QixTQUFTUSxNQUFNb0MsSUFBSSxFQUFFQyxNQUFNO1FBQ3pCLGtCQUFrQixHQUNsQix1RUFBdUU7UUFDdkUsTUFBTWYsUUFBUWUsVUFBVSxDQUFDO1FBQ3pCZixNQUFNYyxJQUFJLEdBQUdBO1FBQ2JkLE1BQU1nQixLQUFLLEdBQUc3QjtRQUVkdkMsMENBQU1BLENBQUMsT0FBT2tFLFNBQVMsVUFBVTtRQUNqQ2xFLDBDQUFNQSxDQUFDa0UsS0FBS2hCLE1BQU0sR0FBRyxHQUFHO1FBQ3hCM0MsTUFBTSxlQUFlMkQ7UUFFckJqQyxRQUFRSyxNQUFNLENBQUNwQyxJQUFJLENBQUM7WUFBQztZQUFTa0Q7WUFBT25CO1NBQVE7UUFFN0NaLE1BQU1uQixJQUFJLENBQUNrRDtRQUVYLE9BQU9BO0lBQ1Q7SUFFQSw0QkFBNEIsR0FDNUIsU0FBU3JCLEtBQUttQyxJQUFJO1FBQ2hCbEUsMENBQU1BLENBQUMsT0FBT2tFLFNBQVMsVUFBVTtRQUNqQ2xFLDBDQUFNQSxDQUFDa0UsS0FBS2hCLE1BQU0sR0FBRyxHQUFHO1FBRXhCLE1BQU1FLFFBQVEvQixNQUFNZ0QsR0FBRztRQUN2QnJFLDBDQUFNQSxDQUFDb0QsT0FBTztRQUNkQSxNQUFNa0IsR0FBRyxHQUFHL0I7UUFFWnZDLDBDQUFNQSxDQUFDa0UsU0FBU2QsTUFBTWMsSUFBSSxFQUFFO1FBRTVCbEUsMENBQU1BLENBQ0osQ0FDRW9ELENBQUFBLE1BQU1nQixLQUFLLENBQUN0RCxNQUFNLEtBQUtzQyxNQUFNa0IsR0FBRyxDQUFDeEQsTUFBTSxJQUN2Q3NDLE1BQU1nQixLQUFLLENBQUN2RCxZQUFZLEtBQUt1QyxNQUFNa0IsR0FBRyxDQUFDekQsWUFBWSxHQUVyRCxnQ0FBZ0NxRCxPQUFPO1FBR3pDM0QsTUFBTSxjQUFjNkMsTUFBTWMsSUFBSTtRQUM5QmpDLFFBQVFLLE1BQU0sQ0FBQ3BDLElBQUksQ0FBQztZQUFDO1lBQVFrRDtZQUFPbkI7U0FBUTtRQUU1QyxPQUFPbUI7SUFDVDtJQUVBOzs7O0dBSUMsR0FDRCxTQUFTMUIsc0JBQXNCNkMsU0FBUyxFQUFFQyxJQUFJO1FBQzVDckIsVUFBVW9CLFdBQVdDLEtBQUs3RCxJQUFJO0lBQ2hDO0lBRUE7Ozs7R0FJQyxHQUNELFNBQVNpQixrQkFBa0I2QyxDQUFDLEVBQUVELElBQUk7UUFDaENBLEtBQUtFLE9BQU87SUFDZDtJQUVBOzs7Ozs7O0dBT0MsR0FDRCxTQUFTakQsaUJBQWlCa0QsUUFBUSxFQUFFUixNQUFNO1FBQ3hDLE9BQU9TO1FBRVA7Ozs7Ozs7Ozs7OztLQVlDLEdBQ0QsU0FBU0EsS0FBS0MsVUFBVSxFQUFFQyxXQUFXLEVBQUVDLFVBQVU7WUFDL0MscUNBQXFDLEdBQ3JDLElBQUlDO1lBQ0osbUJBQW1CLEdBQ25CLElBQUlDO1lBQ0osc0JBQXNCLEdBQ3RCLElBQUlDO1lBQ0osaUJBQWlCLEdBQ2pCLElBQUlWO1lBRUosT0FBT1csTUFBTUMsT0FBTyxDQUFDUCxjQUNqQixvQkFBb0IsR0FDcEJRLHVCQUF1QlIsY0FDdkIsY0FBY0EsYUFFWlEsdUJBQXVCO2dCQUFDLHNCQUFzQixHQUFJUjthQUFZLElBQzlEUyxzQkFBc0JUO1lBRTVCOzs7Ozs7O09BT0MsR0FDRCxTQUFTUyxzQkFBc0JDLEdBQUc7Z0JBQ2hDLE9BQU9uQjtnQkFFUCxrQkFBa0IsR0FDbEIsU0FBU0EsTUFBTWxDLElBQUk7b0JBQ2pCLE1BQU1zRCxPQUFPdEQsU0FBUyxRQUFRcUQsR0FBRyxDQUFDckQsS0FBSztvQkFDdkMsTUFBTXVELE1BQU12RCxTQUFTLFFBQVFxRCxJQUFJRyxJQUFJO29CQUNyQyxNQUFNQyxPQUFPO3dCQUNYLG1DQUFtQzt3QkFDbkMsb0JBQW9CLE1BQ2hCUixNQUFNQyxPQUFPLENBQUNJLFFBQVFBLE9BQU9BLE9BQU87NEJBQUNBO3lCQUFLLEdBQUcsRUFBRTsyQkFDL0NMLE1BQU1DLE9BQU8sQ0FBQ0ssT0FBT0EsTUFBTUEsTUFBTTs0QkFBQ0E7eUJBQUksR0FBRyxFQUFFO3FCQUNoRDtvQkFFRCxPQUFPSix1QkFBdUJNLE1BQU16RDtnQkFDdEM7WUFDRjtZQUVBOzs7Ozs7O09BT0MsR0FDRCxTQUFTbUQsdUJBQXVCTSxJQUFJO2dCQUNsQ1gsbUJBQW1CVztnQkFDbkJWLGlCQUFpQjtnQkFFakIsSUFBSVUsS0FBS3pDLE1BQU0sS0FBSyxHQUFHO29CQUNyQmxELDBDQUFNQSxDQUFDK0UsWUFBWTtvQkFDbkIsT0FBT0E7Z0JBQ1Q7Z0JBRUEsT0FBT2EsZ0JBQWdCRCxJQUFJLENBQUNWLGVBQWU7WUFDN0M7WUFFQTs7Ozs7OztPQU9DLEdBQ0QsU0FBU1csZ0JBQWdCckIsU0FBUztnQkFDaEMsT0FBT0g7Z0JBRVAsa0JBQWtCLEdBQ2xCLFNBQVNBLE1BQU1sQyxJQUFJO29CQUNqQixtRUFBbUU7b0JBQ25FLG9FQUFvRTtvQkFDcEUsdUVBQXVFO29CQUN2RSxrQkFBa0I7b0JBQ2xCc0MsT0FBT3FCO29CQUNQWCxtQkFBbUJYO29CQUVuQixJQUFJLENBQUNBLFVBQVV1QixPQUFPLEVBQUU7d0JBQ3RCN0QsUUFBUWlELGdCQUFnQixHQUFHWDtvQkFDN0I7b0JBRUEsZ0NBQWdDO29CQUNoQ3ZFLDBDQUFNQSxDQUNKaUMsUUFBUXhCLE1BQU0sQ0FBQ29FLFVBQVUsQ0FBQ2tCLE9BQU8sQ0FBQ0wsSUFBSSxFQUN0QztvQkFHRixJQUNFbkIsVUFBVVIsSUFBSSxJQUNkOUIsUUFBUXhCLE1BQU0sQ0FBQ29FLFVBQVUsQ0FBQ2tCLE9BQU8sQ0FBQ0wsSUFBSSxDQUFDTSxRQUFRLENBQUN6QixVQUFVUixJQUFJLEdBQzlEO3dCQUNBLE9BQU9rQyxJQUFJL0Q7b0JBQ2I7b0JBRUEsT0FBT3FDLFVBQVUxQixRQUFRLENBQUNDLElBQUksQ0FDNUIsNkRBQTZEO29CQUM3RCxhQUFhO29CQUNiLGlFQUFpRTtvQkFDakVxQixTQUFTK0IsT0FBT0MsTUFBTSxDQUFDRCxPQUFPRSxNQUFNLENBQUNuRSxVQUFVa0MsVUFBVWxDLFNBQ3pEVixTQUNBeEIsSUFDQWtHLEtBQ0EvRDtnQkFDSjtZQUNGO1lBRUEsa0JBQWtCLEdBQ2xCLFNBQVNuQyxHQUFHbUMsSUFBSTtnQkFDZGxDLDBDQUFNQSxDQUFDa0MsU0FBU2EsY0FBYztnQkFDOUJ6QixXQUFXO2dCQUNYcUQsU0FBU08sa0JBQWtCVjtnQkFDM0IsT0FBT007WUFDVDtZQUVBLGtCQUFrQixHQUNsQixTQUFTbUIsSUFBSS9ELElBQUk7Z0JBQ2ZsQywwQ0FBTUEsQ0FBQ2tDLFNBQVNhLGNBQWM7Z0JBQzlCekIsV0FBVztnQkFDWGtELEtBQUtFLE9BQU87Z0JBRVosSUFBSSxFQUFFTyxpQkFBaUJELGlCQUFpQjlCLE1BQU0sRUFBRTtvQkFDOUMsT0FBTzBDLGdCQUFnQlosZ0JBQWdCLENBQUNDLGVBQWU7Z0JBQ3pEO2dCQUVBLE9BQU9GO1lBQ1Q7UUFDRjtJQUNGO0lBRUE7Ozs7Ozs7R0FPQyxHQUNELFNBQVM1QixVQUFVb0IsU0FBUyxFQUFFNUQsSUFBSTtRQUNoQyxJQUFJNEQsVUFBVW5FLFVBQVUsSUFBSSxDQUFDZSxxQkFBcUI2RSxRQUFRLENBQUN6QixZQUFZO1lBQ3JFcEQscUJBQXFCakIsSUFBSSxDQUFDcUU7UUFDNUI7UUFFQSxJQUFJQSxVQUFVOEIsT0FBTyxFQUFFO1lBQ3JCbEcsOERBQU1BLENBQ0o4QixRQUFRSyxNQUFNLEVBQ2QzQixNQUNBc0IsUUFBUUssTUFBTSxDQUFDWSxNQUFNLEdBQUd2QyxNQUN4QjRELFVBQVU4QixPQUFPLENBQUNwRSxRQUFRSyxNQUFNLENBQUNVLEtBQUssQ0FBQ3JDLE9BQU9zQjtRQUVsRDtRQUVBLElBQUlzQyxVQUFVK0IsU0FBUyxFQUFFO1lBQ3ZCckUsUUFBUUssTUFBTSxHQUFHaUMsVUFBVStCLFNBQVMsQ0FBQ3JFLFFBQVFLLE1BQU0sRUFBRUw7UUFDdkQ7UUFFQWpDLDBDQUFNQSxDQUNKdUUsVUFBVXVCLE9BQU8sSUFDZjdELFFBQVFLLE1BQU0sQ0FBQ1ksTUFBTSxLQUFLLEtBQzFCakIsUUFBUUssTUFBTSxDQUFDTCxRQUFRSyxNQUFNLENBQUNZLE1BQU0sR0FBRyxFQUFFLENBQUMsRUFBRSxLQUFLLFFBQ25EO0lBRUo7SUFFQTs7Ozs7R0FLQyxHQUNELFNBQVMyQztRQUNQLE1BQU1VLGFBQWFoRTtRQUNuQixNQUFNaUUsZ0JBQWdCdkUsUUFBUU8sUUFBUTtRQUN0QyxNQUFNaUUsd0JBQXdCeEUsUUFBUWlELGdCQUFnQjtRQUN0RCxNQUFNd0IsbUJBQW1CekUsUUFBUUssTUFBTSxDQUFDWSxNQUFNO1FBQzlDLE1BQU15RCxhQUFheEIsTUFBTXhFLElBQUksQ0FBQ1U7UUFFOUIsT0FBTztZQUFDVixNQUFNK0Y7WUFBa0JoQztRQUFPO1FBRXZDOzs7OztLQUtDLEdBQ0QsU0FBU0E7WUFDUDlELFFBQVEyRjtZQUNSdEUsUUFBUU8sUUFBUSxHQUFHZ0U7WUFDbkJ2RSxRQUFRaUQsZ0JBQWdCLEdBQUd1QjtZQUMzQnhFLFFBQVFLLE1BQU0sQ0FBQ1ksTUFBTSxHQUFHd0Q7WUFDeEJyRixRQUFRc0Y7WUFDUmxEO1lBQ0FsRCxNQUFNLDJCQUEyQks7UUFDbkM7SUFDRjtJQUVBOzs7Ozs7R0FNQyxHQUNELFNBQVM2QztRQUNQLElBQUk3QyxNQUFNRyxJQUFJLElBQUlHLGVBQWVOLE1BQU1JLE1BQU0sR0FBRyxHQUFHO1lBQ2pESixNQUFNSSxNQUFNLEdBQUdFLFdBQVcsQ0FBQ04sTUFBTUcsSUFBSSxDQUFDO1lBQ3RDSCxNQUFNSyxNQUFNLElBQUlDLFdBQVcsQ0FBQ04sTUFBTUcsSUFBSSxDQUFDLEdBQUc7UUFDNUM7SUFDRjtBQUNGO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ0QsU0FBU3dDLFlBQVluQyxNQUFNLEVBQUVnQyxLQUFLO0lBQ2hDLE1BQU13RCxhQUFheEQsTUFBTWdCLEtBQUssQ0FBQ3RELE1BQU07SUFDckMsTUFBTStGLG1CQUFtQnpELE1BQU1nQixLQUFLLENBQUN2RCxZQUFZO0lBQ2pELE1BQU1pRyxXQUFXMUQsTUFBTWtCLEdBQUcsQ0FBQ3hELE1BQU07SUFDakMsTUFBTWlHLGlCQUFpQjNELE1BQU1rQixHQUFHLENBQUN6RCxZQUFZO0lBQzdDLHlCQUF5QixHQUN6QixJQUFJbUc7SUFFSixJQUFJSixlQUFlRSxVQUFVO1FBQzNCOUcsMENBQU1BLENBQUMrRyxpQkFBaUIsQ0FBQyxHQUFHO1FBQzVCL0csMENBQU1BLENBQUM2RyxtQkFBbUIsQ0FBQyxHQUFHO1FBQzlCLDREQUE0RDtRQUM1REcsT0FBTztZQUFDNUYsTUFBTSxDQUFDd0YsV0FBVyxDQUFDNUQsS0FBSyxDQUFDNkQsa0JBQWtCRTtTQUFnQjtJQUNyRSxPQUFPO1FBQ0xDLE9BQU81RixPQUFPNEIsS0FBSyxDQUFDNEQsWUFBWUU7UUFFaEMsSUFBSUQsbUJBQW1CLENBQUMsR0FBRztZQUN6QixNQUFNSSxPQUFPRCxJQUFJLENBQUMsRUFBRTtZQUNwQixJQUFJLE9BQU9DLFNBQVMsVUFBVTtnQkFDNUJELElBQUksQ0FBQyxFQUFFLEdBQUdDLEtBQUtqRSxLQUFLLENBQUM2RDtZQUNyQixrREFBa0QsR0FDcEQsT0FBTztnQkFDTDdHLDBDQUFNQSxDQUFDNkcscUJBQXFCLEdBQUc7Z0JBQy9CRyxLQUFLRSxLQUFLO1lBQ1o7UUFDRjtRQUVBLElBQUlILGlCQUFpQixHQUFHO1lBQ3RCLDREQUE0RDtZQUM1REMsS0FBSzlHLElBQUksQ0FBQ2tCLE1BQU0sQ0FBQzBGLFNBQVMsQ0FBQzlELEtBQUssQ0FBQyxHQUFHK0Q7UUFDdEM7SUFDRjtJQUVBLE9BQU9DO0FBQ1Q7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDRCxTQUFTMUQsZ0JBQWdCbEMsTUFBTSxFQUFFaUMsVUFBVTtJQUN6QyxJQUFJOEQsUUFBUSxDQUFDO0lBQ2IsMEJBQTBCLEdBQzFCLE1BQU1DLFNBQVMsRUFBRTtJQUNqQixnQ0FBZ0MsR0FDaEMsSUFBSUM7SUFFSixNQUFPLEVBQUVGLFFBQVEvRixPQUFPOEIsTUFBTSxDQUFFO1FBQzlCLE1BQU1TLFFBQVF2QyxNQUFNLENBQUMrRixNQUFNO1FBQzNCLG1CQUFtQixHQUNuQixJQUFJM0Q7UUFFSixJQUFJLE9BQU9HLFVBQVUsVUFBVTtZQUM3QkgsUUFBUUc7UUFDVixPQUNFLE9BQVFBO1lBQ04sS0FBS3RELHdEQUFLQSxDQUFDaUgsY0FBYztnQkFBRTtvQkFDekI5RCxRQUFRbEQseURBQU1BLENBQUNpSCxFQUFFO29CQUVqQjtnQkFDRjtZQUVBLEtBQUtsSCx3REFBS0EsQ0FBQ21ILFFBQVE7Z0JBQUU7b0JBQ25CaEUsUUFBUWxELHlEQUFNQSxDQUFDbUgsRUFBRTtvQkFFakI7Z0JBQ0Y7WUFFQSxLQUFLcEgsd0RBQUtBLENBQUMyRCxzQkFBc0I7Z0JBQUU7b0JBQ2pDUixRQUFRbEQseURBQU1BLENBQUNpSCxFQUFFLEdBQUdqSCx5REFBTUEsQ0FBQ21ILEVBQUU7b0JBRTdCO2dCQUNGO1lBRUEsS0FBS3BILHdEQUFLQSxDQUFDcUgsYUFBYTtnQkFBRTtvQkFDeEJsRSxRQUFRSCxhQUFhL0MseURBQU1BLENBQUNxSCxLQUFLLEdBQUdySCx5REFBTUEsQ0FBQ3NILEVBQUU7b0JBRTdDO2dCQUNGO1lBRUEsS0FBS3ZILHdEQUFLQSxDQUFDNEQsWUFBWTtnQkFBRTtvQkFDdkIsSUFBSSxDQUFDWixjQUFjZ0UsT0FBTztvQkFDMUI3RCxRQUFRbEQseURBQU1BLENBQUNxSCxLQUFLO29CQUVwQjtnQkFDRjtZQUVBO2dCQUFTO29CQUNQM0gsMENBQU1BLENBQUMsT0FBTzJELFVBQVUsVUFBVTtvQkFDbEMsd0NBQXdDO29CQUN4Q0gsUUFBUXFFLE9BQU9DLFlBQVksQ0FBQ25FO2dCQUM5QjtRQUNGO1FBRUYwRCxRQUFRMUQsVUFBVXRELHdEQUFLQSxDQUFDcUgsYUFBYTtRQUNyQ04sT0FBT2xILElBQUksQ0FBQ3NEO0lBQ2Q7SUFFQSxPQUFPNEQsT0FBT1csSUFBSSxDQUFDO0FBQ3JCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvY3JlYXRlLXRva2VuaXplci5qcz8xMDU0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7XG4gKiAgIENodW5rLFxuICogICBDb2RlLFxuICogICBDb25zdHJ1Y3RSZWNvcmQsXG4gKiAgIENvbnN0cnVjdCxcbiAqICAgRWZmZWN0cyxcbiAqICAgSW5pdGlhbENvbnN0cnVjdCxcbiAqICAgUGFyc2VDb250ZXh0LFxuICogICBQb2ludCxcbiAqICAgU3RhdGUsXG4gKiAgIFRva2VuaXplQ29udGV4dCxcbiAqICAgVG9rZW5cbiAqIH0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuLyoqXG4gKiBAY2FsbGJhY2sgUmVzdG9yZVxuICogICBSZXN0b3JlIHRoZSBzdGF0ZS5cbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gKiAgIE5vdGhpbmcuXG4gKlxuICogQHR5cGVkZWYgSW5mb1xuICogICBJbmZvLlxuICogQHByb3BlcnR5IHtSZXN0b3JlfSByZXN0b3JlXG4gKiAgIFJlc3RvcmUuXG4gKiBAcHJvcGVydHkge251bWJlcn0gZnJvbVxuICogICBGcm9tLlxuICpcbiAqIEBjYWxsYmFjayBSZXR1cm5IYW5kbGVcbiAqICAgSGFuZGxlIGEgc3VjY2Vzc2Z1bCBydW4uXG4gKiBAcGFyYW0ge0NvbnN0cnVjdH0gY29uc3RydWN0XG4gKiAgIENvbnN0cnVjdC5cbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogICBJbmZvLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTm90aGluZy5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlRGVidWcgZnJvbSAnZGVidWcnXG5pbXBvcnQge29rIGFzIGFzc2VydH0gZnJvbSAnZGV2bG9wJ1xuaW1wb3J0IHttYXJrZG93bkxpbmVFbmRpbmd9IGZyb20gJ21pY3JvbWFyay11dGlsLWNoYXJhY3RlcidcbmltcG9ydCB7cHVzaCwgc3BsaWNlfSBmcm9tICdtaWNyb21hcmstdXRpbC1jaHVua2VkJ1xuaW1wb3J0IHtyZXNvbHZlQWxsfSBmcm9tICdtaWNyb21hcmstdXRpbC1yZXNvbHZlLWFsbCdcbmltcG9ydCB7Y29kZXMsIHZhbHVlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sJ1xuXG5jb25zdCBkZWJ1ZyA9IGNyZWF0ZURlYnVnKCdtaWNyb21hcmsnKVxuXG4vKipcbiAqIENyZWF0ZSBhIHRva2VuaXplci5cbiAqIFRva2VuaXplcnMgZGVhbCB3aXRoIG9uZSB0eXBlIG9mIGRhdGEgKGUuZy4sIGNvbnRhaW5lcnMsIGZsb3csIHRleHQpLlxuICogVGhlIHBhcnNlciBpcyB0aGUgb2JqZWN0IGRlYWxpbmcgd2l0aCBpdCBhbGwuXG4gKiBgaW5pdGlhbGl6ZWAgd29ya3MgbGlrZSBvdGhlciBjb25zdHJ1Y3RzLCBleGNlcHQgdGhhdCBvbmx5IGl0cyBgdG9rZW5pemVgXG4gKiBmdW5jdGlvbiBpcyB1c2VkLCBpbiB3aGljaCBjYXNlIGl0IGRvZXNu4oCZdCByZWNlaXZlIGFuIGBva2Agb3IgYG5va2AuXG4gKiBgZnJvbWAgY2FuIGJlIGdpdmVuIHRvIHNldCB0aGUgcG9pbnQgYmVmb3JlIHRoZSBmaXJzdCBjaGFyYWN0ZXIsIGFsdGhvdWdoXG4gKiB3aGVuIGZ1cnRoZXIgbGluZXMgYXJlIGluZGVudGVkLCB0aGV5IG11c3QgYmUgc2V0IHdpdGggYGRlZmluZVNraXBgLlxuICpcbiAqIEBwYXJhbSB7UGFyc2VDb250ZXh0fSBwYXJzZXJcbiAqICAgUGFyc2VyLlxuICogQHBhcmFtIHtJbml0aWFsQ29uc3RydWN0fSBpbml0aWFsaXplXG4gKiAgIENvbnN0cnVjdC5cbiAqIEBwYXJhbSB7T21pdDxQb2ludCwgJ19idWZmZXJJbmRleCcgfCAnX2luZGV4Jz4gfCB1bmRlZmluZWR9IFtmcm9tXVxuICogICBQb2ludCAob3B0aW9uYWwpLlxuICogQHJldHVybnMge1Rva2VuaXplQ29udGV4dH1cbiAqICAgQ29udGV4dC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVRva2VuaXplcihwYXJzZXIsIGluaXRpYWxpemUsIGZyb20pIHtcbiAgLyoqIEB0eXBlIHtQb2ludH0gKi9cbiAgbGV0IHBvaW50ID0ge1xuICAgIF9idWZmZXJJbmRleDogLTEsXG4gICAgX2luZGV4OiAwLFxuICAgIGxpbmU6IChmcm9tICYmIGZyb20ubGluZSkgfHwgMSxcbiAgICBjb2x1bW46IChmcm9tICYmIGZyb20uY29sdW1uKSB8fCAxLFxuICAgIG9mZnNldDogKGZyb20gJiYgZnJvbS5vZmZzZXQpIHx8IDBcbiAgfVxuICAvKiogQHR5cGUge1JlY29yZDxzdHJpbmcsIG51bWJlcj59ICovXG4gIGNvbnN0IGNvbHVtblN0YXJ0ID0ge31cbiAgLyoqIEB0eXBlIHtBcnJheTxDb25zdHJ1Y3Q+fSAqL1xuICBjb25zdCByZXNvbHZlQWxsQ29uc3RydWN0cyA9IFtdXG4gIC8qKiBAdHlwZSB7QXJyYXk8Q2h1bms+fSAqL1xuICBsZXQgY2h1bmtzID0gW11cbiAgLyoqIEB0eXBlIHtBcnJheTxUb2tlbj59ICovXG4gIGxldCBzdGFjayA9IFtdXG4gIC8qKiBAdHlwZSB7Ym9vbGVhbiB8IHVuZGVmaW5lZH0gKi9cbiAgbGV0IGNvbnN1bWVkID0gdHJ1ZVxuXG4gIC8qKlxuICAgKiBUb29scyB1c2VkIGZvciB0b2tlbml6aW5nLlxuICAgKlxuICAgKiBAdHlwZSB7RWZmZWN0c31cbiAgICovXG4gIGNvbnN0IGVmZmVjdHMgPSB7XG4gICAgYXR0ZW1wdDogY29uc3RydWN0RmFjdG9yeShvbnN1Y2Nlc3NmdWxjb25zdHJ1Y3QpLFxuICAgIGNoZWNrOiBjb25zdHJ1Y3RGYWN0b3J5KG9uc3VjY2Vzc2Z1bGNoZWNrKSxcbiAgICBjb25zdW1lLFxuICAgIGVudGVyLFxuICAgIGV4aXQsXG4gICAgaW50ZXJydXB0OiBjb25zdHJ1Y3RGYWN0b3J5KG9uc3VjY2Vzc2Z1bGNoZWNrLCB7aW50ZXJydXB0OiB0cnVlfSlcbiAgfVxuXG4gIC8qKlxuICAgKiBTdGF0ZSBhbmQgdG9vbHMgZm9yIHJlc29sdmluZyBhbmQgc2VyaWFsaXppbmcuXG4gICAqXG4gICAqIEB0eXBlIHtUb2tlbml6ZUNvbnRleHR9XG4gICAqL1xuICBjb25zdCBjb250ZXh0ID0ge1xuICAgIGNvZGU6IGNvZGVzLmVvZixcbiAgICBjb250YWluZXJTdGF0ZToge30sXG4gICAgZGVmaW5lU2tpcCxcbiAgICBldmVudHM6IFtdLFxuICAgIG5vdyxcbiAgICBwYXJzZXIsXG4gICAgcHJldmlvdXM6IGNvZGVzLmVvZixcbiAgICBzbGljZVNlcmlhbGl6ZSxcbiAgICBzbGljZVN0cmVhbSxcbiAgICB3cml0ZVxuICB9XG5cbiAgLyoqXG4gICAqIFRoZSBzdGF0ZSBmdW5jdGlvbi5cbiAgICpcbiAgICogQHR5cGUge1N0YXRlIHwgdW5kZWZpbmVkfVxuICAgKi9cbiAgbGV0IHN0YXRlID0gaW5pdGlhbGl6ZS50b2tlbml6ZS5jYWxsKGNvbnRleHQsIGVmZmVjdHMpXG5cbiAgLyoqXG4gICAqIFRyYWNrIHdoaWNoIGNoYXJhY3RlciB3ZSBleHBlY3QgdG8gYmUgY29uc3VtZWQsIHRvIGNhdGNoIGJ1Z3MuXG4gICAqXG4gICAqIEB0eXBlIHtDb2RlfVxuICAgKi9cbiAgbGV0IGV4cGVjdGVkQ29kZVxuXG4gIGlmIChpbml0aWFsaXplLnJlc29sdmVBbGwpIHtcbiAgICByZXNvbHZlQWxsQ29uc3RydWN0cy5wdXNoKGluaXRpYWxpemUpXG4gIH1cblxuICByZXR1cm4gY29udGV4dFxuXG4gIC8qKiBAdHlwZSB7VG9rZW5pemVDb250ZXh0Wyd3cml0ZSddfSAqL1xuICBmdW5jdGlvbiB3cml0ZShzbGljZSkge1xuICAgIGNodW5rcyA9IHB1c2goY2h1bmtzLCBzbGljZSlcblxuICAgIG1haW4oKVxuXG4gICAgLy8gRXhpdCBpZiB3ZeKAmXJlIG5vdCBkb25lLCByZXNvbHZlIG1pZ2h0IGNoYW5nZSBzdHVmZi5cbiAgICBpZiAoY2h1bmtzW2NodW5rcy5sZW5ndGggLSAxXSAhPT0gY29kZXMuZW9mKSB7XG4gICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBhZGRSZXN1bHQoaW5pdGlhbGl6ZSwgMClcblxuICAgIC8vIE90aGVyd2lzZSwgcmVzb2x2ZSwgYW5kIGV4aXQuXG4gICAgY29udGV4dC5ldmVudHMgPSByZXNvbHZlQWxsKHJlc29sdmVBbGxDb25zdHJ1Y3RzLCBjb250ZXh0LmV2ZW50cywgY29udGV4dClcblxuICAgIHJldHVybiBjb250ZXh0LmV2ZW50c1xuICB9XG5cbiAgLy9cbiAgLy8gVG9vbHMuXG4gIC8vXG5cbiAgLyoqIEB0eXBlIHtUb2tlbml6ZUNvbnRleHRbJ3NsaWNlU2VyaWFsaXplJ119ICovXG4gIGZ1bmN0aW9uIHNsaWNlU2VyaWFsaXplKHRva2VuLCBleHBhbmRUYWJzKSB7XG4gICAgcmV0dXJuIHNlcmlhbGl6ZUNodW5rcyhzbGljZVN0cmVhbSh0b2tlbiksIGV4cGFuZFRhYnMpXG4gIH1cblxuICAvKiogQHR5cGUge1Rva2VuaXplQ29udGV4dFsnc2xpY2VTdHJlYW0nXX0gKi9cbiAgZnVuY3Rpb24gc2xpY2VTdHJlYW0odG9rZW4pIHtcbiAgICByZXR1cm4gc2xpY2VDaHVua3MoY2h1bmtzLCB0b2tlbilcbiAgfVxuXG4gIC8qKiBAdHlwZSB7VG9rZW5pemVDb250ZXh0Wydub3cnXX0gKi9cbiAgZnVuY3Rpb24gbm93KCkge1xuICAgIC8vIFRoaXMgaXMgYSBob3QgcGF0aCwgc28gd2UgY2xvbmUgbWFudWFsbHkgaW5zdGVhZCBvZiBgT2JqZWN0LmFzc2lnbih7fSwgcG9pbnQpYFxuICAgIGNvbnN0IHtfYnVmZmVySW5kZXgsIF9pbmRleCwgbGluZSwgY29sdW1uLCBvZmZzZXR9ID0gcG9pbnRcbiAgICByZXR1cm4ge19idWZmZXJJbmRleCwgX2luZGV4LCBsaW5lLCBjb2x1bW4sIG9mZnNldH1cbiAgfVxuXG4gIC8qKiBAdHlwZSB7VG9rZW5pemVDb250ZXh0WydkZWZpbmVTa2lwJ119ICovXG4gIGZ1bmN0aW9uIGRlZmluZVNraXAodmFsdWUpIHtcbiAgICBjb2x1bW5TdGFydFt2YWx1ZS5saW5lXSA9IHZhbHVlLmNvbHVtblxuICAgIGFjY291bnRGb3JQb3RlbnRpYWxTa2lwKClcbiAgICBkZWJ1ZygncG9zaXRpb246IGRlZmluZSBza2lwOiBgJWpgJywgcG9pbnQpXG4gIH1cblxuICAvL1xuICAvLyBTdGF0ZSBtYW5hZ2VtZW50LlxuICAvL1xuXG4gIC8qKlxuICAgKiBNYWluIGxvb3AgKG5vdGUgdGhhdCBgX2luZGV4YCBhbmQgYF9idWZmZXJJbmRleGAgaW4gYHBvaW50YCBhcmUgbW9kaWZpZWQgYnlcbiAgICogYGNvbnN1bWVgKS5cbiAgICogSGVyZSBpcyB3aGVyZSB3ZSB3YWxrIHRocm91Z2ggdGhlIGNodW5rcywgd2hpY2ggZWl0aGVyIGluY2x1ZGUgc3RyaW5ncyBvZlxuICAgKiBzZXZlcmFsIGNoYXJhY3RlcnMsIG9yIG51bWVyaWNhbCBjaGFyYWN0ZXIgY29kZXMuXG4gICAqIFRoZSByZWFzb24gdG8gZG8gdGhpcyBpbiBhIGxvb3AgaW5zdGVhZCBvZiBhIGNhbGwgaXMgc28gdGhlIHN0YWNrIGNhblxuICAgKiBkcmFpbi5cbiAgICpcbiAgICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAgICogICBOb3RoaW5nLlxuICAgKi9cbiAgZnVuY3Rpb24gbWFpbigpIHtcbiAgICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgICBsZXQgY2h1bmtJbmRleFxuXG4gICAgd2hpbGUgKHBvaW50Ll9pbmRleCA8IGNodW5rcy5sZW5ndGgpIHtcbiAgICAgIGNvbnN0IGNodW5rID0gY2h1bmtzW3BvaW50Ll9pbmRleF1cblxuICAgICAgLy8gSWYgd2XigJlyZSBpbiBhIGJ1ZmZlciBjaHVuaywgbG9vcCB0aHJvdWdoIGl0LlxuICAgICAgaWYgKHR5cGVvZiBjaHVuayA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgY2h1bmtJbmRleCA9IHBvaW50Ll9pbmRleFxuXG4gICAgICAgIGlmIChwb2ludC5fYnVmZmVySW5kZXggPCAwKSB7XG4gICAgICAgICAgcG9pbnQuX2J1ZmZlckluZGV4ID0gMFxuICAgICAgICB9XG5cbiAgICAgICAgd2hpbGUgKFxuICAgICAgICAgIHBvaW50Ll9pbmRleCA9PT0gY2h1bmtJbmRleCAmJlxuICAgICAgICAgIHBvaW50Ll9idWZmZXJJbmRleCA8IGNodW5rLmxlbmd0aFxuICAgICAgICApIHtcbiAgICAgICAgICBnbyhjaHVuay5jaGFyQ29kZUF0KHBvaW50Ll9idWZmZXJJbmRleCkpXG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGdvKGNodW5rKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBEZWFsIHdpdGggb25lIGNvZGUuXG4gICAqXG4gICAqIEBwYXJhbSB7Q29kZX0gY29kZVxuICAgKiAgIENvZGUuXG4gICAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gICAqICAgTm90aGluZy5cbiAgICovXG4gIGZ1bmN0aW9uIGdvKGNvZGUpIHtcbiAgICBhc3NlcnQoY29uc3VtZWQgPT09IHRydWUsICdleHBlY3RlZCBjaGFyYWN0ZXIgdG8gYmUgY29uc3VtZWQnKVxuICAgIGNvbnN1bWVkID0gdW5kZWZpbmVkXG4gICAgZGVidWcoJ21haW46IHBhc3NpbmcgYCVzYCB0byAlcycsIGNvZGUsIHN0YXRlICYmIHN0YXRlLm5hbWUpXG4gICAgZXhwZWN0ZWRDb2RlID0gY29kZVxuICAgIGFzc2VydCh0eXBlb2Ygc3RhdGUgPT09ICdmdW5jdGlvbicsICdleHBlY3RlZCBzdGF0ZScpXG4gICAgc3RhdGUgPSBzdGF0ZShjb2RlKVxuICB9XG5cbiAgLyoqIEB0eXBlIHtFZmZlY3RzWydjb25zdW1lJ119ICovXG4gIGZ1bmN0aW9uIGNvbnN1bWUoY29kZSkge1xuICAgIGFzc2VydChjb2RlID09PSBleHBlY3RlZENvZGUsICdleHBlY3RlZCBnaXZlbiBjb2RlIHRvIGVxdWFsIGV4cGVjdGVkIGNvZGUnKVxuXG4gICAgZGVidWcoJ2NvbnN1bWU6IGAlc2AnLCBjb2RlKVxuXG4gICAgYXNzZXJ0KFxuICAgICAgY29uc3VtZWQgPT09IHVuZGVmaW5lZCxcbiAgICAgICdleHBlY3RlZCBjb2RlIHRvIG5vdCBoYXZlIGJlZW4gY29uc3VtZWQ6IHRoaXMgbWlnaHQgYmUgYmVjYXVzZSBgcmV0dXJuIHgoY29kZSlgIGluc3RlYWQgb2YgYHJldHVybiB4YCB3YXMgdXNlZCdcbiAgICApXG4gICAgYXNzZXJ0KFxuICAgICAgY29kZSA9PT0gbnVsbFxuICAgICAgICA/IGNvbnRleHQuZXZlbnRzLmxlbmd0aCA9PT0gMCB8fFxuICAgICAgICAgICAgY29udGV4dC5ldmVudHNbY29udGV4dC5ldmVudHMubGVuZ3RoIC0gMV1bMF0gPT09ICdleGl0J1xuICAgICAgICA6IGNvbnRleHQuZXZlbnRzW2NvbnRleHQuZXZlbnRzLmxlbmd0aCAtIDFdWzBdID09PSAnZW50ZXInLFxuICAgICAgJ2V4cGVjdGVkIGxhc3QgdG9rZW4gdG8gYmUgb3BlbidcbiAgICApXG5cbiAgICBpZiAobWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpKSB7XG4gICAgICBwb2ludC5saW5lKytcbiAgICAgIHBvaW50LmNvbHVtbiA9IDFcbiAgICAgIHBvaW50Lm9mZnNldCArPSBjb2RlID09PSBjb2Rlcy5jYXJyaWFnZVJldHVybkxpbmVGZWVkID8gMiA6IDFcbiAgICAgIGFjY291bnRGb3JQb3RlbnRpYWxTa2lwKClcbiAgICAgIGRlYnVnKCdwb3NpdGlvbjogYWZ0ZXIgZW9sOiBgJWpgJywgcG9pbnQpXG4gICAgfSBlbHNlIGlmIChjb2RlICE9PSBjb2Rlcy52aXJ0dWFsU3BhY2UpIHtcbiAgICAgIHBvaW50LmNvbHVtbisrXG4gICAgICBwb2ludC5vZmZzZXQrK1xuICAgIH1cblxuICAgIC8vIE5vdCBpbiBhIHN0cmluZyBjaHVuay5cbiAgICBpZiAocG9pbnQuX2J1ZmZlckluZGV4IDwgMCkge1xuICAgICAgcG9pbnQuX2luZGV4KytcbiAgICB9IGVsc2Uge1xuICAgICAgcG9pbnQuX2J1ZmZlckluZGV4KytcblxuICAgICAgLy8gQXQgZW5kIG9mIHN0cmluZyBjaHVuay5cbiAgICAgIGlmIChcbiAgICAgICAgcG9pbnQuX2J1ZmZlckluZGV4ID09PVxuICAgICAgICAvLyBQb2ludHMgdy8gbm9uLW5lZ2F0aXZlIGBfYnVmZmVySW5kZXhgIHJlZmVyZW5jZVxuICAgICAgICAvLyBzdHJpbmdzLlxuICAgICAgICAvKiogQHR5cGUge3N0cmluZ30gKi8gKGNodW5rc1twb2ludC5faW5kZXhdKS5sZW5ndGhcbiAgICAgICkge1xuICAgICAgICBwb2ludC5fYnVmZmVySW5kZXggPSAtMVxuICAgICAgICBwb2ludC5faW5kZXgrK1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEV4cG9zZSB0aGUgcHJldmlvdXMgY2hhcmFjdGVyLlxuICAgIGNvbnRleHQucHJldmlvdXMgPSBjb2RlXG5cbiAgICAvLyBNYXJrIGFzIGNvbnN1bWVkLlxuICAgIGNvbnN1bWVkID0gdHJ1ZVxuICB9XG5cbiAgLyoqIEB0eXBlIHtFZmZlY3RzWydlbnRlciddfSAqL1xuICBmdW5jdGlvbiBlbnRlcih0eXBlLCBmaWVsZHMpIHtcbiAgICAvKiogQHR5cGUge1Rva2VufSAqL1xuICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgUGF0Y2ggaW5zdGVhZCBvZiBhc3NpZ24gcmVxdWlyZWQgZmllbGRzIHRvIGhlbHAgR0MuXG4gICAgY29uc3QgdG9rZW4gPSBmaWVsZHMgfHwge31cbiAgICB0b2tlbi50eXBlID0gdHlwZVxuICAgIHRva2VuLnN0YXJ0ID0gbm93KClcblxuICAgIGFzc2VydCh0eXBlb2YgdHlwZSA9PT0gJ3N0cmluZycsICdleHBlY3RlZCBzdHJpbmcgdHlwZScpXG4gICAgYXNzZXJ0KHR5cGUubGVuZ3RoID4gMCwgJ2V4cGVjdGVkIG5vbi1lbXB0eSBzdHJpbmcnKVxuICAgIGRlYnVnKCdlbnRlcjogYCVzYCcsIHR5cGUpXG5cbiAgICBjb250ZXh0LmV2ZW50cy5wdXNoKFsnZW50ZXInLCB0b2tlbiwgY29udGV4dF0pXG5cbiAgICBzdGFjay5wdXNoKHRva2VuKVxuXG4gICAgcmV0dXJuIHRva2VuXG4gIH1cblxuICAvKiogQHR5cGUge0VmZmVjdHNbJ2V4aXQnXX0gKi9cbiAgZnVuY3Rpb24gZXhpdCh0eXBlKSB7XG4gICAgYXNzZXJ0KHR5cGVvZiB0eXBlID09PSAnc3RyaW5nJywgJ2V4cGVjdGVkIHN0cmluZyB0eXBlJylcbiAgICBhc3NlcnQodHlwZS5sZW5ndGggPiAwLCAnZXhwZWN0ZWQgbm9uLWVtcHR5IHN0cmluZycpXG5cbiAgICBjb25zdCB0b2tlbiA9IHN0YWNrLnBvcCgpXG4gICAgYXNzZXJ0KHRva2VuLCAnY2Fubm90IGNsb3NlIHcvbyBvcGVuIHRva2VucycpXG4gICAgdG9rZW4uZW5kID0gbm93KClcblxuICAgIGFzc2VydCh0eXBlID09PSB0b2tlbi50eXBlLCAnZXhwZWN0ZWQgZXhpdCB0b2tlbiB0byBtYXRjaCBjdXJyZW50IHRva2VuJylcblxuICAgIGFzc2VydChcbiAgICAgICEoXG4gICAgICAgIHRva2VuLnN0YXJ0Ll9pbmRleCA9PT0gdG9rZW4uZW5kLl9pbmRleCAmJlxuICAgICAgICB0b2tlbi5zdGFydC5fYnVmZmVySW5kZXggPT09IHRva2VuLmVuZC5fYnVmZmVySW5kZXhcbiAgICAgICksXG4gICAgICAnZXhwZWN0ZWQgbm9uLWVtcHR5IHRva2VuIChgJyArIHR5cGUgKyAnYCknXG4gICAgKVxuXG4gICAgZGVidWcoJ2V4aXQ6IGAlc2AnLCB0b2tlbi50eXBlKVxuICAgIGNvbnRleHQuZXZlbnRzLnB1c2goWydleGl0JywgdG9rZW4sIGNvbnRleHRdKVxuXG4gICAgcmV0dXJuIHRva2VuXG4gIH1cblxuICAvKipcbiAgICogVXNlIHJlc3VsdHMuXG4gICAqXG4gICAqIEB0eXBlIHtSZXR1cm5IYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBvbnN1Y2Nlc3NmdWxjb25zdHJ1Y3QoY29uc3RydWN0LCBpbmZvKSB7XG4gICAgYWRkUmVzdWx0KGNvbnN0cnVjdCwgaW5mby5mcm9tKVxuICB9XG5cbiAgLyoqXG4gICAqIERpc2NhcmQgcmVzdWx0cy5cbiAgICpcbiAgICogQHR5cGUge1JldHVybkhhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIG9uc3VjY2Vzc2Z1bGNoZWNrKF8sIGluZm8pIHtcbiAgICBpbmZvLnJlc3RvcmUoKVxuICB9XG5cbiAgLyoqXG4gICAqIEZhY3RvcnkgdG8gYXR0ZW1wdC9jaGVjay9pbnRlcnJ1cHQuXG4gICAqXG4gICAqIEBwYXJhbSB7UmV0dXJuSGFuZGxlfSBvbnJldHVyblxuICAgKiAgIENhbGxiYWNrLlxuICAgKiBAcGFyYW0ge3tpbnRlcnJ1cHQ/OiBib29sZWFuIHwgdW5kZWZpbmVkfSB8IHVuZGVmaW5lZH0gW2ZpZWxkc11cbiAgICogICBGaWVsZHMuXG4gICAqL1xuICBmdW5jdGlvbiBjb25zdHJ1Y3RGYWN0b3J5KG9ucmV0dXJuLCBmaWVsZHMpIHtcbiAgICByZXR1cm4gaG9va1xuXG4gICAgLyoqXG4gICAgICogSGFuZGxlIGVpdGhlciBhbiBvYmplY3QgbWFwcGluZyBjb2RlcyB0byBjb25zdHJ1Y3RzLCBhIGxpc3Qgb2ZcbiAgICAgKiBjb25zdHJ1Y3RzLCBvciBhIHNpbmdsZSBjb25zdHJ1Y3QuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge0FycmF5PENvbnN0cnVjdD4gfCBDb25zdHJ1Y3RSZWNvcmQgfCBDb25zdHJ1Y3R9IGNvbnN0cnVjdHNcbiAgICAgKiAgIENvbnN0cnVjdHMuXG4gICAgICogQHBhcmFtIHtTdGF0ZX0gcmV0dXJuU3RhdGVcbiAgICAgKiAgIFN0YXRlLlxuICAgICAqIEBwYXJhbSB7U3RhdGUgfCB1bmRlZmluZWR9IFtib2d1c1N0YXRlXVxuICAgICAqICAgU3RhdGUuXG4gICAgICogQHJldHVybnMge1N0YXRlfVxuICAgICAqICAgU3RhdGUuXG4gICAgICovXG4gICAgZnVuY3Rpb24gaG9vayhjb25zdHJ1Y3RzLCByZXR1cm5TdGF0ZSwgYm9ndXNTdGF0ZSkge1xuICAgICAgLyoqIEB0eXBlIHtSZWFkb25seUFycmF5PENvbnN0cnVjdD59ICovXG4gICAgICBsZXQgbGlzdE9mQ29uc3RydWN0c1xuICAgICAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gICAgICBsZXQgY29uc3RydWN0SW5kZXhcbiAgICAgIC8qKiBAdHlwZSB7Q29uc3RydWN0fSAqL1xuICAgICAgbGV0IGN1cnJlbnRDb25zdHJ1Y3RcbiAgICAgIC8qKiBAdHlwZSB7SW5mb30gKi9cbiAgICAgIGxldCBpbmZvXG5cbiAgICAgIHJldHVybiBBcnJheS5pc0FycmF5KGNvbnN0cnVjdHMpXG4gICAgICAgID8gLyogYzggaWdub3JlIG5leHQgMSAqL1xuICAgICAgICAgIGhhbmRsZUxpc3RPZkNvbnN0cnVjdHMoY29uc3RydWN0cylcbiAgICAgICAgOiAndG9rZW5pemUnIGluIGNvbnN0cnVjdHNcbiAgICAgICAgICA/IC8vIExvb2tzIGxpa2UgYSBjb25zdHJ1Y3QuXG4gICAgICAgICAgICBoYW5kbGVMaXN0T2ZDb25zdHJ1Y3RzKFsvKiogQHR5cGUge0NvbnN0cnVjdH0gKi8gKGNvbnN0cnVjdHMpXSlcbiAgICAgICAgICA6IGhhbmRsZU1hcE9mQ29uc3RydWN0cyhjb25zdHJ1Y3RzKVxuXG4gICAgICAvKipcbiAgICAgICAqIEhhbmRsZSBhIGxpc3Qgb2YgY29uc3RydWN0LlxuICAgICAgICpcbiAgICAgICAqIEBwYXJhbSB7Q29uc3RydWN0UmVjb3JkfSBtYXBcbiAgICAgICAqICAgQ29uc3RydWN0cy5cbiAgICAgICAqIEByZXR1cm5zIHtTdGF0ZX1cbiAgICAgICAqICAgU3RhdGUuXG4gICAgICAgKi9cbiAgICAgIGZ1bmN0aW9uIGhhbmRsZU1hcE9mQ29uc3RydWN0cyhtYXApIHtcbiAgICAgICAgcmV0dXJuIHN0YXJ0XG5cbiAgICAgICAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgICAgICAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgICAgICAgIGNvbnN0IGxlZnQgPSBjb2RlICE9PSBudWxsICYmIG1hcFtjb2RlXVxuICAgICAgICAgIGNvbnN0IGFsbCA9IGNvZGUgIT09IG51bGwgJiYgbWFwLm51bGxcbiAgICAgICAgICBjb25zdCBsaXN0ID0gW1xuICAgICAgICAgICAgLy8gVG8gZG86IGFkZCBtb3JlIGV4dGVuc2lvbiB0ZXN0cy5cbiAgICAgICAgICAgIC8qIGM4IGlnbm9yZSBuZXh0IDIgKi9cbiAgICAgICAgICAgIC4uLihBcnJheS5pc0FycmF5KGxlZnQpID8gbGVmdCA6IGxlZnQgPyBbbGVmdF0gOiBbXSksXG4gICAgICAgICAgICAuLi4oQXJyYXkuaXNBcnJheShhbGwpID8gYWxsIDogYWxsID8gW2FsbF0gOiBbXSlcbiAgICAgICAgICBdXG5cbiAgICAgICAgICByZXR1cm4gaGFuZGxlTGlzdE9mQ29uc3RydWN0cyhsaXN0KShjb2RlKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8qKlxuICAgICAgICogSGFuZGxlIGEgbGlzdCBvZiBjb25zdHJ1Y3QuXG4gICAgICAgKlxuICAgICAgICogQHBhcmFtIHtSZWFkb25seUFycmF5PENvbnN0cnVjdD59IGxpc3RcbiAgICAgICAqICAgQ29uc3RydWN0cy5cbiAgICAgICAqIEByZXR1cm5zIHtTdGF0ZX1cbiAgICAgICAqICAgU3RhdGUuXG4gICAgICAgKi9cbiAgICAgIGZ1bmN0aW9uIGhhbmRsZUxpc3RPZkNvbnN0cnVjdHMobGlzdCkge1xuICAgICAgICBsaXN0T2ZDb25zdHJ1Y3RzID0gbGlzdFxuICAgICAgICBjb25zdHJ1Y3RJbmRleCA9IDBcblxuICAgICAgICBpZiAobGlzdC5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICBhc3NlcnQoYm9ndXNTdGF0ZSwgJ2V4cGVjdGVkIGBib2d1c1N0YXRlYCB0byBiZSBnaXZlbicpXG4gICAgICAgICAgcmV0dXJuIGJvZ3VzU3RhdGVcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBoYW5kbGVDb25zdHJ1Y3QobGlzdFtjb25zdHJ1Y3RJbmRleF0pXG4gICAgICB9XG5cbiAgICAgIC8qKlxuICAgICAgICogSGFuZGxlIGEgc2luZ2xlIGNvbnN0cnVjdC5cbiAgICAgICAqXG4gICAgICAgKiBAcGFyYW0ge0NvbnN0cnVjdH0gY29uc3RydWN0XG4gICAgICAgKiAgIENvbnN0cnVjdC5cbiAgICAgICAqIEByZXR1cm5zIHtTdGF0ZX1cbiAgICAgICAqICAgU3RhdGUuXG4gICAgICAgKi9cbiAgICAgIGZ1bmN0aW9uIGhhbmRsZUNvbnN0cnVjdChjb25zdHJ1Y3QpIHtcbiAgICAgICAgcmV0dXJuIHN0YXJ0XG5cbiAgICAgICAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgICAgICAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgICAgICAgIC8vIFRvIGRvOiBub3QgbmVlZGVkIHRvIHN0b3JlIGlmIHRoZXJlIGlzIG5vIGJvZ3VzIHN0YXRlLCBwcm9iYWJseT9cbiAgICAgICAgICAvLyBDdXJyZW50bHkgZG9lc27igJl0IHdvcmsgYmVjYXVzZSBgaW5zcGVjdGAgaW4gZG9jdW1lbnQgZG9lcyBhIGNoZWNrXG4gICAgICAgICAgLy8gdy9vIGEgYm9ndXMsIHdoaWNoIGRvZXNu4oCZdCBtYWtlIHNlbnNlLiBCdXQgaXQgZG9lcyBzZWVtIHRvIGhlbHAgcGVyZlxuICAgICAgICAgIC8vIGJ5IG5vdCBzdG9yaW5nLlxuICAgICAgICAgIGluZm8gPSBzdG9yZSgpXG4gICAgICAgICAgY3VycmVudENvbnN0cnVjdCA9IGNvbnN0cnVjdFxuXG4gICAgICAgICAgaWYgKCFjb25zdHJ1Y3QucGFydGlhbCkge1xuICAgICAgICAgICAgY29udGV4dC5jdXJyZW50Q29uc3RydWN0ID0gY29uc3RydWN0XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gQWx3YXlzIHBvcHVsYXRlZCBieSBkZWZhdWx0cy5cbiAgICAgICAgICBhc3NlcnQoXG4gICAgICAgICAgICBjb250ZXh0LnBhcnNlci5jb25zdHJ1Y3RzLmRpc2FibGUubnVsbCxcbiAgICAgICAgICAgICdleHBlY3RlZCBgZGlzYWJsZS5udWxsYCB0byBiZSBwb3B1bGF0ZWQnXG4gICAgICAgICAgKVxuXG4gICAgICAgICAgaWYgKFxuICAgICAgICAgICAgY29uc3RydWN0Lm5hbWUgJiZcbiAgICAgICAgICAgIGNvbnRleHQucGFyc2VyLmNvbnN0cnVjdHMuZGlzYWJsZS5udWxsLmluY2x1ZGVzKGNvbnN0cnVjdC5uYW1lKVxuICAgICAgICAgICkge1xuICAgICAgICAgICAgcmV0dXJuIG5vayhjb2RlKVxuICAgICAgICAgIH1cblxuICAgICAgICAgIHJldHVybiBjb25zdHJ1Y3QudG9rZW5pemUuY2FsbChcbiAgICAgICAgICAgIC8vIElmIHdlIGRvIGhhdmUgZmllbGRzLCBjcmVhdGUgYW4gb2JqZWN0IHcvIGBjb250ZXh0YCBhcyBpdHNcbiAgICAgICAgICAgIC8vIHByb3RvdHlwZS5cbiAgICAgICAgICAgIC8vIFRoaXMgYWxsb3dzIGEg4oCcbGl2ZSBiaW5kaW5n4oCdLCB3aGljaCBpcyBuZWVkZWQgZm9yIGBpbnRlcnJ1cHRgLlxuICAgICAgICAgICAgZmllbGRzID8gT2JqZWN0LmFzc2lnbihPYmplY3QuY3JlYXRlKGNvbnRleHQpLCBmaWVsZHMpIDogY29udGV4dCxcbiAgICAgICAgICAgIGVmZmVjdHMsXG4gICAgICAgICAgICBvayxcbiAgICAgICAgICAgIG5va1xuICAgICAgICAgICkoY29kZSlcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICAgICAgZnVuY3Rpb24gb2soY29kZSkge1xuICAgICAgICBhc3NlcnQoY29kZSA9PT0gZXhwZWN0ZWRDb2RlLCAnZXhwZWN0ZWQgY29kZScpXG4gICAgICAgIGNvbnN1bWVkID0gdHJ1ZVxuICAgICAgICBvbnJldHVybihjdXJyZW50Q29uc3RydWN0LCBpbmZvKVxuICAgICAgICByZXR1cm4gcmV0dXJuU3RhdGVcbiAgICAgIH1cblxuICAgICAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgICAgIGZ1bmN0aW9uIG5vayhjb2RlKSB7XG4gICAgICAgIGFzc2VydChjb2RlID09PSBleHBlY3RlZENvZGUsICdleHBlY3RlZCBjb2RlJylcbiAgICAgICAgY29uc3VtZWQgPSB0cnVlXG4gICAgICAgIGluZm8ucmVzdG9yZSgpXG5cbiAgICAgICAgaWYgKCsrY29uc3RydWN0SW5kZXggPCBsaXN0T2ZDb25zdHJ1Y3RzLmxlbmd0aCkge1xuICAgICAgICAgIHJldHVybiBoYW5kbGVDb25zdHJ1Y3QobGlzdE9mQ29uc3RydWN0c1tjb25zdHJ1Y3RJbmRleF0pXG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gYm9ndXNTdGF0ZVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBAcGFyYW0ge0NvbnN0cnVjdH0gY29uc3RydWN0XG4gICAqICAgQ29uc3RydWN0LlxuICAgKiBAcGFyYW0ge251bWJlcn0gZnJvbVxuICAgKiAgIEZyb20uXG4gICAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gICAqICAgTm90aGluZy5cbiAgICovXG4gIGZ1bmN0aW9uIGFkZFJlc3VsdChjb25zdHJ1Y3QsIGZyb20pIHtcbiAgICBpZiAoY29uc3RydWN0LnJlc29sdmVBbGwgJiYgIXJlc29sdmVBbGxDb25zdHJ1Y3RzLmluY2x1ZGVzKGNvbnN0cnVjdCkpIHtcbiAgICAgIHJlc29sdmVBbGxDb25zdHJ1Y3RzLnB1c2goY29uc3RydWN0KVxuICAgIH1cblxuICAgIGlmIChjb25zdHJ1Y3QucmVzb2x2ZSkge1xuICAgICAgc3BsaWNlKFxuICAgICAgICBjb250ZXh0LmV2ZW50cyxcbiAgICAgICAgZnJvbSxcbiAgICAgICAgY29udGV4dC5ldmVudHMubGVuZ3RoIC0gZnJvbSxcbiAgICAgICAgY29uc3RydWN0LnJlc29sdmUoY29udGV4dC5ldmVudHMuc2xpY2UoZnJvbSksIGNvbnRleHQpXG4gICAgICApXG4gICAgfVxuXG4gICAgaWYgKGNvbnN0cnVjdC5yZXNvbHZlVG8pIHtcbiAgICAgIGNvbnRleHQuZXZlbnRzID0gY29uc3RydWN0LnJlc29sdmVUbyhjb250ZXh0LmV2ZW50cywgY29udGV4dClcbiAgICB9XG5cbiAgICBhc3NlcnQoXG4gICAgICBjb25zdHJ1Y3QucGFydGlhbCB8fFxuICAgICAgICBjb250ZXh0LmV2ZW50cy5sZW5ndGggPT09IDAgfHxcbiAgICAgICAgY29udGV4dC5ldmVudHNbY29udGV4dC5ldmVudHMubGVuZ3RoIC0gMV1bMF0gPT09ICdleGl0JyxcbiAgICAgICdleHBlY3RlZCBsYXN0IHRva2VuIHRvIGVuZCdcbiAgICApXG4gIH1cblxuICAvKipcbiAgICogU3RvcmUgc3RhdGUuXG4gICAqXG4gICAqIEByZXR1cm5zIHtJbmZvfVxuICAgKiAgIEluZm8uXG4gICAqL1xuICBmdW5jdGlvbiBzdG9yZSgpIHtcbiAgICBjb25zdCBzdGFydFBvaW50ID0gbm93KClcbiAgICBjb25zdCBzdGFydFByZXZpb3VzID0gY29udGV4dC5wcmV2aW91c1xuICAgIGNvbnN0IHN0YXJ0Q3VycmVudENvbnN0cnVjdCA9IGNvbnRleHQuY3VycmVudENvbnN0cnVjdFxuICAgIGNvbnN0IHN0YXJ0RXZlbnRzSW5kZXggPSBjb250ZXh0LmV2ZW50cy5sZW5ndGhcbiAgICBjb25zdCBzdGFydFN0YWNrID0gQXJyYXkuZnJvbShzdGFjaylcblxuICAgIHJldHVybiB7ZnJvbTogc3RhcnRFdmVudHNJbmRleCwgcmVzdG9yZX1cblxuICAgIC8qKlxuICAgICAqIFJlc3RvcmUgc3RhdGUuXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyB7dW5kZWZpbmVkfVxuICAgICAqICAgTm90aGluZy5cbiAgICAgKi9cbiAgICBmdW5jdGlvbiByZXN0b3JlKCkge1xuICAgICAgcG9pbnQgPSBzdGFydFBvaW50XG4gICAgICBjb250ZXh0LnByZXZpb3VzID0gc3RhcnRQcmV2aW91c1xuICAgICAgY29udGV4dC5jdXJyZW50Q29uc3RydWN0ID0gc3RhcnRDdXJyZW50Q29uc3RydWN0XG4gICAgICBjb250ZXh0LmV2ZW50cy5sZW5ndGggPSBzdGFydEV2ZW50c0luZGV4XG4gICAgICBzdGFjayA9IHN0YXJ0U3RhY2tcbiAgICAgIGFjY291bnRGb3JQb3RlbnRpYWxTa2lwKClcbiAgICAgIGRlYnVnKCdwb3NpdGlvbjogcmVzdG9yZTogYCVqYCcsIHBvaW50KVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBNb3ZlIHRoZSBjdXJyZW50IHBvaW50IGEgYml0IGZvcndhcmQgaW4gdGhlIGxpbmUgd2hlbiBpdOKAmXMgb24gYSBjb2x1bW5cbiAgICogc2tpcC5cbiAgICpcbiAgICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAgICogICBOb3RoaW5nLlxuICAgKi9cbiAgZnVuY3Rpb24gYWNjb3VudEZvclBvdGVudGlhbFNraXAoKSB7XG4gICAgaWYgKHBvaW50LmxpbmUgaW4gY29sdW1uU3RhcnQgJiYgcG9pbnQuY29sdW1uIDwgMikge1xuICAgICAgcG9pbnQuY29sdW1uID0gY29sdW1uU3RhcnRbcG9pbnQubGluZV1cbiAgICAgIHBvaW50Lm9mZnNldCArPSBjb2x1bW5TdGFydFtwb2ludC5saW5lXSAtIDFcbiAgICB9XG4gIH1cbn1cblxuLyoqXG4gKiBHZXQgdGhlIGNodW5rcyBmcm9tIGEgc2xpY2Ugb2YgY2h1bmtzIGluIHRoZSByYW5nZSBvZiBhIHRva2VuLlxuICpcbiAqIEBwYXJhbSB7UmVhZG9ubHlBcnJheTxDaHVuaz59IGNodW5rc1xuICogICBDaHVua3MuXG4gKiBAcGFyYW0ge1BpY2s8VG9rZW4sICdlbmQnIHwgJ3N0YXJ0Jz59IHRva2VuXG4gKiAgIFRva2VuLlxuICogQHJldHVybnMge0FycmF5PENodW5rPn1cbiAqICAgQ2h1bmtzLlxuICovXG5mdW5jdGlvbiBzbGljZUNodW5rcyhjaHVua3MsIHRva2VuKSB7XG4gIGNvbnN0IHN0YXJ0SW5kZXggPSB0b2tlbi5zdGFydC5faW5kZXhcbiAgY29uc3Qgc3RhcnRCdWZmZXJJbmRleCA9IHRva2VuLnN0YXJ0Ll9idWZmZXJJbmRleFxuICBjb25zdCBlbmRJbmRleCA9IHRva2VuLmVuZC5faW5kZXhcbiAgY29uc3QgZW5kQnVmZmVySW5kZXggPSB0b2tlbi5lbmQuX2J1ZmZlckluZGV4XG4gIC8qKiBAdHlwZSB7QXJyYXk8Q2h1bms+fSAqL1xuICBsZXQgdmlld1xuXG4gIGlmIChzdGFydEluZGV4ID09PSBlbmRJbmRleCkge1xuICAgIGFzc2VydChlbmRCdWZmZXJJbmRleCA+IC0xLCAnZXhwZWN0ZWQgbm9uLW5lZ2F0aXZlIGVuZCBidWZmZXIgaW5kZXgnKVxuICAgIGFzc2VydChzdGFydEJ1ZmZlckluZGV4ID4gLTEsICdleHBlY3RlZCBub24tbmVnYXRpdmUgc3RhcnQgYnVmZmVyIGluZGV4JylcbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yIGBfYnVmZmVySW5kZXhgIGlzIHVzZWQgb24gc3RyaW5nIGNodW5rcy5cbiAgICB2aWV3ID0gW2NodW5rc1tzdGFydEluZGV4XS5zbGljZShzdGFydEJ1ZmZlckluZGV4LCBlbmRCdWZmZXJJbmRleCldXG4gIH0gZWxzZSB7XG4gICAgdmlldyA9IGNodW5rcy5zbGljZShzdGFydEluZGV4LCBlbmRJbmRleClcblxuICAgIGlmIChzdGFydEJ1ZmZlckluZGV4ID4gLTEpIHtcbiAgICAgIGNvbnN0IGhlYWQgPSB2aWV3WzBdXG4gICAgICBpZiAodHlwZW9mIGhlYWQgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgIHZpZXdbMF0gPSBoZWFkLnNsaWNlKHN0YXJ0QnVmZmVySW5kZXgpXG4gICAgICAgIC8qIGM4IGlnbm9yZSBuZXh0IDQgLS0gdXNlZCB0byBiZSB1c2VkLCBubyBsb25nZXIgKi9cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGFzc2VydChzdGFydEJ1ZmZlckluZGV4ID09PSAwLCAnZXhwZWN0ZWQgYHN0YXJ0QnVmZmVySW5kZXhgIHRvIGJlIGAwYCcpXG4gICAgICAgIHZpZXcuc2hpZnQoKVxuICAgICAgfVxuICAgIH1cblxuICAgIGlmIChlbmRCdWZmZXJJbmRleCA+IDApIHtcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgYF9idWZmZXJJbmRleGAgaXMgdXNlZCBvbiBzdHJpbmcgY2h1bmtzLlxuICAgICAgdmlldy5wdXNoKGNodW5rc1tlbmRJbmRleF0uc2xpY2UoMCwgZW5kQnVmZmVySW5kZXgpKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiB2aWV3XG59XG5cbi8qKlxuICogR2V0IHRoZSBzdHJpbmcgdmFsdWUgb2YgYSBzbGljZSBvZiBjaHVua3MuXG4gKlxuICogQHBhcmFtIHtSZWFkb25seUFycmF5PENodW5rPn0gY2h1bmtzXG4gKiAgIENodW5rcy5cbiAqIEBwYXJhbSB7Ym9vbGVhbiB8IHVuZGVmaW5lZH0gW2V4cGFuZFRhYnM9ZmFsc2VdXG4gKiAgIFdoZXRoZXIgdG8gZXhwYW5kIHRhYnMgKGRlZmF1bHQ6IGBmYWxzZWApLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgUmVzdWx0LlxuICovXG5mdW5jdGlvbiBzZXJpYWxpemVDaHVua3MoY2h1bmtzLCBleHBhbmRUYWJzKSB7XG4gIGxldCBpbmRleCA9IC0xXG4gIC8qKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn0gKi9cbiAgY29uc3QgcmVzdWx0ID0gW11cbiAgLyoqIEB0eXBlIHtib29sZWFuIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgYXRUYWJcblxuICB3aGlsZSAoKytpbmRleCA8IGNodW5rcy5sZW5ndGgpIHtcbiAgICBjb25zdCBjaHVuayA9IGNodW5rc1tpbmRleF1cbiAgICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgICBsZXQgdmFsdWVcblxuICAgIGlmICh0eXBlb2YgY2h1bmsgPT09ICdzdHJpbmcnKSB7XG4gICAgICB2YWx1ZSA9IGNodW5rXG4gICAgfSBlbHNlXG4gICAgICBzd2l0Y2ggKGNodW5rKSB7XG4gICAgICAgIGNhc2UgY29kZXMuY2FycmlhZ2VSZXR1cm46IHtcbiAgICAgICAgICB2YWx1ZSA9IHZhbHVlcy5jclxuXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgfVxuXG4gICAgICAgIGNhc2UgY29kZXMubGluZUZlZWQ6IHtcbiAgICAgICAgICB2YWx1ZSA9IHZhbHVlcy5sZlxuXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgfVxuXG4gICAgICAgIGNhc2UgY29kZXMuY2FycmlhZ2VSZXR1cm5MaW5lRmVlZDoge1xuICAgICAgICAgIHZhbHVlID0gdmFsdWVzLmNyICsgdmFsdWVzLmxmXG5cbiAgICAgICAgICBicmVha1xuICAgICAgICB9XG5cbiAgICAgICAgY2FzZSBjb2Rlcy5ob3Jpem9udGFsVGFiOiB7XG4gICAgICAgICAgdmFsdWUgPSBleHBhbmRUYWJzID8gdmFsdWVzLnNwYWNlIDogdmFsdWVzLmh0XG5cbiAgICAgICAgICBicmVha1xuICAgICAgICB9XG5cbiAgICAgICAgY2FzZSBjb2Rlcy52aXJ0dWFsU3BhY2U6IHtcbiAgICAgICAgICBpZiAoIWV4cGFuZFRhYnMgJiYgYXRUYWIpIGNvbnRpbnVlXG4gICAgICAgICAgdmFsdWUgPSB2YWx1ZXMuc3BhY2VcblxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cblxuICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgYXNzZXJ0KHR5cGVvZiBjaHVuayA9PT0gJ251bWJlcicsICdleHBlY3RlZCBudW1iZXInKVxuICAgICAgICAgIC8vIEN1cnJlbnRseSBvbmx5IHJlcGxhY2VtZW50IGNoYXJhY3Rlci5cbiAgICAgICAgICB2YWx1ZSA9IFN0cmluZy5mcm9tQ2hhckNvZGUoY2h1bmspXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgIGF0VGFiID0gY2h1bmsgPT09IGNvZGVzLmhvcml6b250YWxUYWJcbiAgICByZXN1bHQucHVzaCh2YWx1ZSlcbiAgfVxuXG4gIHJldHVybiByZXN1bHQuam9pbignJylcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVEZWJ1ZyIsIm9rIiwiYXNzZXJ0IiwibWFya2Rvd25MaW5lRW5kaW5nIiwicHVzaCIsInNwbGljZSIsInJlc29sdmVBbGwiLCJjb2RlcyIsInZhbHVlcyIsImRlYnVnIiwiY3JlYXRlVG9rZW5pemVyIiwicGFyc2VyIiwiaW5pdGlhbGl6ZSIsImZyb20iLCJwb2ludCIsIl9idWZmZXJJbmRleCIsIl9pbmRleCIsImxpbmUiLCJjb2x1bW4iLCJvZmZzZXQiLCJjb2x1bW5TdGFydCIsInJlc29sdmVBbGxDb25zdHJ1Y3RzIiwiY2h1bmtzIiwic3RhY2siLCJjb25zdW1lZCIsImVmZmVjdHMiLCJhdHRlbXB0IiwiY29uc3RydWN0RmFjdG9yeSIsIm9uc3VjY2Vzc2Z1bGNvbnN0cnVjdCIsImNoZWNrIiwib25zdWNjZXNzZnVsY2hlY2siLCJjb25zdW1lIiwiZW50ZXIiLCJleGl0IiwiaW50ZXJydXB0IiwiY29udGV4dCIsImNvZGUiLCJlb2YiLCJjb250YWluZXJTdGF0ZSIsImRlZmluZVNraXAiLCJldmVudHMiLCJub3ciLCJwcmV2aW91cyIsInNsaWNlU2VyaWFsaXplIiwic2xpY2VTdHJlYW0iLCJ3cml0ZSIsInN0YXRlIiwidG9rZW5pemUiLCJjYWxsIiwiZXhwZWN0ZWRDb2RlIiwic2xpY2UiLCJtYWluIiwibGVuZ3RoIiwiYWRkUmVzdWx0IiwidG9rZW4iLCJleHBhbmRUYWJzIiwic2VyaWFsaXplQ2h1bmtzIiwic2xpY2VDaHVua3MiLCJ2YWx1ZSIsImFjY291bnRGb3JQb3RlbnRpYWxTa2lwIiwiY2h1bmtJbmRleCIsImNodW5rIiwiZ28iLCJjaGFyQ29kZUF0IiwidW5kZWZpbmVkIiwibmFtZSIsImNhcnJpYWdlUmV0dXJuTGluZUZlZWQiLCJ2aXJ0dWFsU3BhY2UiLCJ0eXBlIiwiZmllbGRzIiwic3RhcnQiLCJwb3AiLCJlbmQiLCJjb25zdHJ1Y3QiLCJpbmZvIiwiXyIsInJlc3RvcmUiLCJvbnJldHVybiIsImhvb2siLCJjb25zdHJ1Y3RzIiwicmV0dXJuU3RhdGUiLCJib2d1c1N0YXRlIiwibGlzdE9mQ29uc3RydWN0cyIsImNvbnN0cnVjdEluZGV4IiwiY3VycmVudENvbnN0cnVjdCIsIkFycmF5IiwiaXNBcnJheSIsImhhbmRsZUxpc3RPZkNvbnN0cnVjdHMiLCJoYW5kbGVNYXBPZkNvbnN0cnVjdHMiLCJtYXAiLCJsZWZ0IiwiYWxsIiwibnVsbCIsImxpc3QiLCJoYW5kbGVDb25zdHJ1Y3QiLCJzdG9yZSIsInBhcnRpYWwiLCJkaXNhYmxlIiwiaW5jbHVkZXMiLCJub2siLCJPYmplY3QiLCJhc3NpZ24iLCJjcmVhdGUiLCJyZXNvbHZlIiwicmVzb2x2ZVRvIiwic3RhcnRQb2ludCIsInN0YXJ0UHJldmlvdXMiLCJzdGFydEN1cnJlbnRDb25zdHJ1Y3QiLCJzdGFydEV2ZW50c0luZGV4Iiwic3RhcnRTdGFjayIsInN0YXJ0SW5kZXgiLCJzdGFydEJ1ZmZlckluZGV4IiwiZW5kSW5kZXgiLCJlbmRCdWZmZXJJbmRleCIsInZpZXciLCJoZWFkIiwic2hpZnQiLCJpbmRleCIsInJlc3VsdCIsImF0VGFiIiwiY2FycmlhZ2VSZXR1cm4iLCJjciIsImxpbmVGZWVkIiwibGYiLCJob3Jpem9udGFsVGFiIiwic3BhY2UiLCJodCIsIlN0cmluZyIsImZyb21DaGFyQ29kZSIsImpvaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/content.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/content.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */ \n\n\n\n/** @type {InitialConstruct} */ const content = {\n    tokenize: initializeContent\n};\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */ function initializeContent(effects) {\n    const contentStart = effects.attempt(this.parser.constructs.contentInitial, afterContentStartConstruct, paragraphInitial);\n    /** @type {Token} */ let previous;\n    return contentStart;\n    /** @type {State} */ function afterContentStartConstruct(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, contentStart, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix);\n    }\n    /** @type {State} */ function paragraphInitial(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code), \"expected anything other than a line ending or EOF\");\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph);\n        return lineStart(code);\n    }\n    /** @type {State} */ function lineStart(code) {\n        const token = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText, {\n            contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText,\n            previous\n        });\n        if (previous) {\n            previous.next = token;\n        }\n        previous = token;\n        return data(code);\n    }\n    /** @type {State} */ function data(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText);\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph);\n            effects.consume(code);\n            return;\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            effects.consume(code);\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText);\n            return lineStart;\n        }\n        // Data.\n        effects.consume(code);\n        return data;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/document.js":
/*!***************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/document.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   document: () => (/* binding */ document)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Construct,\n *   ContainerState,\n *   InitialConstruct,\n *   Initializer,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */ /**\n * @typedef {[Construct, ContainerState]} StackItem\n *   Construct and its state.\n */ \n\n\n\n\n/** @type {InitialConstruct} */ const document = {\n    tokenize: initializeDocument\n};\n/** @type {Construct} */ const containerConstruct = {\n    tokenize: tokenizeContainer\n};\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */ function initializeDocument(effects) {\n    const self = this;\n    /** @type {Array<StackItem>} */ const stack = [];\n    let continued = 0;\n    /** @type {TokenizeContext | undefined} */ let childFlow;\n    /** @type {Token | undefined} */ let childToken;\n    /** @type {number} */ let lineStartOffset;\n    return start;\n    /** @type {State} */ function start(code) {\n        // First we iterate through the open blocks, starting with the root\n        // document, and descending through last children down to the last open\n        // block.\n        // Each block imposes a condition that the line must satisfy if the block is\n        // to remain open.\n        // For example, a block quote requires a `>` character.\n        // A paragraph requires a non-blank line.\n        // In this phase we may match all or just some of the open blocks.\n        // But we cannot close unmatched blocks yet, because we may have a lazy\n        // continuation line.\n        if (continued < stack.length) {\n            const item = stack[continued];\n            self.containerState = item[1];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(item[0].continuation, \"expected `continuation` to be defined on container construct\");\n            return effects.attempt(item[0].continuation, documentContinue, checkNewContainers)(code);\n        }\n        // Done.\n        return checkNewContainers(code);\n    }\n    /** @type {State} */ function documentContinue(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined after continuation\");\n        continued++;\n        // Note: this field is called `_closeFlow` but it also closes containers.\n        // Perhaps a good idea to rename it but it’s already used in the wild by\n        // extensions.\n        if (self.containerState._closeFlow) {\n            self.containerState._closeFlow = undefined;\n            if (childFlow) {\n                closeFlow();\n            }\n            // Note: this algorithm for moving events around is similar to the\n            // algorithm when dealing with lazy lines in `writeToChild`.\n            const indexBeforeExits = self.events.length;\n            let indexBeforeFlow = indexBeforeExits;\n            /** @type {Point | undefined} */ let point;\n            // Find the flow chunk.\n            while(indexBeforeFlow--){\n                if (self.events[indexBeforeFlow][0] === \"exit\" && self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow) {\n                    point = self.events[indexBeforeFlow][1].end;\n                    break;\n                }\n            }\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, \"could not find previous flow chunk\");\n            exitContainers(continued);\n            // Fix positions.\n            let index = indexBeforeExits;\n            while(index < self.events.length){\n                self.events[index][1].end = {\n                    ...point\n                };\n                index++;\n            }\n            // Inject the exits earlier (they’re still also at the end).\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(self.events, indexBeforeFlow + 1, 0, self.events.slice(indexBeforeExits));\n            // Discard the duplicate exits.\n            self.events.length = index;\n            return checkNewContainers(code);\n        }\n        return start(code);\n    }\n    /** @type {State} */ function checkNewContainers(code) {\n        // Next, after consuming the continuation markers for existing blocks, we\n        // look for new block starts (e.g. `>` for a block quote).\n        // If we encounter a new block start, we close any blocks unmatched in\n        // step 1 before creating the new block as a child of the last matched\n        // block.\n        if (continued === stack.length) {\n            // No need to `check` whether there’s a container, of `exitContainers`\n            // would be moot.\n            // We can instead immediately `attempt` to parse one.\n            if (!childFlow) {\n                return documentContinued(code);\n            }\n            // If we have concrete content, such as block HTML or fenced code,\n            // we can’t have containers “pierce” into them, so we can immediately\n            // start.\n            if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n                return flowStart(code);\n            }\n            // If we do have flow, it could still be a blank line,\n            // but we’d be interrupting it w/ a new container if there’s a current\n            // construct.\n            // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n            // needed in micromark-extension-gfm-table@1.0.6).\n            self.interrupt = Boolean(childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack);\n        }\n        // Check if there is a new container.\n        self.containerState = {};\n        return effects.check(containerConstruct, thereIsANewContainer, thereIsNoNewContainer)(code);\n    }\n    /** @type {State} */ function thereIsANewContainer(code) {\n        if (childFlow) closeFlow();\n        exitContainers(continued);\n        return documentContinued(code);\n    }\n    /** @type {State} */ function thereIsNoNewContainer(code) {\n        self.parser.lazy[self.now().line] = continued !== stack.length;\n        lineStartOffset = self.now().offset;\n        return flowStart(code);\n    }\n    /** @type {State} */ function documentContinued(code) {\n        // Try new containers.\n        self.containerState = {};\n        return effects.attempt(containerConstruct, containerContinue, flowStart)(code);\n    }\n    /** @type {State} */ function containerContinue(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.currentConstruct, \"expected `currentConstruct` to be defined on tokenizer\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined on tokenizer\");\n        continued++;\n        stack.push([\n            self.currentConstruct,\n            self.containerState\n        ]);\n        // Try another.\n        return documentContinued(code);\n    }\n    /** @type {State} */ function flowStart(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n            if (childFlow) closeFlow();\n            exitContainers(0);\n            effects.consume(code);\n            return;\n        }\n        childFlow = childFlow || self.parser.flow(self.now());\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow, {\n            _tokenizer: childFlow,\n            contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.contentTypeFlow,\n            previous: childToken\n        });\n        return flowContinue(code);\n    }\n    /** @type {State} */ function flowContinue(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n            writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow), true);\n            exitContainers(0);\n            effects.consume(code);\n            return;\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n            effects.consume(code);\n            writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow));\n            // Get ready for the next line.\n            continued = 0;\n            self.interrupt = undefined;\n            return start;\n        }\n        effects.consume(code);\n        return flowContinue;\n    }\n    /**\n   * @param {Token} token\n   *   Token.\n   * @param {boolean | undefined} [endOfFile]\n   *   Whether the token is at the end of the file (default: `false`).\n   * @returns {undefined}\n   *   Nothing.\n   */ function writeToChild(token, endOfFile) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, \"expected `childFlow` to be defined when continuing\");\n        const stream = self.sliceStream(token);\n        if (endOfFile) stream.push(null);\n        token.previous = childToken;\n        if (childToken) childToken.next = token;\n        childToken = token;\n        childFlow.defineSkip(token.start);\n        childFlow.write(stream);\n        // Alright, so we just added a lazy line:\n        //\n        // ```markdown\n        // > a\n        // b.\n        //\n        // Or:\n        //\n        // > ~~~c\n        // d\n        //\n        // Or:\n        //\n        // > | e |\n        // f\n        // ```\n        //\n        // The construct in the second example (fenced code) does not accept lazy\n        // lines, so it marked itself as done at the end of its first line, and\n        // then the content construct parses `d`.\n        // Most constructs in markdown match on the first line: if the first line\n        // forms a construct, a non-lazy line can’t “unmake” it.\n        //\n        // The construct in the third example is potentially a GFM table, and\n        // those are *weird*.\n        // It *could* be a table, from the first line, if the following line\n        // matches a condition.\n        // In this case, that second line is lazy, which “unmakes” the first line\n        // and turns the whole into one content block.\n        //\n        // We’ve now parsed the non-lazy and the lazy line, and can figure out\n        // whether the lazy line started a new flow block.\n        // If it did, we exit the current containers between the two flow blocks.\n        if (self.parser.lazy[token.start.line]) {\n            let index = childFlow.events.length;\n            while(index--){\n                if (// The token starts before the line ending…\n                childFlow.events[index][1].start.offset < lineStartOffset && // …and either is not ended yet…\n                (!childFlow.events[index][1].end || // …or ends after it.\n                childFlow.events[index][1].end.offset > lineStartOffset)) {\n                    // Exit: there’s still something open, which means it’s a lazy line\n                    // part of something.\n                    return;\n                }\n            }\n            // Note: this algorithm for moving events around is similar to the\n            // algorithm when closing flow in `documentContinue`.\n            const indexBeforeExits = self.events.length;\n            let indexBeforeFlow = indexBeforeExits;\n            /** @type {boolean | undefined} */ let seen;\n            /** @type {Point | undefined} */ let point;\n            // Find the previous chunk (the one before the lazy line).\n            while(indexBeforeFlow--){\n                if (self.events[indexBeforeFlow][0] === \"exit\" && self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow) {\n                    if (seen) {\n                        point = self.events[indexBeforeFlow][1].end;\n                        break;\n                    }\n                    seen = true;\n                }\n            }\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, \"could not find previous flow chunk\");\n            exitContainers(continued);\n            // Fix positions.\n            index = indexBeforeExits;\n            while(index < self.events.length){\n                self.events[index][1].end = {\n                    ...point\n                };\n                index++;\n            }\n            // Inject the exits earlier (they’re still also at the end).\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(self.events, indexBeforeFlow + 1, 0, self.events.slice(indexBeforeExits));\n            // Discard the duplicate exits.\n            self.events.length = index;\n        }\n    }\n    /**\n   * @param {number} size\n   *   Size.\n   * @returns {undefined}\n   *   Nothing.\n   */ function exitContainers(size) {\n        let index = stack.length;\n        // Exit open containers.\n        while(index-- > size){\n            const entry = stack[index];\n            self.containerState = entry[1];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(entry[0].exit, \"expected `exit` to be defined on container construct\");\n            entry[0].exit.call(self, effects);\n        }\n        stack.length = size;\n    }\n    function closeFlow() {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined when closing flow\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, \"expected `childFlow` to be defined when closing it\");\n        childFlow.write([\n            micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof\n        ]);\n        childToken = undefined;\n        childFlow = undefined;\n        self.containerState._closeFlow = undefined;\n    }\n}\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n *   Tokenizer.\n */ function tokenizeContainer(effects, ok, nok) {\n    // Always populated by defaults.\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(this.parser.constructs.disable.null, \"expected `disable.null` to be populated\");\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, effects.attempt(this.parser.constructs.document, ok, nok), micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix, this.parser.constructs.disable.null.includes(\"codeIndented\") ? undefined : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/document.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/flow.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flow: () => (/* binding */ flow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/content.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */ \n\n\n\n\n/** @type {InitialConstruct} */ const flow = {\n    tokenize: initializeFlow\n};\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */ function initializeFlow(effects) {\n    const self = this;\n    const initial = effects.attempt(// Try to parse a blank line.\n    micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__.blankLine, atBlankEnding, // Try to parse initial flow (essentially, only code).\n    effects.attempt(this.parser.constructs.flowInitial, afterConstruct, (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__.factorySpace)(effects, effects.attempt(this.parser.constructs.flow, afterConstruct, effects.attempt(micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.content, afterConstruct)), micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix)));\n    return initial;\n    /** @type {State} */ function atBlankEnding(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank);\n        self.currentConstruct = undefined;\n        return initial;\n    }\n    /** @type {State} */ function afterConstruct(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        self.currentConstruct = undefined;\n        return initial;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvaW5pdGlhbGl6ZS9mbG93LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUE7Ozs7Ozs7Q0FPQyxHQUVrQztBQUN5QjtBQUNSO0FBQ087QUFDVDtBQUVsRCw2QkFBNkIsR0FDdEIsTUFBTVEsT0FBTztJQUFDQyxVQUFVQztBQUFjLEVBQUM7QUFFOUM7Ozs7O0NBS0MsR0FDRCxTQUFTQSxlQUFlQyxPQUFPO0lBQzdCLE1BQU1DLE9BQU8sSUFBSTtJQUNqQixNQUFNQyxVQUFVRixRQUFRRyxPQUFPLENBQzdCLDZCQUE2QjtJQUM3QlosZ0VBQVNBLEVBQ1RhLGVBQ0Esc0RBQXNEO0lBQ3RESixRQUFRRyxPQUFPLENBQ2IsSUFBSSxDQUFDRSxNQUFNLENBQUNDLFVBQVUsQ0FBQ0MsV0FBVyxFQUNsQ0MsZ0JBQ0FmLHFFQUFZQSxDQUNWTyxTQUNBQSxRQUFRRyxPQUFPLENBQ2IsSUFBSSxDQUFDRSxNQUFNLENBQUNDLFVBQVUsQ0FBQ1QsSUFBSSxFQUMzQlcsZ0JBQ0FSLFFBQVFHLE9BQU8sQ0FBQ1gsOERBQU9BLEVBQUVnQixrQkFFM0JaLHdEQUFLQSxDQUFDYSxVQUFVO0lBS3RCLE9BQU9QO0lBRVAsa0JBQWtCLEdBQ2xCLFNBQVNFLGNBQWNNLElBQUk7UUFDekJwQiwwQ0FBTUEsQ0FDSm9CLFNBQVNmLHdEQUFLQSxDQUFDZ0IsR0FBRyxJQUFJakIsNEVBQWtCQSxDQUFDZ0IsT0FDekM7UUFHRixJQUFJQSxTQUFTZix3REFBS0EsQ0FBQ2dCLEdBQUcsRUFBRTtZQUN0QlgsUUFBUVksT0FBTyxDQUFDRjtZQUNoQjtRQUNGO1FBRUFWLFFBQVFhLEtBQUssQ0FBQ2pCLHdEQUFLQSxDQUFDa0IsZUFBZTtRQUNuQ2QsUUFBUVksT0FBTyxDQUFDRjtRQUNoQlYsUUFBUWUsSUFBSSxDQUFDbkIsd0RBQUtBLENBQUNrQixlQUFlO1FBQ2xDYixLQUFLZSxnQkFBZ0IsR0FBR0M7UUFDeEIsT0FBT2Y7SUFDVDtJQUVBLGtCQUFrQixHQUNsQixTQUFTTSxlQUFlRSxJQUFJO1FBQzFCcEIsMENBQU1BLENBQ0pvQixTQUFTZix3REFBS0EsQ0FBQ2dCLEdBQUcsSUFBSWpCLDRFQUFrQkEsQ0FBQ2dCLE9BQ3pDO1FBR0YsSUFBSUEsU0FBU2Ysd0RBQUtBLENBQUNnQixHQUFHLEVBQUU7WUFDdEJYLFFBQVFZLE9BQU8sQ0FBQ0Y7WUFDaEI7UUFDRjtRQUVBVixRQUFRYSxLQUFLLENBQUNqQix3REFBS0EsQ0FBQ3NCLFVBQVU7UUFDOUJsQixRQUFRWSxPQUFPLENBQUNGO1FBQ2hCVixRQUFRZSxJQUFJLENBQUNuQix3REFBS0EsQ0FBQ3NCLFVBQVU7UUFDN0JqQixLQUFLZSxnQkFBZ0IsR0FBR0M7UUFDeEIsT0FBT2Y7SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvaW5pdGlhbGl6ZS9mbG93LmpzPzE2YTAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtcbiAqICAgSW5pdGlhbENvbnN0cnVjdCxcbiAqICAgSW5pdGlhbGl6ZXIsXG4gKiAgIFN0YXRlLFxuICogICBUb2tlbml6ZUNvbnRleHRcbiAqIH0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuaW1wb3J0IHtvayBhcyBhc3NlcnR9IGZyb20gJ2RldmxvcCdcbmltcG9ydCB7YmxhbmtMaW5lLCBjb250ZW50fSBmcm9tICdtaWNyb21hcmstY29yZS1jb21tb25tYXJrJ1xuaW1wb3J0IHtmYWN0b3J5U3BhY2V9IGZyb20gJ21pY3JvbWFyay1mYWN0b3J5LXNwYWNlJ1xuaW1wb3J0IHttYXJrZG93bkxpbmVFbmRpbmd9IGZyb20gJ21pY3JvbWFyay11dGlsLWNoYXJhY3RlcidcbmltcG9ydCB7Y29kZXMsIHR5cGVzfSBmcm9tICdtaWNyb21hcmstdXRpbC1zeW1ib2wnXG5cbi8qKiBAdHlwZSB7SW5pdGlhbENvbnN0cnVjdH0gKi9cbmV4cG9ydCBjb25zdCBmbG93ID0ge3Rva2VuaXplOiBpbml0aWFsaXplRmxvd31cblxuLyoqXG4gKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICogICBTZWxmLlxuICogQHR5cGUge0luaXRpYWxpemVyfVxuICogICBJbml0aWFsaXplci5cbiAqL1xuZnVuY3Rpb24gaW5pdGlhbGl6ZUZsb3coZWZmZWN0cykge1xuICBjb25zdCBzZWxmID0gdGhpc1xuICBjb25zdCBpbml0aWFsID0gZWZmZWN0cy5hdHRlbXB0KFxuICAgIC8vIFRyeSB0byBwYXJzZSBhIGJsYW5rIGxpbmUuXG4gICAgYmxhbmtMaW5lLFxuICAgIGF0QmxhbmtFbmRpbmcsXG4gICAgLy8gVHJ5IHRvIHBhcnNlIGluaXRpYWwgZmxvdyAoZXNzZW50aWFsbHksIG9ubHkgY29kZSkuXG4gICAgZWZmZWN0cy5hdHRlbXB0KFxuICAgICAgdGhpcy5wYXJzZXIuY29uc3RydWN0cy5mbG93SW5pdGlhbCxcbiAgICAgIGFmdGVyQ29uc3RydWN0LFxuICAgICAgZmFjdG9yeVNwYWNlKFxuICAgICAgICBlZmZlY3RzLFxuICAgICAgICBlZmZlY3RzLmF0dGVtcHQoXG4gICAgICAgICAgdGhpcy5wYXJzZXIuY29uc3RydWN0cy5mbG93LFxuICAgICAgICAgIGFmdGVyQ29uc3RydWN0LFxuICAgICAgICAgIGVmZmVjdHMuYXR0ZW1wdChjb250ZW50LCBhZnRlckNvbnN0cnVjdClcbiAgICAgICAgKSxcbiAgICAgICAgdHlwZXMubGluZVByZWZpeFxuICAgICAgKVxuICAgIClcbiAgKVxuXG4gIHJldHVybiBpbml0aWFsXG5cbiAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgZnVuY3Rpb24gYXRCbGFua0VuZGluZyhjb2RlKSB7XG4gICAgYXNzZXJ0KFxuICAgICAgY29kZSA9PT0gY29kZXMuZW9mIHx8IG1hcmtkb3duTGluZUVuZGluZyhjb2RlKSxcbiAgICAgICdleHBlY3RlZCBlb2wgb3IgZW9mJ1xuICAgIClcblxuICAgIGlmIChjb2RlID09PSBjb2Rlcy5lb2YpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgZWZmZWN0cy5lbnRlcih0eXBlcy5saW5lRW5kaW5nQmxhbmspXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgZWZmZWN0cy5leGl0KHR5cGVzLmxpbmVFbmRpbmdCbGFuaylcbiAgICBzZWxmLmN1cnJlbnRDb25zdHJ1Y3QgPSB1bmRlZmluZWRcbiAgICByZXR1cm4gaW5pdGlhbFxuICB9XG5cbiAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgZnVuY3Rpb24gYWZ0ZXJDb25zdHJ1Y3QoY29kZSkge1xuICAgIGFzc2VydChcbiAgICAgIGNvZGUgPT09IGNvZGVzLmVvZiB8fCBtYXJrZG93bkxpbmVFbmRpbmcoY29kZSksXG4gICAgICAnZXhwZWN0ZWQgZW9sIG9yIGVvZidcbiAgICApXG5cbiAgICBpZiAoY29kZSA9PT0gY29kZXMuZW9mKSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGVmZmVjdHMuZW50ZXIodHlwZXMubGluZUVuZGluZylcbiAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICBlZmZlY3RzLmV4aXQodHlwZXMubGluZUVuZGluZylcbiAgICBzZWxmLmN1cnJlbnRDb25zdHJ1Y3QgPSB1bmRlZmluZWRcbiAgICByZXR1cm4gaW5pdGlhbFxuICB9XG59XG4iXSwibmFtZXMiOlsib2siLCJhc3NlcnQiLCJibGFua0xpbmUiLCJjb250ZW50IiwiZmFjdG9yeVNwYWNlIiwibWFya2Rvd25MaW5lRW5kaW5nIiwiY29kZXMiLCJ0eXBlcyIsImZsb3ciLCJ0b2tlbml6ZSIsImluaXRpYWxpemVGbG93IiwiZWZmZWN0cyIsInNlbGYiLCJpbml0aWFsIiwiYXR0ZW1wdCIsImF0QmxhbmtFbmRpbmciLCJwYXJzZXIiLCJjb25zdHJ1Y3RzIiwiZmxvd0luaXRpYWwiLCJhZnRlckNvbnN0cnVjdCIsImxpbmVQcmVmaXgiLCJjb2RlIiwiZW9mIiwiY29uc3VtZSIsImVudGVyIiwibGluZUVuZGluZ0JsYW5rIiwiZXhpdCIsImN1cnJlbnRDb25zdHJ1Y3QiLCJ1bmRlZmluZWQiLCJsaW5lRW5kaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/text.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/text.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolver: () => (/* binding */ resolver),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   InitialConstruct,\n *   Initializer,\n *   Resolver,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */ \n\nconst resolver = {\n    resolveAll: createResolver()\n};\nconst string = initializeFactory(\"string\");\nconst text = initializeFactory(\"text\");\n/**\n * @param {'string' | 'text'} field\n *   Field.\n * @returns {InitialConstruct}\n *   Construct.\n */ function initializeFactory(field) {\n    return {\n        resolveAll: createResolver(field === \"text\" ? resolveAllLineSuffixes : undefined),\n        tokenize: initializeText\n    };\n    /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Initializer}\n   */ function initializeText(effects) {\n        const self = this;\n        const constructs = this.parser.constructs[field];\n        const text = effects.attempt(constructs, start, notText);\n        return start;\n        /** @type {State} */ function start(code) {\n            return atBreak(code) ? text(code) : notText(code);\n        }\n        /** @type {State} */ function notText(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n                effects.consume(code);\n                return;\n            }\n            effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data);\n            effects.consume(code);\n            return data;\n        }\n        /** @type {State} */ function data(code) {\n            if (atBreak(code)) {\n                effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data);\n                return text(code);\n            }\n            // Data.\n            effects.consume(code);\n            return data;\n        }\n        /**\n     * @param {Code} code\n     *   Code.\n     * @returns {boolean}\n     *   Whether the code is a break.\n     */ function atBreak(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n                return true;\n            }\n            const list = constructs[code];\n            let index = -1;\n            if (list) {\n                // Always populated by defaults.\n                (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(Array.isArray(list), \"expected `disable.null` to be populated\");\n                while(++index < list.length){\n                    const item = list[index];\n                    if (!item.previous || item.previous.call(self, self.previous)) {\n                        return true;\n                    }\n                }\n            }\n            return false;\n        }\n    }\n}\n/**\n * @param {Resolver | undefined} [extraResolver]\n *   Resolver.\n * @returns {Resolver}\n *   Resolver.\n */ function createResolver(extraResolver) {\n    return resolveAllText;\n    /** @type {Resolver} */ function resolveAllText(events, context) {\n        let index = -1;\n        /** @type {number | undefined} */ let enter;\n        // A rather boring computation (to merge adjacent `data` events) which\n        // improves mm performance by 29%.\n        while(++index <= events.length){\n            if (enter === undefined) {\n                if (events[index] && events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n                    enter = index;\n                    index++;\n                }\n            } else if (!events[index] || events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n                // Don’t do anything if there is one data token.\n                if (index !== enter + 2) {\n                    events[enter][1].end = events[index - 1][1].end;\n                    events.splice(enter + 2, index - enter - 2);\n                    index = enter + 2;\n                }\n                enter = undefined;\n            }\n        }\n        return extraResolver ? extraResolver(events, context) : events;\n    }\n}\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */ function resolveAllLineSuffixes(events, context) {\n    let eventIndex = 0 // Skip first.\n    ;\n    while(++eventIndex <= events.length){\n        if ((eventIndex === events.length || events[eventIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding) && events[eventIndex - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n            const data = events[eventIndex - 1][1];\n            const chunks = context.sliceStream(data);\n            let index = chunks.length;\n            let bufferIndex = -1;\n            let size = 0;\n            /** @type {boolean | undefined} */ let tabs;\n            while(index--){\n                const chunk = chunks[index];\n                if (typeof chunk === \"string\") {\n                    bufferIndex = chunk.length;\n                    while(chunk.charCodeAt(bufferIndex - 1) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space){\n                        size++;\n                        bufferIndex--;\n                    }\n                    if (bufferIndex) break;\n                    bufferIndex = -1;\n                } else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab) {\n                    tabs = true;\n                    size++;\n                } else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace) {\n                // Empty\n                } else {\n                    // Replacement character, exit.\n                    index++;\n                    break;\n                }\n            }\n            // Allow final trailing whitespace.\n            if (context._contentTypeTextTrailing && eventIndex === events.length) {\n                size = 0;\n            }\n            if (size) {\n                const token = {\n                    type: eventIndex === events.length || tabs || size < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.hardBreakPrefixSizeMin ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineSuffix : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.hardBreakTrailing,\n                    start: {\n                        _bufferIndex: index ? bufferIndex : data.start._bufferIndex + bufferIndex,\n                        _index: data.start._index + index,\n                        line: data.end.line,\n                        column: data.end.column - size,\n                        offset: data.end.offset - size\n                    },\n                    end: {\n                        ...data.end\n                    }\n                };\n                data.end = {\n                    ...token.start\n                };\n                if (data.start.offset === data.end.offset) {\n                    Object.assign(data, token);\n                } else {\n                    events.splice(eventIndex, 0, [\n                        \"enter\",\n                        token,\n                        context\n                    ], [\n                        \"exit\",\n                        token,\n                        context\n                    ]);\n                    eventIndex += 2;\n                }\n            }\n            eventIndex++;\n        }\n    }\n    return events;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/parse.js":
/*!*************************************************!*\
  !*** ./node_modules/micromark/dev/lib/parse.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-combine-extensions */ \"(ssr)/./node_modules/micromark-util-combine-extensions/index.js\");\n/* harmony import */ var _initialize_content_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./initialize/content.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/content.js\");\n/* harmony import */ var _initialize_document_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initialize/document.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/document.js\");\n/* harmony import */ var _initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./initialize/flow.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/* harmony import */ var _constructs_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constructs.js */ \"(ssr)/./node_modules/micromark/dev/lib/constructs.js\");\n/* harmony import */ var _create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./create-tokenizer.js */ \"(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js\");\n/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */ \n\n\n\n\n\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */ function parse(options) {\n    const settings = options || {};\n    const constructs = /** @type {FullNormalizedExtension} */ (0,micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__.combineExtensions)([\n        _constructs_js__WEBPACK_IMPORTED_MODULE_1__,\n        ...settings.extensions || []\n    ]);\n    /** @type {ParseContext} */ const parser = {\n        constructs,\n        content: create(_initialize_content_js__WEBPACK_IMPORTED_MODULE_2__.content),\n        defined: [],\n        document: create(_initialize_document_js__WEBPACK_IMPORTED_MODULE_3__.document),\n        flow: create(_initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__.flow),\n        lazy: {},\n        string: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.string),\n        text: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.text)\n    };\n    return parser;\n    /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */ function create(initial) {\n        return creator;\n        /** @type {Create} */ function creator(from) {\n            return (0,_create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__.createTokenizer)(parser, initial, from);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/postprocess.js":
/*!*******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/postprocess.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   postprocess: () => (/* binding */ postprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-subtokenize */ \"(ssr)/./node_modules/micromark-util-subtokenize/dev/index.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */ \n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */ function postprocess(events) {\n    while(!(0,micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__.subtokenize)(events)){\n    // Empty\n    }\n    return events;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvcG9zdHByb2Nlc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7Q0FFQyxHQUVxRDtBQUV0RDs7Ozs7Q0FLQyxHQUNNLFNBQVNDLFlBQVlDLE1BQU07SUFDaEMsTUFBTyxDQUFDRix1RUFBV0EsQ0FBQ0UsUUFBUztJQUMzQixRQUFRO0lBQ1Y7SUFFQSxPQUFPQTtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvcG9zdHByb2Nlc3MuanM/N2IwNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0V2ZW50fSBmcm9tICdtaWNyb21hcmstdXRpbC10eXBlcydcbiAqL1xuXG5pbXBvcnQge3N1YnRva2VuaXplfSBmcm9tICdtaWNyb21hcmstdXRpbC1zdWJ0b2tlbml6ZSdcblxuLyoqXG4gKiBAcGFyYW0ge0FycmF5PEV2ZW50Pn0gZXZlbnRzXG4gKiAgIEV2ZW50cy5cbiAqIEByZXR1cm5zIHtBcnJheTxFdmVudD59XG4gKiAgIEV2ZW50cy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBvc3Rwcm9jZXNzKGV2ZW50cykge1xuICB3aGlsZSAoIXN1YnRva2VuaXplKGV2ZW50cykpIHtcbiAgICAvLyBFbXB0eVxuICB9XG5cbiAgcmV0dXJuIGV2ZW50c1xufVxuIl0sIm5hbWVzIjpbInN1YnRva2VuaXplIiwicG9zdHByb2Nlc3MiLCJldmVudHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/postprocess.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/preprocess.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/preprocess.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   preprocess: () => (/* binding */ preprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */ /**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */ \nconst search = /[\\0\\t\\n\\r]/g;\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */ function preprocess() {\n    let column = 1;\n    let buffer = \"\";\n    /** @type {boolean | undefined} */ let start = true;\n    /** @type {boolean | undefined} */ let atCarriageReturn;\n    return preprocessor;\n    /** @type {Preprocessor} */ // eslint-disable-next-line complexity\n    function preprocessor(value, encoding, end) {\n        /** @type {Array<Chunk>} */ const chunks = [];\n        /** @type {RegExpMatchArray | null} */ let match;\n        /** @type {number} */ let next;\n        /** @type {number} */ let startPosition;\n        /** @type {number} */ let endPosition;\n        /** @type {Code} */ let code;\n        value = buffer + (typeof value === \"string\" ? value.toString() : new TextDecoder(encoding || undefined).decode(value));\n        startPosition = 0;\n        buffer = \"\";\n        if (start) {\n            // To do: `markdown-rs` actually parses BOMs (byte order mark).\n            if (value.charCodeAt(0) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.byteOrderMarker) {\n                startPosition++;\n            }\n            start = undefined;\n        }\n        while(startPosition < value.length){\n            search.lastIndex = startPosition;\n            match = search.exec(value);\n            endPosition = match && match.index !== undefined ? match.index : value.length;\n            code = value.charCodeAt(endPosition);\n            if (!match) {\n                buffer = value.slice(startPosition);\n                break;\n            }\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf && startPosition === endPosition && atCarriageReturn) {\n                chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed);\n                atCarriageReturn = undefined;\n            } else {\n                if (atCarriageReturn) {\n                    chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn);\n                    atCarriageReturn = undefined;\n                }\n                if (startPosition < endPosition) {\n                    chunks.push(value.slice(startPosition, endPosition));\n                    column += endPosition - startPosition;\n                }\n                switch(code){\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.nul:\n                        {\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.replacementCharacter);\n                            column++;\n                            break;\n                        }\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ht:\n                        {\n                            next = Math.ceil(column / micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize) * micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize;\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab);\n                            while(column++ < next)chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace);\n                            break;\n                        }\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf:\n                        {\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed);\n                            column = 1;\n                            break;\n                        }\n                    default:\n                        {\n                            atCarriageReturn = true;\n                            column = 1;\n                        }\n                }\n            }\n            startPosition = endPosition + 1;\n        }\n        if (end) {\n            if (atCarriageReturn) chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn);\n            if (buffer) chunks.push(buffer);\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof);\n        }\n        return chunks;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/preprocess.js\n");

/***/ })

};
;