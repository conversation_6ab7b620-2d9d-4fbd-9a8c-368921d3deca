"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ungap";
exports.ids = ["vendor-chunks/@ungap"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/deserialize.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\");\n\nconst env = typeof self === \"object\" ? self : globalThis;\nconst deserializer = ($, _)=>{\n    const as = (out, index)=>{\n        $.set(index, out);\n        return out;\n    };\n    const unpair = (index)=>{\n        if ($.has(index)) return $.get(index);\n        const [type, value] = _[index];\n        switch(type){\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE:\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.VOID:\n                return as(value, index);\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY:\n                {\n                    const arr = as([], index);\n                    for (const index of value)arr.push(unpair(index));\n                    return arr;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT:\n                {\n                    const object = as({}, index);\n                    for (const [key, index] of value)object[unpair(key)] = unpair(index);\n                    return object;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:\n                return as(new Date(value), index);\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP:\n                {\n                    const { source, flags } = value;\n                    return as(new RegExp(source, flags), index);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP:\n                {\n                    const map = as(new Map, index);\n                    for (const [key, index] of value)map.set(unpair(key), unpair(index));\n                    return map;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET:\n                {\n                    const set = as(new Set, index);\n                    for (const index of value)set.add(unpair(index));\n                    return set;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR:\n                {\n                    const { name, message } = value;\n                    return as(new env[name](message), index);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT:\n                return as(BigInt(value), index);\n            case \"BigInt\":\n                return as(Object(BigInt(value)), index);\n            case \"ArrayBuffer\":\n                return as(new Uint8Array(value).buffer, value);\n            case \"DataView\":\n                {\n                    const { buffer } = new Uint8Array(value);\n                    return as(new DataView(buffer), value);\n                }\n        }\n        return as(new env[type](value), index);\n    };\n    return unpair;\n};\n/**\n * @typedef {Array<string,any>} Record a type representation\n */ /**\n * Returns a deserialized value from a serialized array of Records.\n * @param {Record[]} serialized a previously serialized value.\n * @returns {any}\n */ const deserialize = (serialized)=>deserializer(new Map, serialized)(0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   deserialize: () => (/* reexport safe */ _deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize),\n/* harmony export */   serialize: () => (/* reexport safe */ _serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)\n/* harmony export */ });\n/* harmony import */ var _deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserialize.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js\");\n/* harmony import */ var _serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serialize.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js\");\n\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */ /**\n * Returns an array of serialized Records.\n * @param {any} any a serializable value.\n * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with\n * a transfer option (ignored when polyfilled) and/or non standard fields that\n * fallback to the polyfill if present.\n * @returns {Record[]}\n */ /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (typeof structuredClone === \"function\" ? /* c8 ignore start */ (any, options)=>options && (\"json\" in options || \"lossy\" in options) ? (0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)) : structuredClone(any) : (any, options)=>(0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)));\n/* c8 ignore stop */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZDO0FBQ0o7QUFFekM7O0NBRUMsR0FFRDs7Ozs7OztDQU9DLEdBQ0QsaUVBQWUsT0FBT0Usb0JBQW9CLGFBQ3hDLG1CQUFtQixHQUNuQixDQUFDQyxLQUFLQyxVQUNKQSxXQUFZLFdBQVVBLFdBQVcsV0FBV0EsT0FBTSxJQUNoREosNERBQVdBLENBQUNDLHdEQUFTQSxDQUFDRSxLQUFLQyxZQUFZRixnQkFBZ0JDLE9BRTNELENBQUNBLEtBQUtDLFVBQVlKLDREQUFXQSxDQUFDQyx3REFBU0EsQ0FBQ0UsS0FBS0MsU0FBU0EsRUFBQztBQUN2RCxrQkFBa0IsR0FFWSIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0B1bmdhcC9zdHJ1Y3R1cmVkLWNsb25lL2VzbS9pbmRleC5qcz9iN2Q0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7ZGVzZXJpYWxpemV9IGZyb20gJy4vZGVzZXJpYWxpemUuanMnO1xuaW1wb3J0IHtzZXJpYWxpemV9IGZyb20gJy4vc2VyaWFsaXplLmpzJztcblxuLyoqXG4gKiBAdHlwZWRlZiB7QXJyYXk8c3RyaW5nLGFueT59IFJlY29yZCBhIHR5cGUgcmVwcmVzZW50YXRpb25cbiAqL1xuXG4vKipcbiAqIFJldHVybnMgYW4gYXJyYXkgb2Ygc2VyaWFsaXplZCBSZWNvcmRzLlxuICogQHBhcmFtIHthbnl9IGFueSBhIHNlcmlhbGl6YWJsZSB2YWx1ZS5cbiAqIEBwYXJhbSB7e3RyYW5zZmVyPzogYW55W10sIGpzb24/OiBib29sZWFuLCBsb3NzeT86IGJvb2xlYW59P30gb3B0aW9ucyBhbiBvYmplY3Qgd2l0aFxuICogYSB0cmFuc2ZlciBvcHRpb24gKGlnbm9yZWQgd2hlbiBwb2x5ZmlsbGVkKSBhbmQvb3Igbm9uIHN0YW5kYXJkIGZpZWxkcyB0aGF0XG4gKiBmYWxsYmFjayB0byB0aGUgcG9seWZpbGwgaWYgcHJlc2VudC5cbiAqIEByZXR1cm5zIHtSZWNvcmRbXX1cbiAqL1xuZXhwb3J0IGRlZmF1bHQgdHlwZW9mIHN0cnVjdHVyZWRDbG9uZSA9PT0gXCJmdW5jdGlvblwiID9cbiAgLyogYzggaWdub3JlIHN0YXJ0ICovXG4gIChhbnksIG9wdGlvbnMpID0+IChcbiAgICBvcHRpb25zICYmICgnanNvbicgaW4gb3B0aW9ucyB8fCAnbG9zc3knIGluIG9wdGlvbnMpID9cbiAgICAgIGRlc2VyaWFsaXplKHNlcmlhbGl6ZShhbnksIG9wdGlvbnMpKSA6IHN0cnVjdHVyZWRDbG9uZShhbnkpXG4gICkgOlxuICAoYW55LCBvcHRpb25zKSA9PiBkZXNlcmlhbGl6ZShzZXJpYWxpemUoYW55LCBvcHRpb25zKSk7XG4gIC8qIGM4IGlnbm9yZSBzdG9wICovXG5cbmV4cG9ydCB7ZGVzZXJpYWxpemUsIHNlcmlhbGl6ZX07XG4iXSwibmFtZXMiOlsiZGVzZXJpYWxpemUiLCJzZXJpYWxpemUiLCJzdHJ1Y3R1cmVkQ2xvbmUiLCJhbnkiLCJvcHRpb25zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/serialize.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\");\n\nconst EMPTY = \"\";\nconst { toString } = {};\nconst { keys } = Object;\nconst typeOf = (value)=>{\n    const type = typeof value;\n    if (type !== \"object\" || !value) return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE,\n        type\n    ];\n    const asString = toString.call(value).slice(8, -1);\n    switch(asString){\n        case \"Array\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY,\n                EMPTY\n            ];\n        case \"Object\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT,\n                EMPTY\n            ];\n        case \"Date\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE,\n                EMPTY\n            ];\n        case \"RegExp\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP,\n                EMPTY\n            ];\n        case \"Map\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP,\n                EMPTY\n            ];\n        case \"Set\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.SET,\n                EMPTY\n            ];\n        case \"DataView\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY,\n                asString\n            ];\n    }\n    if (asString.includes(\"Array\")) return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY,\n        asString\n    ];\n    if (asString.includes(\"Error\")) return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR,\n        asString\n    ];\n    return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT,\n        asString\n    ];\n};\nconst shouldSkip = ([TYPE, type])=>TYPE === _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE && (type === \"function\" || type === \"symbol\");\nconst serializer = (strict, json, $, _)=>{\n    const as = (out, value)=>{\n        const index = _.push(out) - 1;\n        $.set(value, index);\n        return index;\n    };\n    const pair = (value)=>{\n        if ($.has(value)) return $.get(value);\n        let [TYPE, type] = typeOf(value);\n        switch(TYPE){\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE:\n                {\n                    let entry = value;\n                    switch(type){\n                        case \"bigint\":\n                            TYPE = _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT;\n                            entry = value.toString();\n                            break;\n                        case \"function\":\n                        case \"symbol\":\n                            if (strict) throw new TypeError(\"unable to serialize \" + type);\n                            entry = null;\n                            break;\n                        case \"undefined\":\n                            return as([\n                                _types_js__WEBPACK_IMPORTED_MODULE_0__.VOID\n                            ], value);\n                    }\n                    return as([\n                        TYPE,\n                        entry\n                    ], value);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY:\n                {\n                    if (type) {\n                        let spread = value;\n                        if (type === \"DataView\") {\n                            spread = new Uint8Array(value.buffer);\n                        } else if (type === \"ArrayBuffer\") {\n                            spread = new Uint8Array(value);\n                        }\n                        return as([\n                            type,\n                            [\n                                ...spread\n                            ]\n                        ], value);\n                    }\n                    const arr = [];\n                    const index = as([\n                        TYPE,\n                        arr\n                    ], value);\n                    for (const entry of value)arr.push(pair(entry));\n                    return index;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT:\n                {\n                    if (type) {\n                        switch(type){\n                            case \"BigInt\":\n                                return as([\n                                    type,\n                                    value.toString()\n                                ], value);\n                            case \"Boolean\":\n                            case \"Number\":\n                            case \"String\":\n                                return as([\n                                    type,\n                                    value.valueOf()\n                                ], value);\n                        }\n                    }\n                    if (json && \"toJSON\" in value) return pair(value.toJSON());\n                    const entries = [];\n                    const index = as([\n                        TYPE,\n                        entries\n                    ], value);\n                    for (const key of keys(value)){\n                        if (strict || !shouldSkip(typeOf(value[key]))) entries.push([\n                            pair(key),\n                            pair(value[key])\n                        ]);\n                    }\n                    return index;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:\n                return as([\n                    TYPE,\n                    value.toISOString()\n                ], value);\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP:\n                {\n                    const { source, flags } = value;\n                    return as([\n                        TYPE,\n                        {\n                            source,\n                            flags\n                        }\n                    ], value);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP:\n                {\n                    const entries = [];\n                    const index = as([\n                        TYPE,\n                        entries\n                    ], value);\n                    for (const [key, entry] of value){\n                        if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry)))) entries.push([\n                            pair(key),\n                            pair(entry)\n                        ]);\n                    }\n                    return index;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET:\n                {\n                    const entries = [];\n                    const index = as([\n                        TYPE,\n                        entries\n                    ], value);\n                    for (const entry of value){\n                        if (strict || !shouldSkip(typeOf(entry))) entries.push(pair(entry));\n                    }\n                    return index;\n                }\n        }\n        const { message } = value;\n        return as([\n            TYPE,\n            {\n                name: type,\n                message\n            }\n        ], value);\n    };\n    return pair;\n};\n/**\n * @typedef {Array<string,any>} Record a type representation\n */ /**\n * Returns an array of serialized Records.\n * @param {any} value a serializable value.\n * @param {{json?: boolean, lossy?: boolean}?} options an object with a `lossy` or `json` property that,\n *  if `true`, will not throw errors on incompatible types, and behave more\n *  like JSON stringify would behave. Symbol and Function will be discarded.\n * @returns {Record[]}\n */ const serialize = (value, { json, lossy } = {})=>{\n    const _ = [];\n    return serializer(!(json || lossy), !!json, new Map, _)(value), _;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/types.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/types.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ARRAY: () => (/* binding */ ARRAY),\n/* harmony export */   BIGINT: () => (/* binding */ BIGINT),\n/* harmony export */   DATE: () => (/* binding */ DATE),\n/* harmony export */   ERROR: () => (/* binding */ ERROR),\n/* harmony export */   MAP: () => (/* binding */ MAP),\n/* harmony export */   OBJECT: () => (/* binding */ OBJECT),\n/* harmony export */   PRIMITIVE: () => (/* binding */ PRIMITIVE),\n/* harmony export */   REGEXP: () => (/* binding */ REGEXP),\n/* harmony export */   SET: () => (/* binding */ SET),\n/* harmony export */   VOID: () => (/* binding */ VOID)\n/* harmony export */ });\nconst VOID = -1;\nconst PRIMITIVE = 0;\nconst ARRAY = 1;\nconst OBJECT = 2;\nconst DATE = 3;\nconst REGEXP = 4;\nconst MAP = 5;\nconst SET = 6;\nconst ERROR = 7;\nconst BIGINT = 8; // export const SYMBOL = 9;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBTyxNQUFNQSxPQUFhLENBQUMsRUFBRTtBQUN0QixNQUFNQyxZQUFhLEVBQUU7QUFDckIsTUFBTUMsUUFBYSxFQUFFO0FBQ3JCLE1BQU1DLFNBQWEsRUFBRTtBQUNyQixNQUFNQyxPQUFhLEVBQUU7QUFDckIsTUFBTUMsU0FBYSxFQUFFO0FBQ3JCLE1BQU1DLE1BQWEsRUFBRTtBQUNyQixNQUFNQyxNQUFhLEVBQUU7QUFDckIsTUFBTUMsUUFBYSxFQUFFO0FBQ3JCLE1BQU1DLFNBQWEsRUFBRSxDQUM1QiwyQkFBMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9AdW5nYXAvc3RydWN0dXJlZC1jbG9uZS9lc20vdHlwZXMuanM/NWU5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgVk9JRCAgICAgICA9IC0xO1xuZXhwb3J0IGNvbnN0IFBSSU1JVElWRSAgPSAwO1xuZXhwb3J0IGNvbnN0IEFSUkFZICAgICAgPSAxO1xuZXhwb3J0IGNvbnN0IE9CSkVDVCAgICAgPSAyO1xuZXhwb3J0IGNvbnN0IERBVEUgICAgICAgPSAzO1xuZXhwb3J0IGNvbnN0IFJFR0VYUCAgICAgPSA0O1xuZXhwb3J0IGNvbnN0IE1BUCAgICAgICAgPSA1O1xuZXhwb3J0IGNvbnN0IFNFVCAgICAgICAgPSA2O1xuZXhwb3J0IGNvbnN0IEVSUk9SICAgICAgPSA3O1xuZXhwb3J0IGNvbnN0IEJJR0lOVCAgICAgPSA4O1xuLy8gZXhwb3J0IGNvbnN0IFNZTUJPTCA9IDk7XG4iXSwibmFtZXMiOlsiVk9JRCIsIlBSSU1JVElWRSIsIkFSUkFZIiwiT0JKRUNUIiwiREFURSIsIlJFR0VYUCIsIk1BUCIsIlNFVCIsIkVSUk9SIiwiQklHSU5UIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\n");

/***/ })

};
;