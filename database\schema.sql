-- ========================================
-- AI博文管理系统数据库表结构
-- ========================================

-- 项目表 - 管理多个项目
CREATE TABLE projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    database_url TEXT, -- 外部项目数据库连接URL
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 作者表
CREATE TABLE authors (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    bio TEXT,
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 提示词模板表
CREATE TABLE prompts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(100) DEFAULT 'general',
    language VARCHAR(10) DEFAULT 'en',
    variables JSONB, -- 存储提示词中的变量信息
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 博文系列表
CREATE TABLE blog_series (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    summary TEXT, -- 系列总结，用于为后续博文提供上下文
    total_posts INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 博文表
CREATE TABLE blog_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    author_id UUID REFERENCES authors(id) ON DELETE SET NULL,
    series_id UUID REFERENCES blog_series(id) ON DELETE SET NULL,
    prompt_id UUID REFERENCES prompts(id) ON DELETE SET NULL,
    
    -- 基本信息
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    language VARCHAR(10) DEFAULT 'en',
    
    -- SEO信息
    seo_title VARCHAR(500),
    seo_description VARCHAR(500),
    seo_keywords TEXT[], -- 数组存储关键词
    
    -- 分类和标签
    category VARCHAR(100),
    tags TEXT[], -- 数组存储标签
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'scheduled', 'archived')),
    
    -- 时间信息
    scheduled_at TIMESTAMP WITH TIME ZONE,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 生成信息
    generation_params JSONB, -- 存储生成时的参数，如关键词、语言等
    series_context TEXT, -- 系列上下文信息
    
    -- 统计信息
    word_count INTEGER,
    reading_time INTEGER -- 预估阅读时间（分钟）
);

-- 博文系列关系表（用于维护系列内博文的顺序）
CREATE TABLE series_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    series_id UUID REFERENCES blog_series(id) ON DELETE CASCADE,
    post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
    order_in_series INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(series_id, post_id),
    UNIQUE(series_id, order_in_series)
);

-- 博文生成历史表（记录每次生成的详细信息）
CREATE TABLE generation_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
    prompt_used TEXT NOT NULL,
    input_keywords TEXT[],
    input_title VARCHAR(500),
    generation_params JSONB,
    ai_model VARCHAR(100),
    response_time INTEGER, -- 生成耗时（毫秒）
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 项目同步记录表（记录数据推送到其他项目的历史）
CREATE TABLE sync_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    source_project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    target_project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL, -- 'blog_post', 'author', 'prompt' etc.
    entity_id UUID NOT NULL,
    sync_status VARCHAR(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'success', 'failed')),
    error_message TEXT,
    synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- 索引优化
-- ========================================

-- 博文查询优化
CREATE INDEX idx_blog_posts_project_status ON blog_posts(project_id, status);
CREATE INDEX idx_blog_posts_series ON blog_posts(series_id);
CREATE INDEX idx_blog_posts_author ON blog_posts(author_id);
CREATE INDEX idx_blog_posts_published_at ON blog_posts(published_at DESC);
CREATE INDEX idx_blog_posts_tags ON blog_posts USING GIN(tags);
CREATE INDEX idx_blog_posts_keywords ON blog_posts USING GIN(seo_keywords);

-- 系列查询优化
CREATE INDEX idx_series_posts_series ON series_posts(series_id, order_in_series);

-- 其他索引
CREATE INDEX idx_authors_project ON authors(project_id);
CREATE INDEX idx_prompts_project_active ON prompts(project_id, is_active);
CREATE INDEX idx_generation_history_post ON generation_history(post_id);

-- ========================================
-- 触发器和函数
-- ========================================

-- 更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间戳触发器
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_authors_updated_at BEFORE UPDATE ON authors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_prompts_updated_at BEFORE UPDATE ON prompts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_series_updated_at BEFORE UPDATE ON blog_series
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_posts_updated_at BEFORE UPDATE ON blog_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 博文发布时自动设置发布时间
CREATE OR REPLACE FUNCTION set_published_at()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'published' AND OLD.status != 'published' THEN
        NEW.published_at = NOW();
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_blog_post_published_at BEFORE UPDATE ON blog_posts
    FOR EACH ROW EXECUTE FUNCTION set_published_at();

-- 更新系列博文数量
CREATE OR REPLACE FUNCTION update_series_post_count()
RETURNS TRIGGER AS $$
BEGIN
    -- 处理插入和删除
    IF TG_OP = 'INSERT' THEN
        UPDATE blog_series 
        SET total_posts = (
            SELECT COUNT(*) 
            FROM blog_posts 
            WHERE series_id = NEW.series_id AND status != 'archived'
        )
        WHERE id = NEW.series_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE blog_series 
        SET total_posts = (
            SELECT COUNT(*) 
            FROM blog_posts 
            WHERE series_id = OLD.series_id AND status != 'archived'
        )
        WHERE id = OLD.series_id;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        -- 如果系列发生变化
        IF OLD.series_id IS DISTINCT FROM NEW.series_id THEN
            -- 更新旧系列
            IF OLD.series_id IS NOT NULL THEN
                UPDATE blog_series 
                SET total_posts = (
                    SELECT COUNT(*) 
                    FROM blog_posts 
                    WHERE series_id = OLD.series_id AND status != 'archived'
                )
                WHERE id = OLD.series_id;
            END IF;
            -- 更新新系列
            IF NEW.series_id IS NOT NULL THEN
                UPDATE blog_series 
                SET total_posts = (
                    SELECT COUNT(*) 
                    FROM blog_posts 
                    WHERE series_id = NEW.series_id AND status != 'archived'
                )
                WHERE id = NEW.series_id;
            END IF;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_series_count_on_blog_posts 
    AFTER INSERT OR UPDATE OR DELETE ON blog_posts
    FOR EACH ROW EXECUTE FUNCTION update_series_post_count();

-- ========================================
-- 初始数据插入
-- ========================================

-- 插入默认项目
INSERT INTO projects (id, name, description, is_active) 
VALUES (gen_random_uuid(), 'Default Project', 'Default blog management project', true);

-- 插入示例作者
INSERT INTO authors (project_id, name, email, bio) 
SELECT id, 'Admin User', '<EMAIL>', 'Default administrator and content creator'
FROM projects WHERE name = 'Default Project' LIMIT 1;

-- 插入默认提示词模板
INSERT INTO prompts (project_id, name, content, category, language) 
SELECT id, 'Technical Blog Post', 
'Write a comprehensive technical blog post about {topic}. The post should be informative, well-structured, and include practical examples. Target audience: {audience}. Writing style: {style}. Please include: 1. Introduction 2. Main content with examples 3. Best practices 4. Conclusion',
'Technical', 'en'
FROM projects WHERE name = 'Default Project' LIMIT 1;

INSERT INTO prompts (project_id, name, content, category, language) 
SELECT id, 'SEO Optimized Article', 
'Create an SEO-optimized article about {topic}. Include relevant keywords naturally throughout the content. Structure: 1. Compelling headline 2. Meta description 3. Introduction with hook 4. Main content with subheadings 5. Conclusion with call-to-action. Target keyword: {keyword}',
'SEO', 'en'
FROM projects WHERE name = 'Default Project' LIMIT 1;

-- ========================================
-- RLS (Row Level Security) 策略
-- ========================================

-- 启用RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE authors ENABLE ROW LEVEL SECURITY;
ALTER TABLE prompts ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_series ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE series_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE generation_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_logs ENABLE ROW LEVEL SECURITY;

-- 项目访问策略（简化版，实际应用中需要更复杂的用户权限管理）
CREATE POLICY "Enable access for all users" ON projects FOR ALL USING (true);
CREATE POLICY "Enable access for all users" ON authors FOR ALL USING (true);
CREATE POLICY "Enable access for all users" ON prompts FOR ALL USING (true);
CREATE POLICY "Enable access for all users" ON blog_series FOR ALL USING (true);
CREATE POLICY "Enable access for all users" ON blog_posts FOR ALL USING (true);
CREATE POLICY "Enable access for all users" ON series_posts FOR ALL USING (true);
CREATE POLICY "Enable access for all users" ON generation_history FOR ALL USING (true);
CREATE POLICY "Enable access for all users" ON sync_logs FOR ALL USING (true);