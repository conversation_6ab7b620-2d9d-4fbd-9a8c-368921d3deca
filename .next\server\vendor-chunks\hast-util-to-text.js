"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-text";
exports.ids = ["vendor-chunks/hast-util-to-text"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-text/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/hast-util-to-text/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toText: () => (/* binding */ toText)\n/* harmony export */ });\n/* harmony import */ var unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-find-after */ \"(ssr)/./node_modules/unist-util-find-after/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-is-element/lib/index.js\");\n/**\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast-util-is-element').TestFunction} TestFunction\n */ /**\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Valid and useful whitespace values (from CSS).\n *\n * @typedef {0 | 1 | 2} BreakNumber\n *   Specific break:\n *\n *   *   `0` — space\n *   *   `1` — line ending\n *   *   `2` — blank line\n *\n * @typedef {'\\n'} BreakForce\n *   Forced break.\n *\n * @typedef {boolean} BreakValue\n *   Whether there was a break.\n *\n * @typedef {BreakNumber | BreakValue | undefined} BreakBefore\n *   Any value for a break before.\n *\n * @typedef {BreakForce | BreakNumber | BreakValue | undefined} BreakAfter\n *   Any value for a break after.\n *\n * @typedef CollectionInfo\n *   Info on current collection.\n * @property {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @property {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @property {Whitespace} whitespace\n *   Current whitespace setting.\n *\n * @typedef Options\n *   Configuration.\n * @property {Whitespace | null | undefined} [whitespace='normal']\n *   Initial CSS whitespace setting to use (default: `'normal'`).\n */ \n\nconst searchLineFeeds = /\\n/g;\nconst searchTabOrSpaces = /[\\t ]+/g;\nconst br = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(\"br\");\nconst cell = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(isCell);\nconst p = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(\"p\");\nconst row = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(\"tr\");\n// Note that we don’t need to include void elements here as they don’t have text.\n// See: <https://github.com/wooorm/html-void-elements>\nconst notRendered = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n    // List from: <https://html.spec.whatwg.org/multipage/rendering.html#hidden-elements>\n    \"datalist\",\n    \"head\",\n    \"noembed\",\n    \"noframes\",\n    \"noscript\",\n    \"rp\",\n    \"script\",\n    \"style\",\n    \"template\",\n    \"title\",\n    // Hidden attribute.\n    hidden,\n    // From: <https://html.spec.whatwg.org/multipage/rendering.html#flow-content-3>\n    closedDialog\n]);\n// See: <https://html.spec.whatwg.org/multipage/rendering.html#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blockOrCaption = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n    \"address\",\n    \"article\",\n    \"aside\",\n    \"blockquote\",\n    \"body\",\n    \"caption\",\n    \"center\",\n    \"dd\",\n    \"dialog\",\n    \"dir\",\n    \"dl\",\n    \"dt\",\n    \"div\",\n    \"figure\",\n    \"figcaption\",\n    \"footer\",\n    \"form,\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"header\",\n    \"hgroup\",\n    \"hr\",\n    \"html\",\n    \"legend\",\n    \"li\",\n    \"listing\",\n    \"main\",\n    \"menu\",\n    \"nav\",\n    \"ol\",\n    \"p\",\n    \"plaintext\",\n    \"pre\",\n    \"section\",\n    \"ul\",\n    \"xmp\" // Flow content (legacy)\n]);\n/**\n * Get the plain-text value of a node.\n *\n * ###### Algorithm\n *\n * *   if `tree` is a comment, returns its `value`\n * *   if `tree` is a text, applies normal whitespace collapsing to its\n *     `value`, as defined by the CSS Text spec\n * *   if `tree` is a root or element, applies an algorithm similar to the\n *     `innerText` getter as defined by HTML\n *\n * ###### Notes\n *\n * > 👉 **Note**: the algorithm acts as if `tree` is being rendered, and as if\n * > we’re a CSS-supporting user agent, with scripting enabled.\n *\n * *   if `tree` is an element that is not displayed (such as a `head`), we’ll\n *     still use the `innerText` algorithm instead of switching to `textContent`\n * *   if descendants of `tree` are elements that are not displayed, they are\n *     ignored\n * *   CSS is not considered, except for the default user agent style sheet\n * *   a line feed is collapsed instead of ignored in cases where Fullwidth, Wide,\n *     or Halfwidth East Asian Width characters are used, the same goes for a case\n *     with Chinese, Japanese, or Yi writing systems\n * *   replaced elements (such as `audio`) are treated like non-replaced elements\n *\n * @param {Nodes} tree\n *   Tree to turn into text.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `tree`.\n */ function toText(tree, options) {\n    const options_ = options || {};\n    const children = \"children\" in tree ? tree.children : [];\n    const block = blockOrCaption(tree);\n    const whitespace = inferWhitespace(tree, {\n        whitespace: options_.whitespace || \"normal\",\n        breakBefore: false,\n        breakAfter: false\n    });\n    /** @type {Array<BreakNumber | string>} */ const results = [];\n    // Treat `text` and `comment` as having normal white-space.\n    // This deviates from the spec as in the DOM the node’s `.data` has to be\n    // returned.\n    // If you want that behavior use `hast-util-to-string`.\n    // All other nodes are later handled as if they are `element`s (so the\n    // algorithm also works on a `root`).\n    // Nodes without children are treated as a void element, so `doctype` is thus\n    // ignored.\n    if (tree.type === \"text\" || tree.type === \"comment\") {\n        results.push(...collectText(tree, {\n            whitespace,\n            breakBefore: true,\n            breakAfter: true\n        }));\n    }\n    // 1.  If this element is not being rendered, or if the user agent is a\n    //     non-CSS user agent, then return the same value as the textContent IDL\n    //     attribute on this element.\n    //\n    //     Note: we’re not supporting stylesheets so we’re acting as if the node\n    //     is rendered.\n    //\n    //     If you want that behavior use `hast-util-to-string`.\n    //     Important: we’ll have to account for this later though.\n    // 2.  Let results be a new empty list.\n    let index = -1;\n    // 3.  For each child node node of this element:\n    while(++index < children.length){\n        // 3.1. Let current be the list resulting in running the inner text\n        //      collection steps with node.\n        //      Each item in results will either be a JavaScript string or a\n        //      positive integer (a required line break count).\n        // 3.2. For each item item in current, append item to results.\n        results.push(...renderedTextCollection(children[index], // @ts-expect-error: `tree` is a parent if we’re here.\n        tree, {\n            whitespace,\n            breakBefore: index ? undefined : block,\n            breakAfter: index < children.length - 1 ? br(children[index + 1]) : block\n        }));\n    }\n    // 4.  Remove any items from results that are the empty string.\n    // 5.  Remove any runs of consecutive required line break count items at the\n    //     start or end of results.\n    // 6.  Replace each remaining run of consecutive required line break count\n    //     items with a string consisting of as many U+000A LINE FEED (LF)\n    //     characters as the maximum of the values in the required line break\n    //     count items.\n    /** @type {Array<string>} */ const result = [];\n    /** @type {number | undefined} */ let count;\n    index = -1;\n    while(++index < results.length){\n        const value = results[index];\n        if (typeof value === \"number\") {\n            if (count !== undefined && value > count) count = value;\n        } else if (value) {\n            if (count !== undefined && count > -1) {\n                result.push(\"\\n\".repeat(count) || \" \");\n            }\n            count = -1;\n            result.push(value);\n        }\n    }\n    // 7.  Return the concatenation of the string items in results.\n    return result.join(\"\");\n}\n/**\n * <https://html.spec.whatwg.org/multipage/dom.html#rendered-text-collection-steps>\n *\n * @param {Nodes} node\n * @param {Parents} parent\n * @param {CollectionInfo} info\n * @returns {Array<BreakNumber | string>}\n */ function renderedTextCollection(node, parent, info) {\n    if (node.type === \"element\") {\n        return collectElement(node, parent, info);\n    }\n    if (node.type === \"text\") {\n        return info.whitespace === \"normal\" ? collectText(node, info) : collectPreText(node);\n    }\n    return [];\n}\n/**\n * Collect an element.\n *\n * @param {Element} node\n *   Element node.\n * @param {Parents} parent\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n */ function collectElement(node, parent, info) {\n    // First we infer the `white-space` property.\n    const whitespace = inferWhitespace(node, info);\n    const children = node.children || [];\n    let index = -1;\n    /** @type {Array<BreakNumber | string>} */ let items = [];\n    // We’re ignoring point 3, and exiting without any content here, because we\n    // deviated from the spec in `toText` at step 3.\n    if (notRendered(node)) {\n        return items;\n    }\n    /** @type {BreakNumber | undefined} */ let prefix;\n    /** @type {BreakForce | BreakNumber | undefined} */ let suffix;\n    // Note: we first detect if there is going to be a break before or after the\n    // contents, as that changes the white-space handling.\n    // 2.  If node’s computed value of `visibility` is not `visible`, then return\n    //     items.\n    //\n    //     Note: Ignored, as everything is visible by default user agent styles.\n    // 3.  If node is not being rendered, then return items. [...]\n    //\n    //     Note: We already did this above.\n    // See `collectText` for step 4.\n    // 5.  If node is a `<br>` element, then append a string containing a single\n    //     U+000A LINE FEED (LF) character to items.\n    if (br(node)) {\n        suffix = \"\\n\";\n    } else if (row(node) && // @ts-expect-error: something up with types of parents.\n    (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, row)) {\n        suffix = \"\\n\";\n    } else if (p(node)) {\n        prefix = 2;\n        suffix = 2;\n    } else if (blockOrCaption(node)) {\n        prefix = 1;\n        suffix = 1;\n    }\n    // 1.  Let items be the result of running the inner text collection steps with\n    //     each child node of node in tree order, and then concatenating the\n    //     results to a single list.\n    while(++index < children.length){\n        items = items.concat(renderedTextCollection(children[index], node, {\n            whitespace,\n            breakBefore: index ? undefined : prefix,\n            breakAfter: index < children.length - 1 ? br(children[index + 1]) : suffix\n        }));\n    }\n    // 6.  If node’s computed value of `display` is `table-cell`, and node’s CSS\n    //     box is not the last `table-cell` box of its enclosing `table-row` box,\n    //     then append a string containing a single U+0009 CHARACTER TABULATION\n    //     (tab) character to items.\n    //\n    //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>\n    if (cell(node) && // @ts-expect-error: something up with types of parents.\n    (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, cell)) {\n        items.push(\"\t\");\n    }\n    // Add the pre- and suffix.\n    if (prefix) items.unshift(prefix);\n    if (suffix) items.push(suffix);\n    return items;\n}\n/**\n * 4.  If node is a Text node, then for each CSS text box produced by node,\n *     in content order, compute the text of the box after application of the\n *     CSS `white-space` processing rules and `text-transform` rules, set\n *     items to the list of the resulting strings, and return items.\n *     The CSS `white-space` processing rules are slightly modified:\n *     collapsible spaces at the end of lines are always collapsed, but they\n *     are only removed if the line is the last line of the block, or it ends\n *     with a br element.\n *     Soft hyphens should be preserved.\n *\n *     Note: See `collectText` and `collectPreText`.\n *     Note: we don’t deal with `text-transform`, no element has that by\n *     default.\n *\n * See: <https://drafts.csswg.org/css-text/#white-space-phase-1>\n *\n * @param {Comment | Text} node\n *   Text node.\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */ function collectText(node, info) {\n    const value = String(node.value);\n    /** @type {Array<string>} */ const lines = [];\n    /** @type {Array<BreakNumber | string>} */ const result = [];\n    let start = 0;\n    while(start <= value.length){\n        searchLineFeeds.lastIndex = start;\n        const match = searchLineFeeds.exec(value);\n        const end = match && \"index\" in match ? match.index : value.length;\n        lines.push(// Any sequence of collapsible spaces and tabs immediately preceding or\n        // following a segment break is removed.\n        trimAndCollapseSpacesAndTabs(// […] ignoring bidi formatting characters (characters with the\n        // Bidi_Control property [UAX9]: ALM, LTR, RTL, LRE-RLO, LRI-PDI) as if\n        // they were not there.\n        value.slice(start, end).replace(/[\\u061C\\u200E\\u200F\\u202A-\\u202E\\u2066-\\u2069]/g, \"\"), start === 0 ? info.breakBefore : true, end === value.length ? info.breakAfter : true));\n        start = end + 1;\n    }\n    // Collapsible segment breaks are transformed for rendering according to the\n    // segment break transformation rules.\n    // So here we jump to 4.1.2 of [CSSTEXT]:\n    // Any collapsible segment break immediately following another collapsible\n    // segment break is removed\n    let index = -1;\n    /** @type {BreakNumber | undefined} */ let join;\n    while(++index < lines.length){\n        // *   If the character immediately before or immediately after the segment\n        //     break is the zero-width space character (U+200B), then the break is\n        //     removed, leaving behind the zero-width space.\n        if (lines[index].charCodeAt(lines[index].length - 1) === 0x200b /* ZWSP */  || index < lines.length - 1 && lines[index + 1].charCodeAt(0) === 0x200b) {\n            result.push(lines[index]);\n            join = undefined;\n        } else if (lines[index]) {\n            if (typeof join === \"number\") result.push(join);\n            result.push(lines[index]);\n            join = 0;\n        } else if (index === 0 || index === lines.length - 1) {\n            // If this line is empty, and it’s the first or last, add a space.\n            // Note that this function is only called in normal whitespace, so we\n            // don’t worry about `pre`.\n            result.push(0);\n        }\n    }\n    return result;\n}\n/**\n * Collect a text node as “pre” whitespace.\n *\n * @param {Text} node\n *   Text node.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */ function collectPreText(node) {\n    return [\n        String(node.value)\n    ];\n}\n/**\n * 3.  Every collapsible tab is converted to a collapsible space (U+0020).\n * 4.  Any collapsible space immediately following another collapsible\n *     space—even one outside the boundary of the inline containing that\n *     space, provided both spaces are within the same inline formatting\n *     context—is collapsed to have zero advance width. (It is invisible,\n *     but retains its soft wrap opportunity, if any.)\n *\n * @param {string} value\n *   Value to collapse.\n * @param {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @param {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @returns {string}\n *   Result.\n */ function trimAndCollapseSpacesAndTabs(value, breakBefore, breakAfter) {\n    /** @type {Array<string>} */ const result = [];\n    let start = 0;\n    /** @type {number | undefined} */ let end;\n    while(start < value.length){\n        searchTabOrSpaces.lastIndex = start;\n        const match = searchTabOrSpaces.exec(value);\n        end = match ? match.index : value.length;\n        // If we’re not directly after a segment break, but there was white space,\n        // add an empty value that will be turned into a space.\n        if (!start && !end && match && !breakBefore) {\n            result.push(\"\");\n        }\n        if (start !== end) {\n            result.push(value.slice(start, end));\n        }\n        start = match ? end + match[0].length : end;\n    }\n    // If we reached the end, there was trailing white space, and there’s no\n    // segment break after this node, add an empty value that will be turned\n    // into a space.\n    if (start !== end && !breakAfter) {\n        result.push(\"\");\n    }\n    return result.join(\" \");\n}\n/**\n * Figure out the whitespace of a node.\n *\n * We don’t support void elements here (so `nobr wbr` -> `normal` is ignored).\n *\n * @param {Nodes} node\n *   Node (typically `Element`).\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Whitespace}\n *   Applied whitespace.\n */ function inferWhitespace(node, info) {\n    if (node.type === \"element\") {\n        const properties = node.properties || {};\n        switch(node.tagName){\n            case \"listing\":\n            case \"plaintext\":\n            case \"xmp\":\n                {\n                    return \"pre\";\n                }\n            case \"nobr\":\n                {\n                    return \"nowrap\";\n                }\n            case \"pre\":\n                {\n                    return properties.wrap ? \"pre-wrap\" : \"pre\";\n                }\n            case \"td\":\n            case \"th\":\n                {\n                    return properties.noWrap ? \"nowrap\" : info.whitespace;\n                }\n            case \"textarea\":\n                {\n                    return \"pre-wrap\";\n                }\n            default:\n        }\n    }\n    return info.whitespace;\n}\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {properties: {hidden: true}}}\n */ function hidden(node) {\n    return Boolean((node.properties || {}).hidden);\n}\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {tagName: 'td' | 'th'}}\n */ function isCell(node) {\n    return node.tagName === \"td\" || node.tagName === \"th\";\n}\n/**\n * @type {TestFunction}\n */ function closedDialog(node) {\n    return node.tagName === \"dialog\" && !(node.properties || {}).open;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-text/lib/index.js\n");

/***/ })

};
;