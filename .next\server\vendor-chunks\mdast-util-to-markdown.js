"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-markdown";
exports.ids = ["vendor-chunks/mdast-util-to-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @import {Blockquote, Parents} from 'mdast'\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {Blockquote} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function blockquote(node, _, state, info) {\n    const exit = state.enter(\"blockquote\");\n    const tracker = state.createTracker(info);\n    tracker.move(\"> \");\n    tracker.shift(2);\n    const value = state.indentLines(state.containerFlow(node, tracker.current()), map);\n    exit();\n    return value;\n}\n/** @type {Map} */ function map(line, _, blank) {\n    return \">\" + (blank ? \"\" : \" \") + line;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/break.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/* harmony import */ var _util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-in-scope.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @import {Break, Parents} from 'mdast'\n * @import {Info, State} from 'mdast-util-to-markdown'\n */ \n/**\n * @param {Break} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function hardBreak(_, _1, state, info) {\n    let index = -1;\n    while(++index < state.unsafe.length){\n        // If we can’t put eols in this construct (setext headings, tables), use a\n        // space instead.\n        if (state.unsafe[index].character === \"\\n\" && (0,_util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, state.unsafe[index])) {\n            return /[ \\t]/.test(info.before) ? \"\" : \" \";\n        }\n    }\n    return \"\\\\\\n\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2JyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7OztDQUdDLEdBRXlEO0FBRTFEOzs7Ozs7Q0FNQyxHQUNNLFNBQVNDLFVBQVVDLENBQUMsRUFBRUMsRUFBRSxFQUFFQyxLQUFLLEVBQUVDLElBQUk7SUFDMUMsSUFBSUMsUUFBUSxDQUFDO0lBRWIsTUFBTyxFQUFFQSxRQUFRRixNQUFNRyxNQUFNLENBQUNDLE1BQU0sQ0FBRTtRQUNwQywwRUFBMEU7UUFDMUUsaUJBQWlCO1FBQ2pCLElBQ0VKLE1BQU1HLE1BQU0sQ0FBQ0QsTUFBTSxDQUFDRyxTQUFTLEtBQUssUUFDbENULHlFQUFjQSxDQUFDSSxNQUFNTSxLQUFLLEVBQUVOLE1BQU1HLE1BQU0sQ0FBQ0QsTUFBTSxHQUMvQztZQUNBLE9BQU8sUUFBUUssSUFBSSxDQUFDTixLQUFLTyxNQUFNLElBQUksS0FBSztRQUMxQztJQUNGO0lBRUEsT0FBTztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2JyZWFrLmpzP2MzMzciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtCcmVhaywgUGFyZW50c30gZnJvbSAnbWRhc3QnXG4gKiBAaW1wb3J0IHtJbmZvLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG5pbXBvcnQge3BhdHRlcm5JblNjb3BlfSBmcm9tICcuLi91dGlsL3BhdHRlcm4taW4tc2NvcGUuanMnXG5cbi8qKlxuICogQHBhcmFtIHtCcmVha30gX1xuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfMVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhhcmRCcmVhayhfLCBfMSwgc3RhdGUsIGluZm8pIHtcbiAgbGV0IGluZGV4ID0gLTFcblxuICB3aGlsZSAoKytpbmRleCA8IHN0YXRlLnVuc2FmZS5sZW5ndGgpIHtcbiAgICAvLyBJZiB3ZSBjYW7igJl0IHB1dCBlb2xzIGluIHRoaXMgY29uc3RydWN0IChzZXRleHQgaGVhZGluZ3MsIHRhYmxlcyksIHVzZSBhXG4gICAgLy8gc3BhY2UgaW5zdGVhZC5cbiAgICBpZiAoXG4gICAgICBzdGF0ZS51bnNhZmVbaW5kZXhdLmNoYXJhY3RlciA9PT0gJ1xcbicgJiZcbiAgICAgIHBhdHRlcm5JblNjb3BlKHN0YXRlLnN0YWNrLCBzdGF0ZS51bnNhZmVbaW5kZXhdKVxuICAgICkge1xuICAgICAgcmV0dXJuIC9bIFxcdF0vLnRlc3QoaW5mby5iZWZvcmUpID8gJycgOiAnICdcbiAgICB9XG4gIH1cblxuICByZXR1cm4gJ1xcXFxcXG4nXG59XG4iXSwibmFtZXMiOlsicGF0dGVybkluU2NvcGUiLCJoYXJkQnJlYWsiLCJfIiwiXzEiLCJzdGF0ZSIsImluZm8iLCJpbmRleCIsInVuc2FmZSIsImxlbmd0aCIsImNoYXJhY3RlciIsInN0YWNrIiwidGVzdCIsImJlZm9yZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/code.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/* harmony import */ var longest_streak__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! longest-streak */ \"(ssr)/./node_modules/longest-streak/index.js\");\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-code-as-indented.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-fence.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {Code, Parents} from 'mdast'\n */ \n\n\n/**\n * @param {Code} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function code(node, _, state, info) {\n    const marker = (0,_util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__.checkFence)(state);\n    const raw = node.value || \"\";\n    const suffix = marker === \"`\" ? \"GraveAccent\" : \"Tilde\";\n    if ((0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__.formatCodeAsIndented)(node, state)) {\n        const exit = state.enter(\"codeIndented\");\n        const value = state.indentLines(raw, map);\n        exit();\n        return value;\n    }\n    const tracker = state.createTracker(info);\n    const sequence = marker.repeat(Math.max((0,longest_streak__WEBPACK_IMPORTED_MODULE_2__.longestStreak)(raw, marker) + 1, 3));\n    const exit = state.enter(\"codeFenced\");\n    let value = tracker.move(sequence);\n    if (node.lang) {\n        const subexit = state.enter(`codeFencedLang${suffix}`);\n        value += tracker.move(state.safe(node.lang, {\n            before: value,\n            after: \" \",\n            encode: [\n                \"`\"\n            ],\n            ...tracker.current()\n        }));\n        subexit();\n    }\n    if (node.lang && node.meta) {\n        const subexit = state.enter(`codeFencedMeta${suffix}`);\n        value += tracker.move(\" \");\n        value += tracker.move(state.safe(node.meta, {\n            before: value,\n            after: \"\\n\",\n            encode: [\n                \"`\"\n            ],\n            ...tracker.current()\n        }));\n        subexit();\n    }\n    value += tracker.move(\"\\n\");\n    if (raw) {\n        value += tracker.move(raw + \"\\n\");\n    }\n    value += tracker.move(sequence);\n    exit();\n    return value;\n}\n/** @type {Map} */ function map(line, _, blank) {\n    return (blank ? \"\" : \"    \") + line;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/definition.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definition: () => (/* binding */ definition)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Definition, Parents} from 'mdast'\n */ \n/**\n * @param {Definition} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function definition(node, _, state, info) {\n    const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state);\n    const suffix = quote === '\"' ? \"Quote\" : \"Apostrophe\";\n    const exit = state.enter(\"definition\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"[\");\n    value += tracker.move(state.safe(state.associationId(node), {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    }));\n    value += tracker.move(\"]: \");\n    subexit();\n    if (// If there’s no url, or…\n    !node.url || // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)) {\n        subexit = state.enter(\"destinationLiteral\");\n        value += tracker.move(\"<\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n    } else {\n        // No whitespace, raw is prettier.\n        subexit = state.enter(\"destinationRaw\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: node.title ? \" \" : \"\\n\",\n            ...tracker.current()\n        }));\n    }\n    subexit();\n    if (node.title) {\n        subexit = state.enter(`title${suffix}`);\n        value += tracker.move(\" \" + quote);\n        value += tracker.move(state.safe(node.title, {\n            before: value,\n            after: quote,\n            ...tracker.current()\n        }));\n        value += tracker.move(quote);\n        subexit();\n    }\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/* harmony import */ var _util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Emphasis, Parents} from 'mdast'\n */ \n\n\nemphasis.peek = emphasisPeek;\n/**\n * @param {Emphasis} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function emphasis(node, _, state, info) {\n    const marker = (0,_util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__.checkEmphasis)(state);\n    const exit = state.enter(\"emphasis\");\n    const tracker = state.createTracker(info);\n    const before = tracker.move(marker);\n    let between = tracker.move(state.containerPhrasing(node, {\n        after: marker,\n        before,\n        ...tracker.current()\n    }));\n    const betweenHead = between.charCodeAt(0);\n    const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.before.charCodeAt(info.before.length - 1), betweenHead, marker);\n    if (open.inside) {\n        between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1);\n    }\n    const betweenTail = between.charCodeAt(between.length - 1);\n    const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker);\n    if (close.inside) {\n        between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail);\n    }\n    const after = tracker.move(marker);\n    exit();\n    state.attentionEncodeSurroundingInfo = {\n        after: close.outside,\n        before: open.outside\n    };\n    return before + between + after;\n}\n/**\n * @param {Emphasis} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */ function emphasisPeek(_, _1, state) {\n    return state.options.emphasis || \"*\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/heading.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/format-heading-as-setext.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Heading, Parents} from 'mdast'\n */ \n\n/**\n * @param {Heading} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function heading(node, _, state, info) {\n    const rank = Math.max(Math.min(6, node.depth || 1), 1);\n    const tracker = state.createTracker(info);\n    if ((0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__.formatHeadingAsSetext)(node, state)) {\n        const exit = state.enter(\"headingSetext\");\n        const subexit = state.enter(\"phrasing\");\n        const value = state.containerPhrasing(node, {\n            ...tracker.current(),\n            before: \"\\n\",\n            after: \"\\n\"\n        });\n        subexit();\n        exit();\n        return value + \"\\n\" + (rank === 1 ? \"=\" : \"-\").repeat(// The whole size…\n        value.length - // Minus the position of the character after the last EOL (or\n        // 0 if there is none)…\n        (Math.max(value.lastIndexOf(\"\\r\"), value.lastIndexOf(\"\\n\")) + 1));\n    }\n    const sequence = \"#\".repeat(rank);\n    const exit = state.enter(\"headingAtx\");\n    const subexit = state.enter(\"phrasing\");\n    // Note: for proper tracking, we should reset the output positions when there\n    // is no content returned, because then the space is not output.\n    // Practically, in that case, there is no content, so it doesn’t matter that\n    // we’ve tracked one too many characters.\n    tracker.move(sequence + \" \");\n    let value = state.containerPhrasing(node, {\n        before: \"# \",\n        after: \"\\n\",\n        ...tracker.current()\n    });\n    if (/^[\\t ]/.test(value)) {\n        // To do: what effect has the character reference on tracking?\n        value = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__.encodeCharacterReference)(value.charCodeAt(0)) + value.slice(1);\n    }\n    value = value ? sequence + \" \" + value : sequence;\n    if (state.options.closeAtx) {\n        value += \" \" + sequence;\n    }\n    subexit();\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/html.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @import {Html} from 'mdast'\n */ html.peek = htmlPeek;\n/**\n * @param {Html} node\n * @returns {string}\n */ function html(node) {\n    return node.value || \"\";\n}\n/**\n * @returns {string}\n */ function htmlPeek() {\n    return \"<\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2h0bWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRURBLEtBQUtDLElBQUksR0FBR0M7QUFFWjs7O0NBR0MsR0FDTSxTQUFTRixLQUFLRyxJQUFJO0lBQ3ZCLE9BQU9BLEtBQUtDLEtBQUssSUFBSTtBQUN2QjtBQUVBOztDQUVDLEdBQ0QsU0FBU0Y7SUFDUCxPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvaHRtbC5qcz83MTUyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SHRtbH0gZnJvbSAnbWRhc3QnXG4gKi9cblxuaHRtbC5wZWVrID0gaHRtbFBlZWtcblxuLyoqXG4gKiBAcGFyYW0ge0h0bWx9IG5vZGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBodG1sKG5vZGUpIHtcbiAgcmV0dXJuIG5vZGUudmFsdWUgfHwgJydcbn1cblxuLyoqXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5mdW5jdGlvbiBodG1sUGVlaygpIHtcbiAgcmV0dXJuICc8J1xufVxuIl0sIm5hbWVzIjpbImh0bWwiLCJwZWVrIiwiaHRtbFBlZWsiLCJub2RlIiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js":
/*!***************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {ImageReference, Parents} from 'mdast'\n */ imageReference.peek = imageReferencePeek;\n/**\n * @param {ImageReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function imageReference(node, _, state, info) {\n    const type = node.referenceType;\n    const exit = state.enter(\"imageReference\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"![\");\n    const alt = state.safe(node.alt, {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    value += tracker.move(alt + \"][\");\n    subexit();\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack;\n    state.stack = [];\n    subexit = state.enter(\"reference\");\n    // Note: for proper tracking, we should reset the output positions when we end\n    // up making a `shortcut` reference, because then there is no brace output.\n    // Practically, in that case, there is no content, so it doesn’t matter that\n    // we’ve tracked one too many characters.\n    const reference = state.safe(state.associationId(node), {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    subexit();\n    state.stack = stack;\n    exit();\n    if (type === \"full\" || !alt || alt !== reference) {\n        value += tracker.move(reference + \"]\");\n    } else if (type === \"shortcut\") {\n        // Remove the unwanted `[`.\n        value = value.slice(0, -1);\n    } else {\n        value += tracker.move(\"]\");\n    }\n    return value;\n}\n/**\n * @returns {string}\n */ function imageReferencePeek() {\n    return \"!\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Image, Parents} from 'mdast'\n */ \nimage.peek = imagePeek;\n/**\n * @param {Image} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function image(node, _, state, info) {\n    const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state);\n    const suffix = quote === '\"' ? \"Quote\" : \"Apostrophe\";\n    const exit = state.enter(\"image\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"![\");\n    value += tracker.move(state.safe(node.alt, {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    }));\n    value += tracker.move(\"](\");\n    subexit();\n    if (// If there’s no url but there is a title…\n    !node.url && node.title || // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)) {\n        subexit = state.enter(\"destinationLiteral\");\n        value += tracker.move(\"<\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n    } else {\n        // No whitespace, raw is prettier.\n        subexit = state.enter(\"destinationRaw\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: node.title ? \" \" : \")\",\n            ...tracker.current()\n        }));\n    }\n    subexit();\n    if (node.title) {\n        subexit = state.enter(`title${suffix}`);\n        value += tracker.move(\" \" + quote);\n        value += tracker.move(state.safe(node.title, {\n            before: value,\n            after: quote,\n            ...tracker.current()\n        }));\n        value += tracker.move(quote);\n        subexit();\n    }\n    value += tracker.move(\")\");\n    exit();\n    return value;\n}\n/**\n * @returns {string}\n */ function imagePeek() {\n    return \"!\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\");\n/* harmony import */ var _definition_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./definition.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./html.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./image.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./link.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./paragraph.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default (CommonMark) handlers.\n */ const handle = {\n    blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n    break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n    code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n    definition: _definition_js__WEBPACK_IMPORTED_MODULE_3__.definition,\n    emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n    hardBreak: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n    heading: _heading_js__WEBPACK_IMPORTED_MODULE_5__.heading,\n    html: _html_js__WEBPACK_IMPORTED_MODULE_6__.html,\n    image: _image_js__WEBPACK_IMPORTED_MODULE_7__.image,\n    imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n    inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n    link: _link_js__WEBPACK_IMPORTED_MODULE_10__.link,\n    linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n    list: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n    listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n    paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_14__.paragraph,\n    root: _root_js__WEBPACK_IMPORTED_MODULE_15__.root,\n    strong: _strong_js__WEBPACK_IMPORTED_MODULE_16__.strong,\n    text: _text_js__WEBPACK_IMPORTED_MODULE_17__.text,\n    thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__.thematicBreak\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {InlineCode, Parents} from 'mdast'\n */ inlineCode.peek = inlineCodePeek;\n/**\n * @param {InlineCode} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */ function inlineCode(node, _, state) {\n    let value = node.value || \"\";\n    let sequence = \"`\";\n    let index = -1;\n    // If there is a single grave accent on its own in the code, use a fence of\n    // two.\n    // If there are two in a row, use one.\n    while(new RegExp(\"(^|[^`])\" + sequence + \"([^`]|$)\").test(value)){\n        sequence += \"`\";\n    }\n    // If this is not just spaces or eols (tabs don’t count), and either the\n    // first or last character are a space, eol, or tick, then pad with spaces.\n    if (/[^ \\r\\n]/.test(value) && (/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value) || /^`|`$/.test(value))) {\n        value = \" \" + value + \" \";\n    }\n    // We have a potential problem: certain characters after eols could result in\n    // blocks being seen.\n    // For example, if someone injected the string `'\\n# b'`, then that would\n    // result in an ATX heading.\n    // We can’t escape characters in `inlineCode`, but because eols are\n    // transformed to spaces when going from markdown to HTML anyway, we can swap\n    // them out.\n    while(++index < state.unsafe.length){\n        const pattern = state.unsafe[index];\n        const expression = state.compilePattern(pattern);\n        /** @type {RegExpExecArray | null} */ let match;\n        // Only look for `atBreak`s.\n        // Btw: note that `atBreak` patterns will always start the regex at LF or\n        // CR.\n        if (!pattern.atBreak) continue;\n        while(match = expression.exec(value)){\n            let position = match.index;\n            // Support CRLF (patterns only look for one of the characters).\n            if (value.charCodeAt(position) === 10 /* `\\n` */  && value.charCodeAt(position - 1) === 13 /* `\\r` */ ) {\n                position--;\n            }\n            value = value.slice(0, position) + \" \" + value.slice(match.index + 1);\n        }\n    }\n    return sequence + value + sequence;\n}\n/**\n * @returns {string}\n */ function inlineCodePeek() {\n    return \"`\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {LinkReference, Parents} from 'mdast'\n */ linkReference.peek = linkReferencePeek;\n/**\n * @param {LinkReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function linkReference(node, _, state, info) {\n    const type = node.referenceType;\n    const exit = state.enter(\"linkReference\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"[\");\n    const text = state.containerPhrasing(node, {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    value += tracker.move(text + \"][\");\n    subexit();\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack;\n    state.stack = [];\n    subexit = state.enter(\"reference\");\n    // Note: for proper tracking, we should reset the output positions when we end\n    // up making a `shortcut` reference, because then there is no brace output.\n    // Practically, in that case, there is no content, so it doesn’t matter that\n    // we’ve tracked one too many characters.\n    const reference = state.safe(state.associationId(node), {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    subexit();\n    state.stack = stack;\n    exit();\n    if (type === \"full\" || !text || text !== reference) {\n        value += tracker.move(reference + \"]\");\n    } else if (type === \"shortcut\") {\n        // Remove the unwanted `[`.\n        value = value.slice(0, -1);\n    } else {\n        value += tracker.move(\"]\");\n    }\n    return value;\n}\n/**\n * @returns {string}\n */ function linkReferencePeek() {\n    return \"[\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/* harmony import */ var _util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-link-as-autolink.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Link, Parents} from 'mdast'\n * @import {Exit} from '../types.js'\n */ \n\nlink.peek = linkPeek;\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function link(node, _, state, info) {\n    const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state);\n    const suffix = quote === '\"' ? \"Quote\" : \"Apostrophe\";\n    const tracker = state.createTracker(info);\n    /** @type {Exit} */ let exit;\n    /** @type {Exit} */ let subexit;\n    if ((0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state)) {\n        // Hide the fact that we’re in phrasing, because escapes don’t work.\n        const stack = state.stack;\n        state.stack = [];\n        exit = state.enter(\"autolink\");\n        let value = tracker.move(\"<\");\n        value += tracker.move(state.containerPhrasing(node, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n        exit();\n        state.stack = stack;\n        return value;\n    }\n    exit = state.enter(\"link\");\n    subexit = state.enter(\"label\");\n    let value = tracker.move(\"[\");\n    value += tracker.move(state.containerPhrasing(node, {\n        before: value,\n        after: \"](\",\n        ...tracker.current()\n    }));\n    value += tracker.move(\"](\");\n    subexit();\n    if (// If there’s no url but there is a title…\n    !node.url && node.title || // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)) {\n        subexit = state.enter(\"destinationLiteral\");\n        value += tracker.move(\"<\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n    } else {\n        // No whitespace, raw is prettier.\n        subexit = state.enter(\"destinationRaw\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: node.title ? \" \" : \")\",\n            ...tracker.current()\n        }));\n    }\n    subexit();\n    if (node.title) {\n        subexit = state.enter(`title${suffix}`);\n        value += tracker.move(\" \" + quote);\n        value += tracker.move(state.safe(node.title, {\n            before: value,\n            after: quote,\n            ...tracker.current()\n        }));\n        value += tracker.move(quote);\n        subexit();\n    }\n    value += tracker.move(\")\");\n    exit();\n    return value;\n}\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */ function linkPeek(node, _, state) {\n    return (0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state) ? \"<\" : \"[\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list-item.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-list-item-indent.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {ListItem, Parents} from 'mdast'\n */ \n\n/**\n * @param {ListItem} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function listItem(node, parent, state, info) {\n    const listItemIndent = (0,_util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__.checkListItemIndent)(state);\n    let bullet = state.bulletCurrent || (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state);\n    // Add the marker value for ordered lists.\n    if (parent && parent.type === \"list\" && parent.ordered) {\n        bullet = (typeof parent.start === \"number\" && parent.start > -1 ? parent.start : 1) + (state.options.incrementListMarker === false ? 0 : parent.children.indexOf(node)) + bullet;\n    }\n    let size = bullet.length + 1;\n    if (listItemIndent === \"tab\" || listItemIndent === \"mixed\" && (parent && parent.type === \"list\" && parent.spread || node.spread)) {\n        size = Math.ceil(size / 4) * 4;\n    }\n    const tracker = state.createTracker(info);\n    tracker.move(bullet + \" \".repeat(size - bullet.length));\n    tracker.shift(size);\n    const exit = state.enter(\"listItem\");\n    const value = state.indentLines(state.containerFlow(node, tracker.current()), map);\n    exit();\n    return value;\n    /** @type {Map} */ function map(line, index, blank) {\n        if (index) {\n            return (blank ? \"\" : \" \".repeat(size)) + line;\n        }\n        return (blank ? bullet : bullet + \" \".repeat(size - bullet.length)) + line;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/check-bullet-other.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\");\n/* harmony import */ var _util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-bullet-ordered.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/check-rule.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {List, Parents} from 'mdast'\n */ \n\n\n\n/**\n * @param {List} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function list(node, parent, state, info) {\n    const exit = state.enter(\"list\");\n    const bulletCurrent = state.bulletCurrent;\n    /** @type {string} */ let bullet = node.ordered ? (0,_util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__.checkBulletOrdered)(state) : (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state);\n    /** @type {string} */ const bulletOther = node.ordered ? bullet === \".\" ? \")\" : \".\" : (0,_util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__.checkBulletOther)(state);\n    let useDifferentMarker = parent && state.bulletLastUsed ? bullet === state.bulletLastUsed : false;\n    if (!node.ordered) {\n        const firstListItem = node.children ? node.children[0] : undefined;\n        // If there’s an empty first list item directly in two list items,\n        // we have to use a different bullet:\n        //\n        // ```markdown\n        // * - *\n        // ```\n        //\n        // …because otherwise it would become one big thematic break.\n        if (// Bullet could be used as a thematic break marker:\n        (bullet === \"*\" || bullet === \"-\") && // Empty first list item:\n        firstListItem && (!firstListItem.children || !firstListItem.children[0]) && // Directly in two other list items:\n        state.stack[state.stack.length - 1] === \"list\" && state.stack[state.stack.length - 2] === \"listItem\" && state.stack[state.stack.length - 3] === \"list\" && state.stack[state.stack.length - 4] === \"listItem\" && // That are each the first child.\n        state.indexStack[state.indexStack.length - 1] === 0 && state.indexStack[state.indexStack.length - 2] === 0 && state.indexStack[state.indexStack.length - 3] === 0) {\n            useDifferentMarker = true;\n        }\n        // If there’s a thematic break at the start of the first list item,\n        // we have to use a different bullet:\n        //\n        // ```markdown\n        // * ---\n        // ```\n        //\n        // …because otherwise it would become one big thematic break.\n        if ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__.checkRule)(state) === bullet && firstListItem) {\n            let index = -1;\n            while(++index < node.children.length){\n                const item = node.children[index];\n                if (item && item.type === \"listItem\" && item.children && item.children[0] && item.children[0].type === \"thematicBreak\") {\n                    useDifferentMarker = true;\n                    break;\n                }\n            }\n        }\n    }\n    if (useDifferentMarker) {\n        bullet = bulletOther;\n    }\n    state.bulletCurrent = bullet;\n    const value = state.containerFlow(node, info);\n    state.bulletLastUsed = bullet;\n    state.bulletCurrent = bulletCurrent;\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Paragraph, Parents} from 'mdast'\n */ /**\n * @param {Paragraph} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function paragraph(node, _, state, info) {\n    const exit = state.enter(\"paragraph\");\n    const subexit = state.enter(\"phrasing\");\n    const value = state.containerPhrasing(node, info);\n    subexit();\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3BhcmFncmFwaC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7OztDQUdDLEdBRUQ7Ozs7OztDQU1DLEdBQ00sU0FBU0EsVUFBVUMsSUFBSSxFQUFFQyxDQUFDLEVBQUVDLEtBQUssRUFBRUMsSUFBSTtJQUM1QyxNQUFNQyxPQUFPRixNQUFNRyxLQUFLLENBQUM7SUFDekIsTUFBTUMsVUFBVUosTUFBTUcsS0FBSyxDQUFDO0lBQzVCLE1BQU1FLFFBQVFMLE1BQU1NLGlCQUFpQixDQUFDUixNQUFNRztJQUM1Q0c7SUFDQUY7SUFDQSxPQUFPRztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3BhcmFncmFwaC5qcz8wOTY3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SW5mbywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtQYXJhZ3JhcGgsIFBhcmVudHN9IGZyb20gJ21kYXN0J1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtQYXJhZ3JhcGh9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhcmFncmFwaChub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICBjb25zdCBleGl0ID0gc3RhdGUuZW50ZXIoJ3BhcmFncmFwaCcpXG4gIGNvbnN0IHN1YmV4aXQgPSBzdGF0ZS5lbnRlcigncGhyYXNpbmcnKVxuICBjb25zdCB2YWx1ZSA9IHN0YXRlLmNvbnRhaW5lclBocmFzaW5nKG5vZGUsIGluZm8pXG4gIHN1YmV4aXQoKVxuICBleGl0KClcbiAgcmV0dXJuIHZhbHVlXG59XG4iXSwibmFtZXMiOlsicGFyYWdyYXBoIiwibm9kZSIsIl8iLCJzdGF0ZSIsImluZm8iLCJleGl0IiwiZW50ZXIiLCJzdWJleGl0IiwidmFsdWUiLCJjb250YWluZXJQaHJhc2luZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/root.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-phrasing */ \"(ssr)/./node_modules/mdast-util-phrasing/lib/index.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Root} from 'mdast'\n */ \n/**\n * @param {Root} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function root(node, _, state, info) {\n    // Note: `html` nodes are ambiguous.\n    const hasPhrasing = node.children.some(function(d) {\n        return (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__.phrasing)(d);\n    });\n    const container = hasPhrasing ? state.containerPhrasing : state.containerFlow;\n    return container.call(state, node, info);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3Jvb3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7O0NBR0MsR0FFMkM7QUFFNUM7Ozs7OztDQU1DLEdBQ00sU0FBU0MsS0FBS0MsSUFBSSxFQUFFQyxDQUFDLEVBQUVDLEtBQUssRUFBRUMsSUFBSTtJQUN2QyxvQ0FBb0M7SUFDcEMsTUFBTUMsY0FBY0osS0FBS0ssUUFBUSxDQUFDQyxJQUFJLENBQUMsU0FBVUMsQ0FBQztRQUNoRCxPQUFPVCw2REFBUUEsQ0FBQ1M7SUFDbEI7SUFFQSxNQUFNQyxZQUFZSixjQUFjRixNQUFNTyxpQkFBaUIsR0FBR1AsTUFBTVEsYUFBYTtJQUM3RSxPQUFPRixVQUFVRyxJQUFJLENBQUNULE9BQU9GLE1BQU1HO0FBQ3JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3Jvb3QuanM/YTZiOCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZm8sIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7UGFyZW50cywgUm9vdH0gZnJvbSAnbWRhc3QnXG4gKi9cblxuaW1wb3J0IHtwaHJhc2luZ30gZnJvbSAnbWRhc3QtdXRpbC1waHJhc2luZydcblxuLyoqXG4gKiBAcGFyYW0ge1Jvb3R9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJvb3Qobm9kZSwgXywgc3RhdGUsIGluZm8pIHtcbiAgLy8gTm90ZTogYGh0bWxgIG5vZGVzIGFyZSBhbWJpZ3VvdXMuXG4gIGNvbnN0IGhhc1BocmFzaW5nID0gbm9kZS5jaGlsZHJlbi5zb21lKGZ1bmN0aW9uIChkKSB7XG4gICAgcmV0dXJuIHBocmFzaW5nKGQpXG4gIH0pXG5cbiAgY29uc3QgY29udGFpbmVyID0gaGFzUGhyYXNpbmcgPyBzdGF0ZS5jb250YWluZXJQaHJhc2luZyA6IHN0YXRlLmNvbnRhaW5lckZsb3dcbiAgcmV0dXJuIGNvbnRhaW5lci5jYWxsKHN0YXRlLCBub2RlLCBpbmZvKVxufVxuIl0sIm5hbWVzIjpbInBocmFzaW5nIiwicm9vdCIsIm5vZGUiLCJfIiwic3RhdGUiLCJpbmZvIiwiaGFzUGhyYXNpbmciLCJjaGlsZHJlbiIsInNvbWUiLCJkIiwiY29udGFpbmVyIiwiY29udGFpbmVyUGhyYXNpbmciLCJjb250YWluZXJGbG93IiwiY2FsbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/strong.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/* harmony import */ var _util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-strong.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Strong} from 'mdast'\n */ \n\n\nstrong.peek = strongPeek;\n/**\n * @param {Strong} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function strong(node, _, state, info) {\n    const marker = (0,_util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__.checkStrong)(state);\n    const exit = state.enter(\"strong\");\n    const tracker = state.createTracker(info);\n    const before = tracker.move(marker + marker);\n    let between = tracker.move(state.containerPhrasing(node, {\n        after: marker,\n        before,\n        ...tracker.current()\n    }));\n    const betweenHead = between.charCodeAt(0);\n    const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.before.charCodeAt(info.before.length - 1), betweenHead, marker);\n    if (open.inside) {\n        between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1);\n    }\n    const betweenTail = between.charCodeAt(between.length - 1);\n    const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker);\n    if (close.inside) {\n        between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail);\n    }\n    const after = tracker.move(marker + marker);\n    exit();\n    state.attentionEncodeSurroundingInfo = {\n        after: close.outside,\n        before: open.outside\n    };\n    return before + between + after;\n}\n/**\n * @param {Strong} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */ function strongPeek(_, _1, state) {\n    return state.options.strong || \"*\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/text.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Text} from 'mdast'\n */ /**\n * @param {Text} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function text(node, _, state, info) {\n    return state.safe(node.value, info);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RleHQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7Q0FHQyxHQUVEOzs7Ozs7Q0FNQyxHQUNNLFNBQVNBLEtBQUtDLElBQUksRUFBRUMsQ0FBQyxFQUFFQyxLQUFLLEVBQUVDLElBQUk7SUFDdkMsT0FBT0QsTUFBTUUsSUFBSSxDQUFDSixLQUFLSyxLQUFLLEVBQUVGO0FBQ2hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RleHQuanM/OGNjZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZm8sIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7UGFyZW50cywgVGV4dH0gZnJvbSAnbWRhc3QnXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1RleHR9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRleHQobm9kZSwgXywgc3RhdGUsIGluZm8pIHtcbiAgcmV0dXJuIHN0YXRlLnNhZmUobm9kZS52YWx1ZSwgaW5mbylcbn1cbiJdLCJuYW1lcyI6WyJ0ZXh0Iiwibm9kZSIsIl8iLCJzdGF0ZSIsImluZm8iLCJzYWZlIiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/* harmony import */ var _util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-rule-repetition.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-rule.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Parents, ThematicBreak} from 'mdast'\n */ \n\n/**\n * @param {ThematicBreak} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */ function thematicBreak(_, _1, state) {\n    const value = ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__.checkRule)(state) + (state.options.ruleSpaces ? \" \" : \"\")).repeat((0,_util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__.checkRuleRepetition)(state));\n    return state.options.ruleSpaces ? value.slice(0, -1) : value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RoZW1hdGljLWJyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBOzs7Q0FHQyxHQUVtRTtBQUNyQjtBQUUvQzs7Ozs7Q0FLQyxHQUNNLFNBQVNFLGNBQWNDLENBQUMsRUFBRUMsRUFBRSxFQUFFQyxLQUFLO0lBQ3hDLE1BQU1DLFFBQVEsQ0FDWkwsOERBQVNBLENBQUNJLFNBQVVBLENBQUFBLE1BQU1FLE9BQU8sQ0FBQ0MsVUFBVSxHQUFHLE1BQU0sRUFBQyxDQUFDLEVBQ3ZEQyxNQUFNLENBQUNULG1GQUFtQkEsQ0FBQ0s7SUFFN0IsT0FBT0EsTUFBTUUsT0FBTyxDQUFDQyxVQUFVLEdBQUdGLE1BQU1JLEtBQUssQ0FBQyxHQUFHLENBQUMsS0FBS0o7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvdGhlbWF0aWMtYnJlYWsuanM/YzE0MCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7UGFyZW50cywgVGhlbWF0aWNCcmVha30gZnJvbSAnbWRhc3QnXG4gKi9cblxuaW1wb3J0IHtjaGVja1J1bGVSZXBldGl0aW9ufSBmcm9tICcuLi91dGlsL2NoZWNrLXJ1bGUtcmVwZXRpdGlvbi5qcydcbmltcG9ydCB7Y2hlY2tSdWxlfSBmcm9tICcuLi91dGlsL2NoZWNrLXJ1bGUuanMnXG5cbi8qKlxuICogQHBhcmFtIHtUaGVtYXRpY0JyZWFrfSBfXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF8xXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRoZW1hdGljQnJlYWsoXywgXzEsIHN0YXRlKSB7XG4gIGNvbnN0IHZhbHVlID0gKFxuICAgIGNoZWNrUnVsZShzdGF0ZSkgKyAoc3RhdGUub3B0aW9ucy5ydWxlU3BhY2VzID8gJyAnIDogJycpXG4gICkucmVwZWF0KGNoZWNrUnVsZVJlcGV0aXRpb24oc3RhdGUpKVxuXG4gIHJldHVybiBzdGF0ZS5vcHRpb25zLnJ1bGVTcGFjZXMgPyB2YWx1ZS5zbGljZSgwLCAtMSkgOiB2YWx1ZVxufVxuIl0sIm5hbWVzIjpbImNoZWNrUnVsZVJlcGV0aXRpb24iLCJjaGVja1J1bGUiLCJ0aGVtYXRpY0JyZWFrIiwiXyIsIl8xIiwic3RhdGUiLCJ2YWx1ZSIsIm9wdGlvbnMiLCJydWxlU3BhY2VzIiwicmVwZWF0Iiwic2xpY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js":
/*!******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOrdered: () => (/* binding */ checkBulletOrdered)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */ function checkBulletOrdered(state) {\n    const marker = state.options.bulletOrdered || \".\";\n    if (marker !== \".\" && marker !== \")\") {\n        throw new Error(\"Cannot serialize items with `\" + marker + \"` for `options.bulletOrdered`, expected `.` or `)`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3JkZXJlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxtQkFBbUJDLEtBQUs7SUFDdEMsTUFBTUMsU0FBU0QsTUFBTUUsT0FBTyxDQUFDQyxhQUFhLElBQUk7SUFFOUMsSUFBSUYsV0FBVyxPQUFPQSxXQUFXLEtBQUs7UUFDcEMsTUFBTSxJQUFJRyxNQUNSLGtDQUNFSCxTQUNBO0lBRU47SUFFQSxPQUFPQTtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3JkZXJlZC5qcz85ZTMzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snYnVsbGV0T3JkZXJlZCddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrQnVsbGV0T3JkZXJlZChzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLmJ1bGxldE9yZGVyZWQgfHwgJy4nXG5cbiAgaWYgKG1hcmtlciAhPT0gJy4nICYmIG1hcmtlciAhPT0gJyknKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgaXRlbXMgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5idWxsZXRPcmRlcmVkYCwgZXhwZWN0ZWQgYC5gIG9yIGApYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOlsiY2hlY2tCdWxsZXRPcmRlcmVkIiwic3RhdGUiLCJtYXJrZXIiLCJvcHRpb25zIiwiYnVsbGV0T3JkZXJlZCIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOther: () => (/* binding */ checkBulletOther)\n/* harmony export */ });\n/* harmony import */ var _check_bullet_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ \n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */ function checkBulletOther(state) {\n    const bullet = (0,_check_bullet_js__WEBPACK_IMPORTED_MODULE_0__.checkBullet)(state);\n    const bulletOther = state.options.bulletOther;\n    if (!bulletOther) {\n        return bullet === \"*\" ? \"-\" : \"*\";\n    }\n    if (bulletOther !== \"*\" && bulletOther !== \"+\" && bulletOther !== \"-\") {\n        throw new Error(\"Cannot serialize items with `\" + bulletOther + \"` for `options.bulletOther`, expected `*`, `+`, or `-`\");\n    }\n    if (bulletOther === bullet) {\n        throw new Error(\"Expected `bullet` (`\" + bullet + \"`) and `bulletOther` (`\" + bulletOther + \"`) to be different\");\n    }\n    return bulletOther;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBullet: () => (/* binding */ checkBullet)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */ function checkBullet(state) {\n    const marker = state.options.bullet || \"*\";\n    if (marker !== \"*\" && marker !== \"+\" && marker !== \"-\") {\n        throw new Error(\"Cannot serialize items with `\" + marker + \"` for `options.bullet`, expected `*`, `+`, or `-`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRUQ7OztDQUdDLEdBQ00sU0FBU0EsWUFBWUMsS0FBSztJQUMvQixNQUFNQyxTQUFTRCxNQUFNRSxPQUFPLENBQUNDLE1BQU0sSUFBSTtJQUV2QyxJQUFJRixXQUFXLE9BQU9BLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3RELE1BQU0sSUFBSUcsTUFDUixrQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stYnVsbGV0LmpzP2ZhZTYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydidWxsZXQnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0J1bGxldChzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLmJ1bGxldCB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnKycgJiYgbWFya2VyICE9PSAnLScpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmJ1bGxldGAsIGV4cGVjdGVkIGAqYCwgYCtgLCBvciBgLWAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbImNoZWNrQnVsbGV0Iiwic3RhdGUiLCJtYXJrZXIiLCJvcHRpb25zIiwiYnVsbGV0IiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEmphasis: () => (/* binding */ checkEmphasis)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['emphasis'], null | undefined>}\n */ function checkEmphasis(state) {\n    const marker = state.options.emphasis || \"*\";\n    if (marker !== \"*\" && marker !== \"_\") {\n        throw new Error(\"Cannot serialize emphasis with `\" + marker + \"` for `options.emphasis`, expected `*`, or `_`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxjQUFjQyxLQUFLO0lBQ2pDLE1BQU1DLFNBQVNELE1BQU1FLE9BQU8sQ0FBQ0MsUUFBUSxJQUFJO0lBRXpDLElBQUlGLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3BDLE1BQU0sSUFBSUcsTUFDUixxQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stZW1waGFzaXMuanM/OGIwMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2VtcGhhc2lzJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tFbXBoYXNpcyhzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLmVtcGhhc2lzIHx8ICcqJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcqJyAmJiBtYXJrZXIgIT09ICdfJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGVtcGhhc2lzIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuZW1waGFzaXNgLCBleHBlY3RlZCBgKmAsIG9yIGBfYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOlsiY2hlY2tFbXBoYXNpcyIsInN0YXRlIiwibWFya2VyIiwib3B0aW9ucyIsImVtcGhhc2lzIiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-fence.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFence: () => (/* binding */ checkFence)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['fence'], null | undefined>}\n */ function checkFence(state) {\n    const marker = state.options.fence || \"`\";\n    if (marker !== \"`\" && marker !== \"~\") {\n        throw new Error(\"Cannot serialize code with `\" + marker + \"` for `options.fence`, expected `` ` `` or `~`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1mZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxXQUFXQyxLQUFLO0lBQzlCLE1BQU1DLFNBQVNELE1BQU1FLE9BQU8sQ0FBQ0MsS0FBSyxJQUFJO0lBRXRDLElBQUlGLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3BDLE1BQU0sSUFBSUcsTUFDUixpQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stZmVuY2UuanM/YmVhZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2ZlbmNlJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tGZW5jZShzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLmZlbmNlIHx8ICdgJ1xuXG4gIGlmIChtYXJrZXIgIT09ICdgJyAmJiBtYXJrZXIgIT09ICd+Jykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGNvZGUgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5mZW5jZWAsIGV4cGVjdGVkIGBgIGAgYGAgb3IgYH5gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6WyJjaGVja0ZlbmNlIiwic3RhdGUiLCJtYXJrZXIiLCJvcHRpb25zIiwiZmVuY2UiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js":
/*!********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkListItemIndent: () => (/* binding */ checkListItemIndent)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */ function checkListItemIndent(state) {\n    const style = state.options.listItemIndent || \"one\";\n    if (style !== \"tab\" && style !== \"one\" && style !== \"mixed\") {\n        throw new Error(\"Cannot serialize items with `\" + style + \"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`\");\n    }\n    return style;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1saXN0LWl0ZW0taW5kZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUVEOzs7Q0FHQyxHQUNNLFNBQVNBLG9CQUFvQkMsS0FBSztJQUN2QyxNQUFNQyxRQUFRRCxNQUFNRSxPQUFPLENBQUNDLGNBQWMsSUFBSTtJQUU5QyxJQUFJRixVQUFVLFNBQVNBLFVBQVUsU0FBU0EsVUFBVSxTQUFTO1FBQzNELE1BQU0sSUFBSUcsTUFDUixrQ0FDRUgsUUFDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stbGlzdC1pdGVtLWluZGVudC5qcz80M2M3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snbGlzdEl0ZW1JbmRlbnQnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0xpc3RJdGVtSW5kZW50KHN0YXRlKSB7XG4gIGNvbnN0IHN0eWxlID0gc3RhdGUub3B0aW9ucy5saXN0SXRlbUluZGVudCB8fCAnb25lJ1xuXG4gIGlmIChzdHlsZSAhPT0gJ3RhYicgJiYgc3R5bGUgIT09ICdvbmUnICYmIHN0eWxlICE9PSAnbWl4ZWQnKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgaXRlbXMgd2l0aCBgJyArXG4gICAgICAgIHN0eWxlICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmxpc3RJdGVtSW5kZW50YCwgZXhwZWN0ZWQgYHRhYmAsIGBvbmVgLCBvciBgbWl4ZWRgJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBzdHlsZVxufVxuIl0sIm5hbWVzIjpbImNoZWNrTGlzdEl0ZW1JbmRlbnQiLCJzdGF0ZSIsInN0eWxlIiwib3B0aW9ucyIsImxpc3RJdGVtSW5kZW50IiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-quote.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkQuote: () => (/* binding */ checkQuote)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['quote'], null | undefined>}\n */ function checkQuote(state) {\n    const marker = state.options.quote || '\"';\n    if (marker !== '\"' && marker !== \"'\") {\n        throw new Error(\"Cannot serialize title with `\" + marker + \"` for `options.quote`, expected `\\\"`, or `'`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1xdW90ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxXQUFXQyxLQUFLO0lBQzlCLE1BQU1DLFNBQVNELE1BQU1FLE9BQU8sQ0FBQ0MsS0FBSyxJQUFJO0lBRXRDLElBQUlGLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3BDLE1BQU0sSUFBSUcsTUFDUixrQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stcXVvdGUuanM/MzViNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ3F1b3RlJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tRdW90ZShzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLnF1b3RlIHx8ICdcIidcblxuICBpZiAobWFya2VyICE9PSAnXCInICYmIG1hcmtlciAhPT0gXCInXCIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSB0aXRsZSB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLnF1b3RlYCwgZXhwZWN0ZWQgYFwiYCwgb3IgYFxcJ2AnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbImNoZWNrUXVvdGUiLCJzdGF0ZSIsIm1hcmtlciIsIm9wdGlvbnMiLCJxdW90ZSIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRuleRepetition: () => (/* binding */ checkRuleRepetition)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['ruleRepetition'], null | undefined>}\n */ function checkRuleRepetition(state) {\n    const repetition = state.options.ruleRepetition || 3;\n    if (repetition < 3) {\n        throw new Error(\"Cannot serialize rules with repetition `\" + repetition + \"` for `options.ruleRepetition`, expected `3` or more\");\n    }\n    return repetition;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLXJlcGV0aXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRUQ7OztDQUdDLEdBQ00sU0FBU0Esb0JBQW9CQyxLQUFLO0lBQ3ZDLE1BQU1DLGFBQWFELE1BQU1FLE9BQU8sQ0FBQ0MsY0FBYyxJQUFJO0lBRW5ELElBQUlGLGFBQWEsR0FBRztRQUNsQixNQUFNLElBQUlHLE1BQ1IsNkNBQ0VILGFBQ0E7SUFFTjtJQUVBLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXJ1bGUtcmVwZXRpdGlvbi5qcz8zZTIwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1sncnVsZVJlcGV0aXRpb24nXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1J1bGVSZXBldGl0aW9uKHN0YXRlKSB7XG4gIGNvbnN0IHJlcGV0aXRpb24gPSBzdGF0ZS5vcHRpb25zLnJ1bGVSZXBldGl0aW9uIHx8IDNcblxuICBpZiAocmVwZXRpdGlvbiA8IDMpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBydWxlcyB3aXRoIHJlcGV0aXRpb24gYCcgK1xuICAgICAgICByZXBldGl0aW9uICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLnJ1bGVSZXBldGl0aW9uYCwgZXhwZWN0ZWQgYDNgIG9yIG1vcmUnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIHJlcGV0aXRpb25cbn1cbiJdLCJuYW1lcyI6WyJjaGVja1J1bGVSZXBldGl0aW9uIiwic3RhdGUiLCJyZXBldGl0aW9uIiwib3B0aW9ucyIsInJ1bGVSZXBldGl0aW9uIiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRule: () => (/* binding */ checkRule)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['rule'], null | undefined>}\n */ function checkRule(state) {\n    const marker = state.options.rule || \"*\";\n    if (marker !== \"*\" && marker !== \"-\" && marker !== \"_\") {\n        throw new Error(\"Cannot serialize rules with `\" + marker + \"` for `options.rule`, expected `*`, `-`, or `_`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUVEOzs7Q0FHQyxHQUNNLFNBQVNBLFVBQVVDLEtBQUs7SUFDN0IsTUFBTUMsU0FBU0QsTUFBTUUsT0FBTyxDQUFDQyxJQUFJLElBQUk7SUFFckMsSUFBSUYsV0FBVyxPQUFPQSxXQUFXLE9BQU9BLFdBQVcsS0FBSztRQUN0RCxNQUFNLElBQUlHLE1BQ1Isa0NBQ0VILFNBQ0E7SUFFTjtJQUVBLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXJ1bGUuanM/YjUwNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ3J1bGUnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1J1bGUoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5ydWxlIHx8ICcqJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcqJyAmJiBtYXJrZXIgIT09ICctJyAmJiBtYXJrZXIgIT09ICdfJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHJ1bGVzIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMucnVsZWAsIGV4cGVjdGVkIGAqYCwgYC1gLCBvciBgX2AnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbImNoZWNrUnVsZSIsInN0YXRlIiwibWFya2VyIiwib3B0aW9ucyIsInJ1bGUiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-strong.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkStrong: () => (/* binding */ checkStrong)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['strong'], null | undefined>}\n */ function checkStrong(state) {\n    const marker = state.options.strong || \"*\";\n    if (marker !== \"*\" && marker !== \"_\") {\n        throw new Error(\"Cannot serialize strong with `\" + marker + \"` for `options.strong`, expected `*`, or `_`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRUQ7OztDQUdDLEdBQ00sU0FBU0EsWUFBWUMsS0FBSztJQUMvQixNQUFNQyxTQUFTRCxNQUFNRSxPQUFPLENBQUNDLE1BQU0sSUFBSTtJQUV2QyxJQUFJRixXQUFXLE9BQU9BLFdBQVcsS0FBSztRQUNwQyxNQUFNLElBQUlHLE1BQ1IsbUNBQ0VILFNBQ0E7SUFFTjtJQUVBLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXN0cm9uZy5qcz9jMGM2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snc3Ryb25nJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tTdHJvbmcoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5zdHJvbmcgfHwgJyonXG5cbiAgaWYgKG1hcmtlciAhPT0gJyonICYmIG1hcmtlciAhPT0gJ18nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgc3Ryb25nIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuc3Ryb25nYCwgZXhwZWN0ZWQgYCpgLCBvciBgX2AnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbImNoZWNrU3Ryb25nIiwic3RhdGUiLCJtYXJrZXIiLCJvcHRpb25zIiwic3Ryb25nIiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js":
/*!************************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeCharacterReference: () => (/* binding */ encodeCharacterReference)\n/* harmony export */ });\n/**\n * Encode a code point as a character reference.\n *\n * @param {number} code\n *   Code point to encode.\n * @returns {string}\n *   Encoded character reference.\n */ function encodeCharacterReference(code) {\n    return \"&#x\" + code.toString(16).toUpperCase() + \";\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9lbmNvZGUtY2hhcmFjdGVyLXJlZmVyZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Ozs7Q0FPQyxHQUNNLFNBQVNBLHlCQUF5QkMsSUFBSTtJQUMzQyxPQUFPLFFBQVFBLEtBQUtDLFFBQVEsQ0FBQyxJQUFJQyxXQUFXLEtBQUs7QUFDbkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2VuY29kZS1jaGFyYWN0ZXItcmVmZXJlbmNlLmpzP2M1MjAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFbmNvZGUgYSBjb2RlIHBvaW50IGFzIGEgY2hhcmFjdGVyIHJlZmVyZW5jZS5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gY29kZVxuICogICBDb2RlIHBvaW50IHRvIGVuY29kZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIEVuY29kZWQgY2hhcmFjdGVyIHJlZmVyZW5jZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGVuY29kZUNoYXJhY3RlclJlZmVyZW5jZShjb2RlKSB7XG4gIHJldHVybiAnJiN4JyArIGNvZGUudG9TdHJpbmcoMTYpLnRvVXBwZXJDYXNlKCkgKyAnOydcbn1cbiJdLCJuYW1lcyI6WyJlbmNvZGVDaGFyYWN0ZXJSZWZlcmVuY2UiLCJjb2RlIiwidG9TdHJpbmciLCJ0b1VwcGVyQ2FzZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-info.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeInfo: () => (/* binding */ encodeInfo)\n/* harmony export */ });\n/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-classify-character */ \"(ssr)/./node_modules/micromark-util-classify-character/dev/index.js\");\n/**\n * @import {EncodeSides} from '../types.js'\n */ \n/**\n * Check whether to encode (as a character reference) the characters\n * surrounding an attention run.\n *\n * Which characters are around an attention run influence whether it works or\n * not.\n *\n * See <https://github.com/orgs/syntax-tree/discussions/60> for more info.\n * See this markdown in a particular renderer to see what works:\n *\n * ```markdown\n * |                         | A (letter inside) | B (punctuation inside) | C (whitespace inside) | D (nothing inside) |\n * | ----------------------- | ----------------- | ---------------------- | --------------------- | ------------------ |\n * | 1 (letter outside)      | x*y*z             | x*.*z                  | x* *z                 | x**z               |\n * | 2 (punctuation outside) | .*y*.             | .*.*.                  | .* *.                 | .**.               |\n * | 3 (whitespace outside)  | x *y* z           | x *.* z                | x * * z               | x ** z             |\n * | 4 (nothing outside)     | *x*               | *.*                    | * *                   | **                 |\n * ```\n *\n * @param {number} outside\n *   Code point on the outer side of the run.\n * @param {number} inside\n *   Code point on the inner side of the run.\n * @param {'*' | '_'} marker\n *   Marker of the run.\n *   Underscores are handled more strictly (they form less often) than\n *   asterisks.\n * @returns {EncodeSides}\n *   Whether to encode characters.\n */ // Important: punctuation must never be encoded.\n// Punctuation is solely used by markdown constructs.\n// And by encoding itself.\n// Encoding them will break constructs or double encode things.\nfunction encodeInfo(outside, inside, marker) {\n    const outsideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(outside);\n    const insideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(inside);\n    // Letter outside:\n    if (outsideKind === undefined) {\n        return insideKind === undefined ? // we have to encode *both* letters for `_` as it is looser.\n        // it already forms for `*` (and GFMs `~`).\n        marker === \"_\" ? {\n            inside: true,\n            outside: true\n        } : {\n            inside: false,\n            outside: false\n        } : insideKind === 1 ? {\n            inside: true,\n            outside: true\n        } : {\n            inside: false,\n            outside: true\n        };\n    }\n    // Whitespace outside:\n    if (outsideKind === 1) {\n        return insideKind === undefined ? {\n            inside: false,\n            outside: false\n        } : insideKind === 1 ? {\n            inside: true,\n            outside: true\n        } : {\n            inside: false,\n            outside: false\n        };\n    }\n    // Punctuation outside:\n    return insideKind === undefined ? {\n        inside: false,\n        outside: false\n    } : insideKind === 1 ? {\n        inside: true,\n        outside: false\n    } : {\n        inside: false,\n        outside: false\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9lbmNvZGUtaW5mby5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBOztDQUVDLEdBRWtFO0FBRW5FOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQTZCQyxHQUNELGdEQUFnRDtBQUNoRCxxREFBcUQ7QUFDckQsMEJBQTBCO0FBQzFCLCtEQUErRDtBQUN4RCxTQUFTQyxXQUFXQyxPQUFPLEVBQUVDLE1BQU0sRUFBRUMsTUFBTTtJQUNoRCxNQUFNQyxjQUFjTCxvRkFBaUJBLENBQUNFO0lBQ3RDLE1BQU1JLGFBQWFOLG9GQUFpQkEsQ0FBQ0c7SUFFckMsa0JBQWtCO0lBQ2xCLElBQUlFLGdCQUFnQkUsV0FBVztRQUM3QixPQUFPRCxlQUFlQyxZQUVsQiw0REFBNEQ7UUFDNUQsMkNBQTJDO1FBQzNDSCxXQUFXLE1BQ1Q7WUFBQ0QsUUFBUTtZQUFNRCxTQUFTO1FBQUksSUFDNUI7WUFBQ0MsUUFBUTtZQUFPRCxTQUFTO1FBQUssSUFDaENJLGVBQWUsSUFFYjtZQUFDSCxRQUFRO1lBQU1ELFNBQVM7UUFBSSxJQUU1QjtZQUFDQyxRQUFRO1lBQU9ELFNBQVM7UUFBSTtJQUNyQztJQUVBLHNCQUFzQjtJQUN0QixJQUFJRyxnQkFBZ0IsR0FBRztRQUNyQixPQUFPQyxlQUFlQyxZQUVsQjtZQUFDSixRQUFRO1lBQU9ELFNBQVM7UUFBSyxJQUM5QkksZUFBZSxJQUViO1lBQUNILFFBQVE7WUFBTUQsU0FBUztRQUFJLElBRTVCO1lBQUNDLFFBQVE7WUFBT0QsU0FBUztRQUFLO0lBQ3RDO0lBRUEsdUJBQXVCO0lBQ3ZCLE9BQU9JLGVBQWVDLFlBRWxCO1FBQUNKLFFBQVE7UUFBT0QsU0FBUztJQUFLLElBQzlCSSxlQUFlLElBRWI7UUFBQ0gsUUFBUTtRQUFNRCxTQUFTO0lBQUssSUFFN0I7UUFBQ0MsUUFBUTtRQUFPRCxTQUFTO0lBQUs7QUFDdEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2VuY29kZS1pbmZvLmpzP2FiMGMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtFbmNvZGVTaWRlc30gZnJvbSAnLi4vdHlwZXMuanMnXG4gKi9cblxuaW1wb3J0IHtjbGFzc2lmeUNoYXJhY3Rlcn0gZnJvbSAnbWljcm9tYXJrLXV0aWwtY2xhc3NpZnktY2hhcmFjdGVyJ1xuXG4vKipcbiAqIENoZWNrIHdoZXRoZXIgdG8gZW5jb2RlIChhcyBhIGNoYXJhY3RlciByZWZlcmVuY2UpIHRoZSBjaGFyYWN0ZXJzXG4gKiBzdXJyb3VuZGluZyBhbiBhdHRlbnRpb24gcnVuLlxuICpcbiAqIFdoaWNoIGNoYXJhY3RlcnMgYXJlIGFyb3VuZCBhbiBhdHRlbnRpb24gcnVuIGluZmx1ZW5jZSB3aGV0aGVyIGl0IHdvcmtzIG9yXG4gKiBub3QuXG4gKlxuICogU2VlIDxodHRwczovL2dpdGh1Yi5jb20vb3Jncy9zeW50YXgtdHJlZS9kaXNjdXNzaW9ucy82MD4gZm9yIG1vcmUgaW5mby5cbiAqIFNlZSB0aGlzIG1hcmtkb3duIGluIGEgcGFydGljdWxhciByZW5kZXJlciB0byBzZWUgd2hhdCB3b3JrczpcbiAqXG4gKiBgYGBtYXJrZG93blxuICogfCAgICAgICAgICAgICAgICAgICAgICAgICB8IEEgKGxldHRlciBpbnNpZGUpIHwgQiAocHVuY3R1YXRpb24gaW5zaWRlKSB8IEMgKHdoaXRlc3BhY2UgaW5zaWRlKSB8IEQgKG5vdGhpbmcgaW5zaWRlKSB8XG4gKiB8IC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIHwgLS0tLS0tLS0tLS0tLS0tLS0gfCAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tIHwgLS0tLS0tLS0tLS0tLS0tLS0tLS0tIHwgLS0tLS0tLS0tLS0tLS0tLS0tIHxcbiAqIHwgMSAobGV0dGVyIG91dHNpZGUpICAgICAgfCB4KnkqeiAgICAgICAgICAgICB8IHgqLip6ICAgICAgICAgICAgICAgICAgfCB4KiAqeiAgICAgICAgICAgICAgICAgfCB4Kip6ICAgICAgICAgICAgICAgfFxuICogfCAyIChwdW5jdHVhdGlvbiBvdXRzaWRlKSB8IC4qeSouICAgICAgICAgICAgIHwgLiouKi4gICAgICAgICAgICAgICAgICB8IC4qICouICAgICAgICAgICAgICAgICB8IC4qKi4gICAgICAgICAgICAgICB8XG4gKiB8IDMgKHdoaXRlc3BhY2Ugb3V0c2lkZSkgIHwgeCAqeSogeiAgICAgICAgICAgfCB4ICouKiB6ICAgICAgICAgICAgICAgIHwgeCAqICogeiAgICAgICAgICAgICAgIHwgeCAqKiB6ICAgICAgICAgICAgIHxcbiAqIHwgNCAobm90aGluZyBvdXRzaWRlKSAgICAgfCAqeCogICAgICAgICAgICAgICB8ICouKiAgICAgICAgICAgICAgICAgICAgfCAqICogICAgICAgICAgICAgICAgICAgfCAqKiAgICAgICAgICAgICAgICAgfFxuICogYGBgXG4gKlxuICogQHBhcmFtIHtudW1iZXJ9IG91dHNpZGVcbiAqICAgQ29kZSBwb2ludCBvbiB0aGUgb3V0ZXIgc2lkZSBvZiB0aGUgcnVuLlxuICogQHBhcmFtIHtudW1iZXJ9IGluc2lkZVxuICogICBDb2RlIHBvaW50IG9uIHRoZSBpbm5lciBzaWRlIG9mIHRoZSBydW4uXG4gKiBAcGFyYW0geycqJyB8ICdfJ30gbWFya2VyXG4gKiAgIE1hcmtlciBvZiB0aGUgcnVuLlxuICogICBVbmRlcnNjb3JlcyBhcmUgaGFuZGxlZCBtb3JlIHN0cmljdGx5ICh0aGV5IGZvcm0gbGVzcyBvZnRlbikgdGhhblxuICogICBhc3Rlcmlza3MuXG4gKiBAcmV0dXJucyB7RW5jb2RlU2lkZXN9XG4gKiAgIFdoZXRoZXIgdG8gZW5jb2RlIGNoYXJhY3RlcnMuXG4gKi9cbi8vIEltcG9ydGFudDogcHVuY3R1YXRpb24gbXVzdCBuZXZlciBiZSBlbmNvZGVkLlxuLy8gUHVuY3R1YXRpb24gaXMgc29sZWx5IHVzZWQgYnkgbWFya2Rvd24gY29uc3RydWN0cy5cbi8vIEFuZCBieSBlbmNvZGluZyBpdHNlbGYuXG4vLyBFbmNvZGluZyB0aGVtIHdpbGwgYnJlYWsgY29uc3RydWN0cyBvciBkb3VibGUgZW5jb2RlIHRoaW5ncy5cbmV4cG9ydCBmdW5jdGlvbiBlbmNvZGVJbmZvKG91dHNpZGUsIGluc2lkZSwgbWFya2VyKSB7XG4gIGNvbnN0IG91dHNpZGVLaW5kID0gY2xhc3NpZnlDaGFyYWN0ZXIob3V0c2lkZSlcbiAgY29uc3QgaW5zaWRlS2luZCA9IGNsYXNzaWZ5Q2hhcmFjdGVyKGluc2lkZSlcblxuICAvLyBMZXR0ZXIgb3V0c2lkZTpcbiAgaWYgKG91dHNpZGVLaW5kID09PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gaW5zaWRlS2luZCA9PT0gdW5kZWZpbmVkXG4gICAgICA/IC8vIExldHRlciBpbnNpZGU6XG4gICAgICAgIC8vIHdlIGhhdmUgdG8gZW5jb2RlICpib3RoKiBsZXR0ZXJzIGZvciBgX2AgYXMgaXQgaXMgbG9vc2VyLlxuICAgICAgICAvLyBpdCBhbHJlYWR5IGZvcm1zIGZvciBgKmAgKGFuZCBHRk1zIGB+YCkuXG4gICAgICAgIG1hcmtlciA9PT0gJ18nXG4gICAgICAgID8ge2luc2lkZTogdHJ1ZSwgb3V0c2lkZTogdHJ1ZX1cbiAgICAgICAgOiB7aW5zaWRlOiBmYWxzZSwgb3V0c2lkZTogZmFsc2V9XG4gICAgICA6IGluc2lkZUtpbmQgPT09IDFcbiAgICAgICAgPyAvLyBXaGl0ZXNwYWNlIGluc2lkZTogZW5jb2RlIGJvdGggKGxldHRlciwgd2hpdGVzcGFjZSkuXG4gICAgICAgICAge2luc2lkZTogdHJ1ZSwgb3V0c2lkZTogdHJ1ZX1cbiAgICAgICAgOiAvLyBQdW5jdHVhdGlvbiBpbnNpZGU6IGVuY29kZSBvdXRlciAobGV0dGVyKVxuICAgICAgICAgIHtpbnNpZGU6IGZhbHNlLCBvdXRzaWRlOiB0cnVlfVxuICB9XG5cbiAgLy8gV2hpdGVzcGFjZSBvdXRzaWRlOlxuICBpZiAob3V0c2lkZUtpbmQgPT09IDEpIHtcbiAgICByZXR1cm4gaW5zaWRlS2luZCA9PT0gdW5kZWZpbmVkXG4gICAgICA/IC8vIExldHRlciBpbnNpZGU6IGFscmVhZHkgZm9ybXMuXG4gICAgICAgIHtpbnNpZGU6IGZhbHNlLCBvdXRzaWRlOiBmYWxzZX1cbiAgICAgIDogaW5zaWRlS2luZCA9PT0gMVxuICAgICAgICA/IC8vIFdoaXRlc3BhY2UgaW5zaWRlOiBlbmNvZGUgYm90aCAod2hpdGVzcGFjZSkuXG4gICAgICAgICAge2luc2lkZTogdHJ1ZSwgb3V0c2lkZTogdHJ1ZX1cbiAgICAgICAgOiAvLyBQdW5jdHVhdGlvbiBpbnNpZGU6IGFscmVhZHkgZm9ybXMuXG4gICAgICAgICAge2luc2lkZTogZmFsc2UsIG91dHNpZGU6IGZhbHNlfVxuICB9XG5cbiAgLy8gUHVuY3R1YXRpb24gb3V0c2lkZTpcbiAgcmV0dXJuIGluc2lkZUtpbmQgPT09IHVuZGVmaW5lZFxuICAgID8gLy8gTGV0dGVyIGluc2lkZTogYWxyZWFkeSBmb3Jtcy5cbiAgICAgIHtpbnNpZGU6IGZhbHNlLCBvdXRzaWRlOiBmYWxzZX1cbiAgICA6IGluc2lkZUtpbmQgPT09IDFcbiAgICAgID8gLy8gV2hpdGVzcGFjZSBpbnNpZGU6IGVuY29kZSBpbm5lciAod2hpdGVzcGFjZSkuXG4gICAgICAgIHtpbnNpZGU6IHRydWUsIG91dHNpZGU6IGZhbHNlfVxuICAgICAgOiAvLyBQdW5jdHVhdGlvbiBpbnNpZGU6IGFscmVhZHkgZm9ybXMuXG4gICAgICAgIHtpbnNpZGU6IGZhbHNlLCBvdXRzaWRlOiBmYWxzZX1cbn1cbiJdLCJuYW1lcyI6WyJjbGFzc2lmeUNoYXJhY3RlciIsImVuY29kZUluZm8iLCJvdXRzaWRlIiwiaW5zaWRlIiwibWFya2VyIiwib3V0c2lkZUtpbmQiLCJpbnNpZGVLaW5kIiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCodeAsIndented: () => (/* binding */ formatCodeAsIndented)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Code} from 'mdast'\n */ /**\n * @param {Code} node\n * @param {State} state\n * @returns {boolean}\n */ function formatCodeAsIndented(node, state) {\n    return Boolean(state.options.fences === false && node.value && // If there’s no info…\n    !node.lang && // And there’s a non-whitespace character…\n    /[^ \\r\\n]/.test(node.value) && // And the value doesn’t start or end in a blank…\n    !/^[\\t ]*(?:[\\r\\n]|$)|(?:^|[\\r\\n])[\\t ]*$/.test(node.value));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtY29kZS1hcy1pbmRlbnRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7OztDQUdDLEdBRUQ7Ozs7Q0FJQyxHQUNNLFNBQVNBLHFCQUFxQkMsSUFBSSxFQUFFQyxLQUFLO0lBQzlDLE9BQU9DLFFBQ0xELE1BQU1FLE9BQU8sQ0FBQ0MsTUFBTSxLQUFLLFNBQ3ZCSixLQUFLSyxLQUFLLElBQ1Ysc0JBQXNCO0lBQ3RCLENBQUNMLEtBQUtNLElBQUksSUFDViwwQ0FBMEM7SUFDMUMsV0FBV0MsSUFBSSxDQUFDUCxLQUFLSyxLQUFLLEtBQzFCLGlEQUFpRDtJQUNqRCxDQUFDLDBDQUEwQ0UsSUFBSSxDQUFDUCxLQUFLSyxLQUFLO0FBRWhFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtY29kZS1hcy1pbmRlbnRlZC5qcz8xZDZiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtDb2RlfSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7Q29kZX0gbm9kZVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0Q29kZUFzSW5kZW50ZWQobm9kZSwgc3RhdGUpIHtcbiAgcmV0dXJuIEJvb2xlYW4oXG4gICAgc3RhdGUub3B0aW9ucy5mZW5jZXMgPT09IGZhbHNlICYmXG4gICAgICBub2RlLnZhbHVlICYmXG4gICAgICAvLyBJZiB0aGVyZeKAmXMgbm8gaW5mb+KAplxuICAgICAgIW5vZGUubGFuZyAmJlxuICAgICAgLy8gQW5kIHRoZXJl4oCZcyBhIG5vbi13aGl0ZXNwYWNlIGNoYXJhY3RlcuKAplxuICAgICAgL1teIFxcclxcbl0vLnRlc3Qobm9kZS52YWx1ZSkgJiZcbiAgICAgIC8vIEFuZCB0aGUgdmFsdWUgZG9lc27igJl0IHN0YXJ0IG9yIGVuZCBpbiBhIGJsYW5r4oCmXG4gICAgICAhL15bXFx0IF0qKD86W1xcclxcbl18JCl8KD86XnxbXFxyXFxuXSlbXFx0IF0qJC8udGVzdChub2RlLnZhbHVlKVxuICApXG59XG4iXSwibmFtZXMiOlsiZm9ybWF0Q29kZUFzSW5kZW50ZWQiLCJub2RlIiwic3RhdGUiLCJCb29sZWFuIiwib3B0aW9ucyIsImZlbmNlcyIsInZhbHVlIiwibGFuZyIsInRlc3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatHeadingAsSetext: () => (/* binding */ formatHeadingAsSetext)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Heading} from 'mdast'\n */ \n\n/**\n * @param {Heading} node\n * @param {State} state\n * @returns {boolean}\n */ function formatHeadingAsSetext(node, state) {\n    let literalWithBreak = false;\n    // Look for literals with a line break.\n    // Note that this also\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(node, function(node) {\n        if (\"value\" in node && /\\r?\\n|\\r/.test(node.value) || node.type === \"break\") {\n            literalWithBreak = true;\n            return unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.EXIT;\n        }\n    });\n    return Boolean((!node.depth || node.depth < 3) && (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__.toString)(node) && (state.options.setext || literalWithBreak));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLinkAsAutolink: () => (/* binding */ formatLinkAsAutolink)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Link} from 'mdast'\n */ \n/**\n * @param {Link} node\n * @param {State} state\n * @returns {boolean}\n */ function formatLinkAsAutolink(node, state) {\n    const raw = (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__.toString)(node);\n    return Boolean(!state.options.resourceLink && // If there’s a url…\n    node.url && // And there’s a no title…\n    !node.title && // And the content of `node` is a single text node…\n    node.children && node.children.length === 1 && node.children[0].type === \"text\" && // And if the url is the same as the content…\n    (raw === node.url || \"mailto:\" + raw === node.url) && // And that starts w/ a protocol…\n    /^[a-z][a-z+.-]+:/i.test(node.url) && // And that doesn’t contain ASCII control codes (character escapes and\n    // references don’t work), space, or angle brackets…\n    !/[\\0- <>\\u007F]/.test(node.url));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patternInScope: () => (/* binding */ patternInScope)\n/* harmony export */ });\n/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */ /**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe} pattern\n * @returns {boolean}\n */ function patternInScope(stack, pattern) {\n    return listInScope(stack, pattern.inConstruct, true) && !listInScope(stack, pattern.notInConstruct, false);\n}\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe['inConstruct']} list\n * @param {boolean} none\n * @returns {boolean}\n */ function listInScope(stack, list, none) {\n    if (typeof list === \"string\") {\n        list = [\n            list\n        ];\n    }\n    if (!list || list.length === 0) {\n        return none;\n    }\n    let index = -1;\n    while(++index < list.length){\n        if (stack.includes(list[index])) {\n            return true;\n        }\n    }\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\n");

/***/ })

};
;