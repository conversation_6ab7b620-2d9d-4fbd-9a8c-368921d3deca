"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-strikethrough";
exports.ids = ["vendor-chunks/mdast-util-gfm-strikethrough"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-strikethrough/lib/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-strikethrough/lib/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmStrikethroughFromMarkdown: () => (/* binding */ gfmStrikethroughFromMarkdown),\n/* harmony export */   gfmStrikethroughToMarkdown: () => (/* binding */ gfmStrikethroughToMarkdown)\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Delete} Delete\n *\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n *\n * @typedef {import('mdast-util-to-markdown').ConstructName} ConstructName\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n */ /**\n * List of constructs that occur in phrasing (paragraphs, headings), but cannot\n * contain strikethrough.\n * So they sort of cancel each other out.\n * Note: could use a better name.\n *\n * Note: keep in sync with: <https://github.com/syntax-tree/mdast-util-to-markdown/blob/8ce8dbf/lib/unsafe.js#L14>\n *\n * @type {Array<ConstructName>}\n */ const constructsWithoutStrikethrough = [\n    \"autolink\",\n    \"destinationLiteral\",\n    \"destinationRaw\",\n    \"reference\",\n    \"titleQuote\",\n    \"titleApostrophe\"\n];\nhandleDelete.peek = peekDelete;\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM\n * strikethrough in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown` to enable GFM strikethrough.\n */ function gfmStrikethroughFromMarkdown() {\n    return {\n        canContainEols: [\n            \"delete\"\n        ],\n        enter: {\n            strikethrough: enterStrikethrough\n        },\n        exit: {\n            strikethrough: exitStrikethrough\n        }\n    };\n}\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM\n * strikethrough in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM strikethrough.\n */ function gfmStrikethroughToMarkdown() {\n    return {\n        unsafe: [\n            {\n                character: \"~\",\n                inConstruct: \"phrasing\",\n                notInConstruct: constructsWithoutStrikethrough\n            }\n        ],\n        handlers: {\n            delete: handleDelete\n        }\n    };\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterStrikethrough(token) {\n    this.enter({\n        type: \"delete\",\n        children: []\n    }, token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitStrikethrough(token) {\n    this.exit(token);\n}\n/**\n * @type {ToMarkdownHandle}\n * @param {Delete} node\n */ function handleDelete(node, _, state, info) {\n    const tracker = state.createTracker(info);\n    const exit = state.enter(\"strikethrough\");\n    let value = tracker.move(\"~~\");\n    value += state.containerPhrasing(node, {\n        ...tracker.current(),\n        before: value,\n        after: \"~\"\n    });\n    value += tracker.move(\"~~\");\n    exit();\n    return value;\n}\n/** @type {ToMarkdownHandle} */ function peekDelete() {\n    return \"~\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-strikethrough/lib/index.js\n");

/***/ })

};
;