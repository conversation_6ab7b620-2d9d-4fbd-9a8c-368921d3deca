"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-hast";
exports.ids = ["vendor-chunks/mdast-util-to-hast"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js":
/*!*******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/footer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultFootnoteBackContent: () => (/* binding */ defaultFootnoteBackContent),\n/* harmony export */   defaultFootnoteBackLabel: () => (/* binding */ defaultFootnoteBackLabel),\n/* harmony export */   footer: () => (/* binding */ footer)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */ /**\n * @callback FootnoteBackContentTemplate\n *   Generate content for the backreference dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent> | ElementContent | string}\n *   Content for the backreference when linking back from definitions to their\n *   reference.\n *\n * @callback FootnoteBackLabelTemplate\n *   Generate a back label dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Back label to use when linking back from definitions to their reference.\n */ \n\n/**\n * Generate the default content that GitHub uses on backreferences.\n *\n * @param {number} _\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent>}\n *   Content.\n */ function defaultFootnoteBackContent(_, rereferenceIndex) {\n    /** @type {Array<ElementContent>} */ const result = [\n        {\n            type: \"text\",\n            value: \"↩\"\n        }\n    ];\n    if (rereferenceIndex > 1) {\n        result.push({\n            type: \"element\",\n            tagName: \"sup\",\n            properties: {},\n            children: [\n                {\n                    type: \"text\",\n                    value: String(rereferenceIndex)\n                }\n            ]\n        });\n    }\n    return result;\n}\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Label.\n */ function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n    return \"Back to reference \" + (referenceIndex + 1) + (rereferenceIndex > 1 ? \"-\" + rereferenceIndex : \"\");\n}\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */ // eslint-disable-next-line complexity\nfunction footer(state) {\n    const clobberPrefix = typeof state.options.clobberPrefix === \"string\" ? state.options.clobberPrefix : \"user-content-\";\n    const footnoteBackContent = state.options.footnoteBackContent || defaultFootnoteBackContent;\n    const footnoteBackLabel = state.options.footnoteBackLabel || defaultFootnoteBackLabel;\n    const footnoteLabel = state.options.footnoteLabel || \"Footnotes\";\n    const footnoteLabelTagName = state.options.footnoteLabelTagName || \"h2\";\n    const footnoteLabelProperties = state.options.footnoteLabelProperties || {\n        className: [\n            \"sr-only\"\n        ]\n    };\n    /** @type {Array<ElementContent>} */ const listItems = [];\n    let referenceIndex = -1;\n    while(++referenceIndex < state.footnoteOrder.length){\n        const definition = state.footnoteById.get(state.footnoteOrder[referenceIndex]);\n        if (!definition) {\n            continue;\n        }\n        const content = state.all(definition);\n        const id = String(definition.identifier).toUpperCase();\n        const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(id.toLowerCase());\n        let rereferenceIndex = 0;\n        /** @type {Array<ElementContent>} */ const backReferences = [];\n        const counts = state.footnoteCounts.get(id);\n        // eslint-disable-next-line no-unmodified-loop-condition\n        while(counts !== undefined && ++rereferenceIndex <= counts){\n            if (backReferences.length > 0) {\n                backReferences.push({\n                    type: \"text\",\n                    value: \" \"\n                });\n            }\n            let children = typeof footnoteBackContent === \"string\" ? footnoteBackContent : footnoteBackContent(referenceIndex, rereferenceIndex);\n            if (typeof children === \"string\") {\n                children = {\n                    type: \"text\",\n                    value: children\n                };\n            }\n            backReferences.push({\n                type: \"element\",\n                tagName: \"a\",\n                properties: {\n                    href: \"#\" + clobberPrefix + \"fnref-\" + safeId + (rereferenceIndex > 1 ? \"-\" + rereferenceIndex : \"\"),\n                    dataFootnoteBackref: \"\",\n                    ariaLabel: typeof footnoteBackLabel === \"string\" ? footnoteBackLabel : footnoteBackLabel(referenceIndex, rereferenceIndex),\n                    className: [\n                        \"data-footnote-backref\"\n                    ]\n                },\n                children: Array.isArray(children) ? children : [\n                    children\n                ]\n            });\n        }\n        const tail = content[content.length - 1];\n        if (tail && tail.type === \"element\" && tail.tagName === \"p\") {\n            const tailTail = tail.children[tail.children.length - 1];\n            if (tailTail && tailTail.type === \"text\") {\n                tailTail.value += \" \";\n            } else {\n                tail.children.push({\n                    type: \"text\",\n                    value: \" \"\n                });\n            }\n            tail.children.push(...backReferences);\n        } else {\n            content.push(...backReferences);\n        }\n        /** @type {Element} */ const listItem = {\n            type: \"element\",\n            tagName: \"li\",\n            properties: {\n                id: clobberPrefix + \"fn-\" + safeId\n            },\n            children: state.wrap(content, true)\n        };\n        state.patch(definition, listItem);\n        listItems.push(listItem);\n    }\n    if (listItems.length === 0) {\n        return;\n    }\n    return {\n        type: \"element\",\n        tagName: \"section\",\n        properties: {\n            dataFootnotes: true,\n            className: [\n                \"footnotes\"\n            ]\n        },\n        children: [\n            {\n                type: \"element\",\n                tagName: footnoteLabelTagName,\n                properties: {\n                    ...(0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(footnoteLabelProperties),\n                    id: \"footnote-label\"\n                },\n                children: [\n                    {\n                        type: \"text\",\n                        value: footnoteLabel\n                    }\n                ]\n            },\n            {\n                type: \"text\",\n                value: \"\\n\"\n            },\n            {\n                type: \"element\",\n                tagName: \"ol\",\n                properties: {},\n                children: state.wrap(listItems, true)\n            },\n            {\n                type: \"text\",\n                value: \"\\n\"\n            }\n        ]\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function blockquote(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"blockquote\",\n        properties: {},\n        children: state.wrap(state.all(node), true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7OztDQUlDLEdBRUQsbURBQW1EO0FBQ25EO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0EsV0FBV0MsS0FBSyxFQUFFQyxJQUFJO0lBQ3BDLG9CQUFvQixHQUNwQixNQUFNQyxTQUFTO1FBQ2JDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxZQUFZLENBQUM7UUFDYkMsVUFBVU4sTUFBTU8sSUFBSSxDQUFDUCxNQUFNUSxHQUFHLENBQUNQLE9BQU87SUFDeEM7SUFDQUQsTUFBTVMsS0FBSyxDQUFDUixNQUFNQztJQUNsQixPQUFPRixNQUFNVSxTQUFTLENBQUNULE1BQU1DO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzP2RhOTMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5CbG9ja3F1b3RlfSBCbG9ja3F1b3RlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBibG9ja3F1b3RlYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0Jsb2NrcXVvdGV9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBibG9ja3F1b3RlKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnYmxvY2txdW90ZScsXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IHN0YXRlLndyYXAoc3RhdGUuYWxsKG5vZGUpLCB0cnVlKVxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOlsiYmxvY2txdW90ZSIsInN0YXRlIiwibm9kZSIsInJlc3VsdCIsInR5cGUiLCJ0YWdOYW1lIiwicHJvcGVydGllcyIsImNoaWxkcmVuIiwid3JhcCIsImFsbCIsInBhdGNoIiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/break.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */ function hardBreak(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"br\",\n        properties: {},\n        children: []\n    };\n    state.patch(node, result);\n    return [\n        state.applyData(node, result),\n        {\n            type: \"text\",\n            value: \"\\n\"\n        }\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9icmVhay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7O0NBS0MsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxVQUFVQyxLQUFLLEVBQUVDLElBQUk7SUFDbkMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFBQ0MsTUFBTTtRQUFXQyxTQUFTO1FBQU1DLFlBQVksQ0FBQztRQUFHQyxVQUFVLEVBQUU7SUFBQTtJQUM1RU4sTUFBTU8sS0FBSyxDQUFDTixNQUFNQztJQUNsQixPQUFPO1FBQUNGLE1BQU1RLFNBQVMsQ0FBQ1AsTUFBTUM7UUFBUztZQUFDQyxNQUFNO1lBQVFNLE9BQU87UUFBSTtLQUFFO0FBQ3JFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9icmVhay5qcz82YjhhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5UZXh0fSBUZXh0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkJyZWFrfSBCcmVha1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgYnJlYWtgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7QnJlYWt9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtBcnJheTxFbGVtZW50IHwgVGV4dD59XG4gKiAgIGhhc3QgZWxlbWVudCBjb250ZW50LlxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFyZEJyZWFrKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge3R5cGU6ICdlbGVtZW50JywgdGFnTmFtZTogJ2JyJywgcHJvcGVydGllczoge30sIGNoaWxkcmVuOiBbXX1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gW3N0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpLCB7dHlwZTogJ3RleHQnLCB2YWx1ZTogJ1xcbid9XVxufVxuIl0sIm5hbWVzIjpbImhhcmRCcmVhayIsInN0YXRlIiwibm9kZSIsInJlc3VsdCIsInR5cGUiLCJ0YWdOYW1lIiwicHJvcGVydGllcyIsImNoaWxkcmVuIiwicGF0Y2giLCJhcHBseURhdGEiLCJ2YWx1ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/code.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function code(state, node) {\n    const value = node.value ? node.value + \"\\n\" : \"\";\n    /** @type {Properties} */ const properties = {};\n    if (node.lang) {\n        properties.className = [\n            \"language-\" + node.lang\n        ];\n    }\n    // Create `<code>`.\n    /** @type {Element} */ let result = {\n        type: \"element\",\n        tagName: \"code\",\n        properties,\n        children: [\n            {\n                type: \"text\",\n                value\n            }\n        ]\n    };\n    if (node.meta) {\n        result.data = {\n            meta: node.meta\n        };\n    }\n    state.patch(node, result);\n    result = state.applyData(node, result);\n    // Create `<pre>`.\n    result = {\n        type: \"element\",\n        tagName: \"pre\",\n        properties: {},\n        children: [\n            result\n        ]\n    };\n    state.patch(node, result);\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9jb2RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNBLEtBQUtDLEtBQUssRUFBRUMsSUFBSTtJQUM5QixNQUFNQyxRQUFRRCxLQUFLQyxLQUFLLEdBQUdELEtBQUtDLEtBQUssR0FBRyxPQUFPO0lBQy9DLHVCQUF1QixHQUN2QixNQUFNQyxhQUFhLENBQUM7SUFFcEIsSUFBSUYsS0FBS0csSUFBSSxFQUFFO1FBQ2JELFdBQVdFLFNBQVMsR0FBRztZQUFDLGNBQWNKLEtBQUtHLElBQUk7U0FBQztJQUNsRDtJQUVBLG1CQUFtQjtJQUNuQixvQkFBb0IsR0FDcEIsSUFBSUUsU0FBUztRQUNYQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEw7UUFDQU0sVUFBVTtZQUFDO2dCQUFDRixNQUFNO2dCQUFRTDtZQUFLO1NBQUU7SUFDbkM7SUFFQSxJQUFJRCxLQUFLUyxJQUFJLEVBQUU7UUFDYkosT0FBT0ssSUFBSSxHQUFHO1lBQUNELE1BQU1ULEtBQUtTLElBQUk7UUFBQTtJQUNoQztJQUVBVixNQUFNWSxLQUFLLENBQUNYLE1BQU1LO0lBQ2xCQSxTQUFTTixNQUFNYSxTQUFTLENBQUNaLE1BQU1LO0lBRS9CLGtCQUFrQjtJQUNsQkEsU0FBUztRQUFDQyxNQUFNO1FBQVdDLFNBQVM7UUFBT0wsWUFBWSxDQUFDO1FBQUdNLFVBQVU7WUFBQ0g7U0FBTztJQUFBO0lBQzdFTixNQUFNWSxLQUFLLENBQUNYLE1BQU1LO0lBQ2xCLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2NvZGUuanM/MTJmZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuUHJvcGVydGllc30gUHJvcGVydGllc1xuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5Db2RlfSBDb2RlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBjb2RlYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0NvZGV9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjb2RlKHN0YXRlLCBub2RlKSB7XG4gIGNvbnN0IHZhbHVlID0gbm9kZS52YWx1ZSA/IG5vZGUudmFsdWUgKyAnXFxuJyA6ICcnXG4gIC8qKiBAdHlwZSB7UHJvcGVydGllc30gKi9cbiAgY29uc3QgcHJvcGVydGllcyA9IHt9XG5cbiAgaWYgKG5vZGUubGFuZykge1xuICAgIHByb3BlcnRpZXMuY2xhc3NOYW1lID0gWydsYW5ndWFnZS0nICsgbm9kZS5sYW5nXVxuICB9XG5cbiAgLy8gQ3JlYXRlIGA8Y29kZT5gLlxuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGxldCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdjb2RlJyxcbiAgICBwcm9wZXJ0aWVzLFxuICAgIGNoaWxkcmVuOiBbe3R5cGU6ICd0ZXh0JywgdmFsdWV9XVxuICB9XG5cbiAgaWYgKG5vZGUubWV0YSkge1xuICAgIHJlc3VsdC5kYXRhID0ge21ldGE6IG5vZGUubWV0YX1cbiAgfVxuXG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmVzdWx0ID0gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcblxuICAvLyBDcmVhdGUgYDxwcmU+YC5cbiAgcmVzdWx0ID0ge3R5cGU6ICdlbGVtZW50JywgdGFnTmFtZTogJ3ByZScsIHByb3BlcnRpZXM6IHt9LCBjaGlsZHJlbjogW3Jlc3VsdF19XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbImNvZGUiLCJzdGF0ZSIsIm5vZGUiLCJ2YWx1ZSIsInByb3BlcnRpZXMiLCJsYW5nIiwiY2xhc3NOYW1lIiwicmVzdWx0IiwidHlwZSIsInRhZ05hbWUiLCJjaGlsZHJlbiIsIm1ldGEiLCJkYXRhIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/delete.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strikethrough: () => (/* binding */ strikethrough)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Delete} Delete\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `delete` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Delete} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function strikethrough(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"del\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9kZWxldGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7O0NBSUMsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxjQUFjQyxLQUFLLEVBQUVDLElBQUk7SUFDdkMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvZGVsZXRlLmpzP2NlNmIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5EZWxldGV9IERlbGV0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgZGVsZXRlYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0RlbGV0ZX0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHN0cmlrZXRocm91Z2goc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdkZWwnLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbInN0cmlrZXRocm91Z2giLCJzdGF0ZSIsIm5vZGUiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsInByb3BlcnRpZXMiLCJjaGlsZHJlbiIsImFsbCIsInBhdGNoIiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `emphasis` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Emphasis} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function emphasis(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"em\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Q0FJQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNBLFNBQVNDLEtBQUssRUFBRUMsSUFBSTtJQUNsQyxvQkFBb0IsR0FDcEIsTUFBTUMsU0FBUztRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsWUFBWSxDQUFDO1FBQ2JDLFVBQVVOLE1BQU1PLEdBQUcsQ0FBQ047SUFDdEI7SUFDQUQsTUFBTVEsS0FBSyxDQUFDUCxNQUFNQztJQUNsQixPQUFPRixNQUFNUyxTQUFTLENBQUNSLE1BQU1DO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9lbXBoYXNpcy5qcz83Mjk0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuRW1waGFzaXN9IEVtcGhhc2lzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBlbXBoYXNpc2Agbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtFbXBoYXNpc30gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGVtcGhhc2lzKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnZW0nLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbImVtcGhhc2lzIiwic3RhdGUiLCJub2RlIiwicmVzdWx0IiwidHlwZSIsInRhZ05hbWUiLCJwcm9wZXJ0aWVzIiwiY2hpbGRyZW4iLCJhbGwiLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   footnoteReference: () => (/* binding */ footnoteReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function footnoteReference(state, node) {\n    const clobberPrefix = typeof state.options.clobberPrefix === \"string\" ? state.options.clobberPrefix : \"user-content-\";\n    const id = String(node.identifier).toUpperCase();\n    const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(id.toLowerCase());\n    const index = state.footnoteOrder.indexOf(id);\n    /** @type {number} */ let counter;\n    let reuseCounter = state.footnoteCounts.get(id);\n    if (reuseCounter === undefined) {\n        reuseCounter = 0;\n        state.footnoteOrder.push(id);\n        counter = state.footnoteOrder.length;\n    } else {\n        counter = index + 1;\n    }\n    reuseCounter += 1;\n    state.footnoteCounts.set(id, reuseCounter);\n    /** @type {Element} */ const link = {\n        type: \"element\",\n        tagName: \"a\",\n        properties: {\n            href: \"#\" + clobberPrefix + \"fn-\" + safeId,\n            id: clobberPrefix + \"fnref-\" + safeId + (reuseCounter > 1 ? \"-\" + reuseCounter : \"\"),\n            dataFootnoteRef: true,\n            ariaDescribedBy: [\n                \"footnote-label\"\n            ]\n        },\n        children: [\n            {\n                type: \"text\",\n                value: String(counter)\n            }\n        ]\n    };\n    state.patch(node, link);\n    /** @type {Element} */ const sup = {\n        type: \"element\",\n        tagName: \"sup\",\n        properties: {},\n        children: [\n            link\n        ]\n    };\n    state.patch(node, sup);\n    return state.applyData(node, sup);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/heading.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function heading(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"h\" + node.depth,\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9oZWFkaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7OztDQUlDLEdBRUQsbURBQW1EO0FBQ25EO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0EsUUFBUUMsS0FBSyxFQUFFQyxJQUFJO0lBQ2pDLG9CQUFvQixHQUNwQixNQUFNQyxTQUFTO1FBQ2JDLE1BQU07UUFDTkMsU0FBUyxNQUFNSCxLQUFLSSxLQUFLO1FBQ3pCQyxZQUFZLENBQUM7UUFDYkMsVUFBVVAsTUFBTVEsR0FBRyxDQUFDUDtJQUN0QjtJQUNBRCxNQUFNUyxLQUFLLENBQUNSLE1BQU1DO0lBQ2xCLE9BQU9GLE1BQU1VLFNBQVMsQ0FBQ1QsTUFBTUM7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2hlYWRpbmcuanM/YTcwYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkhlYWRpbmd9IEhlYWRpbmdcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGhlYWRpbmdgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7SGVhZGluZ30gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhlYWRpbmcoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdoJyArIG5vZGUuZGVwdGgsXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IHN0YXRlLmFsbChub2RlKVxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOlsiaGVhZGluZyIsInN0YXRlIiwibm9kZSIsInJlc3VsdCIsInR5cGUiLCJ0YWdOYW1lIiwiZGVwdGgiLCJwcm9wZXJ0aWVzIiwiY2hpbGRyZW4iLCJhbGwiLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/html.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Html} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Element | Raw | undefined}\n *   hast node.\n */ function html(state, node) {\n    if (state.options.allowDangerousHtml) {\n        /** @type {Raw} */ const result = {\n            type: \"raw\",\n            value: node.value\n        };\n        state.patch(node, result);\n        return state.applyData(node, result);\n    }\n    return undefined;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9odG1sLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7O0NBVUMsR0FDTSxTQUFTQSxLQUFLQyxLQUFLLEVBQUVDLElBQUk7SUFDOUIsSUFBSUQsTUFBTUUsT0FBTyxDQUFDQyxrQkFBa0IsRUFBRTtRQUNwQyxnQkFBZ0IsR0FDaEIsTUFBTUMsU0FBUztZQUFDQyxNQUFNO1lBQU9DLE9BQU9MLEtBQUtLLEtBQUs7UUFBQTtRQUM5Q04sTUFBTU8sS0FBSyxDQUFDTixNQUFNRztRQUNsQixPQUFPSixNQUFNUSxTQUFTLENBQUNQLE1BQU1HO0lBQy9CO0lBRUEsT0FBT0s7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvaHRtbC5qcz9hMzc0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuSHRtbH0gSHRtbFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uLy4uL2luZGV4LmpzJykuUmF3fSBSYXdcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgaHRtbGAgbm9kZSBpbnRvIGhhc3QgKGByYXdgIG5vZGUgaW4gZGFuZ2Vyb3VzIG1vZGUsIG90aGVyd2lzZVxuICogbm90aGluZykuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtIdG1sfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudCB8IFJhdyB8IHVuZGVmaW5lZH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaHRtbChzdGF0ZSwgbm9kZSkge1xuICBpZiAoc3RhdGUub3B0aW9ucy5hbGxvd0Rhbmdlcm91c0h0bWwpIHtcbiAgICAvKiogQHR5cGUge1Jhd30gKi9cbiAgICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ3JhdycsIHZhbHVlOiBub2RlLnZhbHVlfVxuICAgIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbiAgfVxuXG4gIHJldHVybiB1bmRlZmluZWRcbn1cbiJdLCJuYW1lcyI6WyJodG1sIiwic3RhdGUiLCJub2RlIiwib3B0aW9ucyIsImFsbG93RGFuZ2Vyb3VzSHRtbCIsInJlc3VsdCIsInR5cGUiLCJ2YWx1ZSIsInBhdGNoIiwiYXBwbHlEYXRhIiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js":
/*!*************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/* harmony import */ var _revert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../revert.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */ \n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */ function imageReference(state, node) {\n    const id = String(node.identifier).toUpperCase();\n    const definition = state.definitionById.get(id);\n    if (!definition) {\n        return (0,_revert_js__WEBPACK_IMPORTED_MODULE_0__.revert)(state, node);\n    }\n    /** @type {Properties} */ const properties = {\n        src: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__.normalizeUri)(definition.url || \"\"),\n        alt: node.alt\n    };\n    if (definition.title !== null && definition.title !== undefined) {\n        properties.title = definition.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"img\",\n        properties,\n        children: []\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9pbWFnZS1yZWZlcmVuY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7Ozs7OztDQU1DLEdBRXVEO0FBQ3JCO0FBRW5DOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNFLGVBQWVDLEtBQUssRUFBRUMsSUFBSTtJQUN4QyxNQUFNQyxLQUFLQyxPQUFPRixLQUFLRyxVQUFVLEVBQUVDLFdBQVc7SUFDOUMsTUFBTUMsYUFBYU4sTUFBTU8sY0FBYyxDQUFDQyxHQUFHLENBQUNOO0lBRTVDLElBQUksQ0FBQ0ksWUFBWTtRQUNmLE9BQU9SLGtEQUFNQSxDQUFDRSxPQUFPQztJQUN2QjtJQUVBLHVCQUF1QixHQUN2QixNQUFNUSxhQUFhO1FBQUNDLEtBQUtiLHlFQUFZQSxDQUFDUyxXQUFXSyxHQUFHLElBQUk7UUFBS0MsS0FBS1gsS0FBS1csR0FBRztJQUFBO0lBRTFFLElBQUlOLFdBQVdPLEtBQUssS0FBSyxRQUFRUCxXQUFXTyxLQUFLLEtBQUtDLFdBQVc7UUFDL0RMLFdBQVdJLEtBQUssR0FBR1AsV0FBV08sS0FBSztJQUNyQztJQUVBLG9CQUFvQixHQUNwQixNQUFNRSxTQUFTO1FBQUNDLE1BQU07UUFBV0MsU0FBUztRQUFPUjtRQUFZUyxVQUFVLEVBQUU7SUFBQTtJQUN6RWxCLE1BQU1tQixLQUFLLENBQUNsQixNQUFNYztJQUNsQixPQUFPZixNQUFNb0IsU0FBUyxDQUFDbkIsTUFBTWM7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2ltYWdlLXJlZmVyZW5jZS5qcz83Y2U1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50Q29udGVudH0gRWxlbWVudENvbnRlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5Qcm9wZXJ0aWVzfSBQcm9wZXJ0aWVzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkltYWdlUmVmZXJlbmNlfSBJbWFnZVJlZmVyZW5jZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG5pbXBvcnQge25vcm1hbGl6ZVVyaX0gZnJvbSAnbWljcm9tYXJrLXV0aWwtc2FuaXRpemUtdXJpJ1xuaW1wb3J0IHtyZXZlcnR9IGZyb20gJy4uL3JldmVydC5qcydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBpbWFnZVJlZmVyZW5jZWAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtJbWFnZVJlZmVyZW5jZX0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0FycmF5PEVsZW1lbnRDb250ZW50PiB8IEVsZW1lbnRDb250ZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpbWFnZVJlZmVyZW5jZShzdGF0ZSwgbm9kZSkge1xuICBjb25zdCBpZCA9IFN0cmluZyhub2RlLmlkZW50aWZpZXIpLnRvVXBwZXJDYXNlKClcbiAgY29uc3QgZGVmaW5pdGlvbiA9IHN0YXRlLmRlZmluaXRpb25CeUlkLmdldChpZClcblxuICBpZiAoIWRlZmluaXRpb24pIHtcbiAgICByZXR1cm4gcmV2ZXJ0KHN0YXRlLCBub2RlKVxuICB9XG5cbiAgLyoqIEB0eXBlIHtQcm9wZXJ0aWVzfSAqL1xuICBjb25zdCBwcm9wZXJ0aWVzID0ge3NyYzogbm9ybWFsaXplVXJpKGRlZmluaXRpb24udXJsIHx8ICcnKSwgYWx0OiBub2RlLmFsdH1cblxuICBpZiAoZGVmaW5pdGlvbi50aXRsZSAhPT0gbnVsbCAmJiBkZWZpbml0aW9uLnRpdGxlICE9PSB1bmRlZmluZWQpIHtcbiAgICBwcm9wZXJ0aWVzLnRpdGxlID0gZGVmaW5pdGlvbi50aXRsZVxuICB9XG5cbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ2VsZW1lbnQnLCB0YWdOYW1lOiAnaW1nJywgcHJvcGVydGllcywgY2hpbGRyZW46IFtdfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbIm5vcm1hbGl6ZVVyaSIsInJldmVydCIsImltYWdlUmVmZXJlbmNlIiwic3RhdGUiLCJub2RlIiwiaWQiLCJTdHJpbmciLCJpZGVudGlmaWVyIiwidG9VcHBlckNhc2UiLCJkZWZpbml0aW9uIiwiZGVmaW5pdGlvbkJ5SWQiLCJnZXQiLCJwcm9wZXJ0aWVzIiwic3JjIiwidXJsIiwiYWx0IiwidGl0bGUiLCJ1bmRlZmluZWQiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsImNoaWxkcmVuIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/image.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function image(state, node) {\n    /** @type {Properties} */ const properties = {\n        src: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(node.url)\n    };\n    if (node.alt !== null && node.alt !== undefined) {\n        properties.alt = node.alt;\n    }\n    if (node.title !== null && node.title !== undefined) {\n        properties.title = node.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"img\",\n        properties,\n        children: []\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handlers: () => (/* binding */ handlers)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js\");\n/* harmony import */ var _delete_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./delete.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\");\n/* harmony import */ var _footnote_reference_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./footnote-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./html.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./image.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./link.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./list.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./paragraph.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js\");\n/* harmony import */ var _table_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./table.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js\");\n/* harmony import */ var _table_row_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./table-row.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js\");\n/* harmony import */ var _table_cell_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./table-cell.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default handlers for nodes.\n *\n * @satisfies {import('../state.js').Handlers}\n */ const handlers = {\n    blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n    break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n    code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n    delete: _delete_js__WEBPACK_IMPORTED_MODULE_3__.strikethrough,\n    emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n    footnoteReference: _footnote_reference_js__WEBPACK_IMPORTED_MODULE_5__.footnoteReference,\n    heading: _heading_js__WEBPACK_IMPORTED_MODULE_6__.heading,\n    html: _html_js__WEBPACK_IMPORTED_MODULE_7__.html,\n    imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n    image: _image_js__WEBPACK_IMPORTED_MODULE_9__.image,\n    inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_10__.inlineCode,\n    linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n    link: _link_js__WEBPACK_IMPORTED_MODULE_12__.link,\n    listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n    list: _list_js__WEBPACK_IMPORTED_MODULE_14__.list,\n    paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_15__.paragraph,\n    // @ts-expect-error: root is different, but hard to type.\n    root: _root_js__WEBPACK_IMPORTED_MODULE_16__.root,\n    strong: _strong_js__WEBPACK_IMPORTED_MODULE_17__.strong,\n    table: _table_js__WEBPACK_IMPORTED_MODULE_18__.table,\n    tableCell: _table_cell_js__WEBPACK_IMPORTED_MODULE_19__.tableCell,\n    tableRow: _table_row_js__WEBPACK_IMPORTED_MODULE_20__.tableRow,\n    text: _text_js__WEBPACK_IMPORTED_MODULE_21__.text,\n    thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_22__.thematicBreak,\n    toml: ignore,\n    yaml: ignore,\n    definition: ignore,\n    footnoteDefinition: ignore\n};\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n    return undefined;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMEM7QUFDTjtBQUNOO0FBQ1c7QUFDSDtBQUNtQjtBQUNyQjtBQUNOO0FBQ3FCO0FBQ25CO0FBQ1c7QUFDTTtBQUNuQjtBQUNTO0FBQ1Q7QUFDVTtBQUNWO0FBQ0k7QUFDRjtBQUNPO0FBQ0U7QUFDWDtBQUNtQjtBQUVqRDs7OztDQUlDLEdBQ00sTUFBTXVCLFdBQVc7SUFDdEJ2QixVQUFVQSx3REFBQUE7SUFDVndCLE9BQU92QixnREFBU0E7SUFDaEJDLElBQUlBLDRDQUFBQTtJQUNKdUIsUUFBUXRCLHFEQUFhQTtJQUNyQkMsUUFBUUEsb0RBQUFBO0lBQ1JDLGlCQUFpQkEsdUVBQUFBO0lBQ2pCQyxPQUFPQSxrREFBQUE7SUFDUEMsSUFBSUEsNENBQUFBO0lBQ0pDLGNBQWNBLGlFQUFBQTtJQUNkQyxLQUFLQSw4Q0FBQUE7SUFDTEMsVUFBVUEsMERBQUFBO0lBQ1ZDLGFBQWFBLGdFQUFBQTtJQUNiQyxJQUFJQSw2Q0FBQUE7SUFDSkMsUUFBUUEsc0RBQUFBO0lBQ1JDLElBQUlBLDZDQUFBQTtJQUNKQyxTQUFTQSx1REFBQUE7SUFDVCx5REFBeUQ7SUFDekRDLElBQUlBLDZDQUFBQTtJQUNKQyxNQUFNQSxpREFBQUE7SUFDTkMsS0FBS0EsK0NBQUFBO0lBQ0xFLFNBQVNBLHdEQUFBQTtJQUNURCxRQUFRQSxzREFBQUE7SUFDUkUsSUFBSUEsNkNBQUFBO0lBQ0pDLGFBQWFBLGdFQUFBQTtJQUNiSSxNQUFNQztJQUNOQyxNQUFNRDtJQUNORSxZQUFZRjtJQUNaRyxvQkFBb0JIO0FBQ3RCLEVBQUM7QUFFRCw2Q0FBNkM7QUFDN0MsU0FBU0E7SUFDUCxPQUFPSTtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9pbmRleC5qcz9iYjJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7YmxvY2txdW90ZX0gZnJvbSAnLi9ibG9ja3F1b3RlLmpzJ1xuaW1wb3J0IHtoYXJkQnJlYWt9IGZyb20gJy4vYnJlYWsuanMnXG5pbXBvcnQge2NvZGV9IGZyb20gJy4vY29kZS5qcydcbmltcG9ydCB7c3RyaWtldGhyb3VnaH0gZnJvbSAnLi9kZWxldGUuanMnXG5pbXBvcnQge2VtcGhhc2lzfSBmcm9tICcuL2VtcGhhc2lzLmpzJ1xuaW1wb3J0IHtmb290bm90ZVJlZmVyZW5jZX0gZnJvbSAnLi9mb290bm90ZS1yZWZlcmVuY2UuanMnXG5pbXBvcnQge2hlYWRpbmd9IGZyb20gJy4vaGVhZGluZy5qcydcbmltcG9ydCB7aHRtbH0gZnJvbSAnLi9odG1sLmpzJ1xuaW1wb3J0IHtpbWFnZVJlZmVyZW5jZX0gZnJvbSAnLi9pbWFnZS1yZWZlcmVuY2UuanMnXG5pbXBvcnQge2ltYWdlfSBmcm9tICcuL2ltYWdlLmpzJ1xuaW1wb3J0IHtpbmxpbmVDb2RlfSBmcm9tICcuL2lubGluZS1jb2RlLmpzJ1xuaW1wb3J0IHtsaW5rUmVmZXJlbmNlfSBmcm9tICcuL2xpbmstcmVmZXJlbmNlLmpzJ1xuaW1wb3J0IHtsaW5rfSBmcm9tICcuL2xpbmsuanMnXG5pbXBvcnQge2xpc3RJdGVtfSBmcm9tICcuL2xpc3QtaXRlbS5qcydcbmltcG9ydCB7bGlzdH0gZnJvbSAnLi9saXN0LmpzJ1xuaW1wb3J0IHtwYXJhZ3JhcGh9IGZyb20gJy4vcGFyYWdyYXBoLmpzJ1xuaW1wb3J0IHtyb290fSBmcm9tICcuL3Jvb3QuanMnXG5pbXBvcnQge3N0cm9uZ30gZnJvbSAnLi9zdHJvbmcuanMnXG5pbXBvcnQge3RhYmxlfSBmcm9tICcuL3RhYmxlLmpzJ1xuaW1wb3J0IHt0YWJsZVJvd30gZnJvbSAnLi90YWJsZS1yb3cuanMnXG5pbXBvcnQge3RhYmxlQ2VsbH0gZnJvbSAnLi90YWJsZS1jZWxsLmpzJ1xuaW1wb3J0IHt0ZXh0fSBmcm9tICcuL3RleHQuanMnXG5pbXBvcnQge3RoZW1hdGljQnJlYWt9IGZyb20gJy4vdGhlbWF0aWMtYnJlYWsuanMnXG5cbi8qKlxuICogRGVmYXVsdCBoYW5kbGVycyBmb3Igbm9kZXMuXG4gKlxuICogQHNhdGlzZmllcyB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLkhhbmRsZXJzfVxuICovXG5leHBvcnQgY29uc3QgaGFuZGxlcnMgPSB7XG4gIGJsb2NrcXVvdGUsXG4gIGJyZWFrOiBoYXJkQnJlYWssXG4gIGNvZGUsXG4gIGRlbGV0ZTogc3RyaWtldGhyb3VnaCxcbiAgZW1waGFzaXMsXG4gIGZvb3Rub3RlUmVmZXJlbmNlLFxuICBoZWFkaW5nLFxuICBodG1sLFxuICBpbWFnZVJlZmVyZW5jZSxcbiAgaW1hZ2UsXG4gIGlubGluZUNvZGUsXG4gIGxpbmtSZWZlcmVuY2UsXG4gIGxpbmssXG4gIGxpc3RJdGVtLFxuICBsaXN0LFxuICBwYXJhZ3JhcGgsXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IHJvb3QgaXMgZGlmZmVyZW50LCBidXQgaGFyZCB0byB0eXBlLlxuICByb290LFxuICBzdHJvbmcsXG4gIHRhYmxlLFxuICB0YWJsZUNlbGwsXG4gIHRhYmxlUm93LFxuICB0ZXh0LFxuICB0aGVtYXRpY0JyZWFrLFxuICB0b21sOiBpZ25vcmUsXG4gIHlhbWw6IGlnbm9yZSxcbiAgZGVmaW5pdGlvbjogaWdub3JlLFxuICBmb290bm90ZURlZmluaXRpb246IGlnbm9yZVxufVxuXG4vLyBSZXR1cm4gbm90aGluZyBmb3Igbm9kZXMgdGhhdCBhcmUgaWdub3JlZC5cbmZ1bmN0aW9uIGlnbm9yZSgpIHtcbiAgcmV0dXJuIHVuZGVmaW5lZFxufVxuIl0sIm5hbWVzIjpbImJsb2NrcXVvdGUiLCJoYXJkQnJlYWsiLCJjb2RlIiwic3RyaWtldGhyb3VnaCIsImVtcGhhc2lzIiwiZm9vdG5vdGVSZWZlcmVuY2UiLCJoZWFkaW5nIiwiaHRtbCIsImltYWdlUmVmZXJlbmNlIiwiaW1hZ2UiLCJpbmxpbmVDb2RlIiwibGlua1JlZmVyZW5jZSIsImxpbmsiLCJsaXN0SXRlbSIsImxpc3QiLCJwYXJhZ3JhcGgiLCJyb290Iiwic3Ryb25nIiwidGFibGUiLCJ0YWJsZVJvdyIsInRhYmxlQ2VsbCIsInRleHQiLCJ0aGVtYXRpY0JyZWFrIiwiaGFuZGxlcnMiLCJicmVhayIsImRlbGV0ZSIsInRvbWwiLCJpZ25vcmUiLCJ5YW1sIiwiZGVmaW5pdGlvbiIsImZvb3Rub3RlRGVmaW5pdGlvbiIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function inlineCode(state, node) {\n    /** @type {Text} */ const text = {\n        type: \"text\",\n        value: node.value.replace(/\\r?\\n|\\r/g, \" \")\n    };\n    state.patch(node, text);\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"code\",\n        properties: {},\n        children: [\n            text\n        ]\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/* harmony import */ var _revert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../revert.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */ \n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */ function linkReference(state, node) {\n    const id = String(node.identifier).toUpperCase();\n    const definition = state.definitionById.get(id);\n    if (!definition) {\n        return (0,_revert_js__WEBPACK_IMPORTED_MODULE_0__.revert)(state, node);\n    }\n    /** @type {Properties} */ const properties = {\n        href: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__.normalizeUri)(definition.url || \"\")\n    };\n    if (definition.title !== null && definition.title !== undefined) {\n        properties.title = definition.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"a\",\n        properties,\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/link.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function link(state, node) {\n    /** @type {Properties} */ const properties = {\n        href: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(node.url)\n    };\n    if (node.title !== null && node.title !== undefined) {\n        properties.title = node.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"a\",\n        properties,\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/list-item.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */ function listItem(state, node, parent) {\n    const results = state.all(node);\n    const loose = parent ? listLoose(parent) : listItemLoose(node);\n    /** @type {Properties} */ const properties = {};\n    /** @type {Array<ElementContent>} */ const children = [];\n    if (typeof node.checked === \"boolean\") {\n        const head = results[0];\n        /** @type {Element} */ let paragraph;\n        if (head && head.type === \"element\" && head.tagName === \"p\") {\n            paragraph = head;\n        } else {\n            paragraph = {\n                type: \"element\",\n                tagName: \"p\",\n                properties: {},\n                children: []\n            };\n            results.unshift(paragraph);\n        }\n        if (paragraph.children.length > 0) {\n            paragraph.children.unshift({\n                type: \"text\",\n                value: \" \"\n            });\n        }\n        paragraph.children.unshift({\n            type: \"element\",\n            tagName: \"input\",\n            properties: {\n                type: \"checkbox\",\n                checked: node.checked,\n                disabled: true\n            },\n            children: []\n        });\n        // According to github-markdown-css, this class hides bullet.\n        // See: <https://github.com/sindresorhus/github-markdown-css>.\n        properties.className = [\n            \"task-list-item\"\n        ];\n    }\n    let index = -1;\n    while(++index < results.length){\n        const child = results[index];\n        // Add eols before nodes, except if this is a loose, first paragraph.\n        if (loose || index !== 0 || child.type !== \"element\" || child.tagName !== \"p\") {\n            children.push({\n                type: \"text\",\n                value: \"\\n\"\n            });\n        }\n        if (child.type === \"element\" && child.tagName === \"p\" && !loose) {\n            children.push(...child.children);\n        } else {\n            children.push(child);\n        }\n    }\n    const tail = results[results.length - 1];\n    // Add a final eol.\n    if (tail && (loose || tail.type !== \"element\" || tail.tagName !== \"p\")) {\n        children.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"li\",\n        properties,\n        children\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n/**\n * @param {Parents} node\n * @return {Boolean}\n */ function listLoose(node) {\n    let loose = false;\n    if (node.type === \"list\") {\n        loose = node.spread || false;\n        const children = node.children;\n        let index = -1;\n        while(!loose && ++index < children.length){\n            loose = listItemLoose(children[index]);\n        }\n    }\n    return loose;\n}\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */ function listItemLoose(node) {\n    const spread = node.spread;\n    return spread === null || spread === undefined ? node.children.length > 1 : spread;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/list.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function list(state, node) {\n    /** @type {Properties} */ const properties = {};\n    const results = state.all(node);\n    let index = -1;\n    if (typeof node.start === \"number\" && node.start !== 1) {\n        properties.start = node.start;\n    }\n    // Like GitHub, add a class for custom styling.\n    while(++index < results.length){\n        const child = results[index];\n        if (child.type === \"element\" && child.tagName === \"li\" && child.properties && Array.isArray(child.properties.className) && child.properties.className.includes(\"task-list-item\")) {\n            properties.className = [\n                \"contains-task-list\"\n            ];\n            break;\n        }\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: node.ordered ? \"ol\" : \"ul\",\n        properties,\n        children: state.wrap(results, true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function paragraph(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"p\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9wYXJhZ3JhcGguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7O0NBSUMsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxVQUFVQyxLQUFLLEVBQUVDLElBQUk7SUFDbkMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvcGFyYWdyYXBoLmpzP2RlMTkiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5QYXJhZ3JhcGh9IFBhcmFncmFwaFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgcGFyYWdyYXBoYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge1BhcmFncmFwaH0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhcmFncmFwaChzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ3AnLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbInBhcmFncmFwaCIsInN0YXRlIiwibm9kZSIsInJlc3VsdCIsInR5cGUiLCJ0YWdOYW1lIiwicHJvcGVydGllcyIsImNoaWxkcmVuIiwiYWxsIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/root.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Parents} HastParents\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastParents}\n *   hast node.\n */ function root(state, node) {\n    /** @type {HastRoot} */ const result = {\n        type: \"root\",\n        children: state.wrap(state.all(node))\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNBLEtBQUtDLEtBQUssRUFBRUMsSUFBSTtJQUM5QixxQkFBcUIsR0FDckIsTUFBTUMsU0FBUztRQUFDQyxNQUFNO1FBQVFDLFVBQVVKLE1BQU1LLElBQUksQ0FBQ0wsTUFBTU0sR0FBRyxDQUFDTDtJQUFNO0lBQ25FRCxNQUFNTyxLQUFLLENBQUNOLE1BQU1DO0lBQ2xCLE9BQU9GLE1BQU1RLFNBQVMsQ0FBQ1AsTUFBTUM7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL3Jvb3QuanM/MDYyMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5QYXJlbnRzfSBIYXN0UGFyZW50c1xuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLlJvb3R9IEhhc3RSb290XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlJvb3R9IE1kYXN0Um9vdFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgcm9vdGAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtNZGFzdFJvb3R9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtIYXN0UGFyZW50c31cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcm9vdChzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0hhc3RSb290fSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ3Jvb3QnLCBjaGlsZHJlbjogc3RhdGUud3JhcChzdGF0ZS5hbGwobm9kZSkpfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbInJvb3QiLCJzdGF0ZSIsIm5vZGUiLCJyZXN1bHQiLCJ0eXBlIiwiY2hpbGRyZW4iLCJ3cmFwIiwiYWxsIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/strong.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `strong` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Strong} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function strong(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"strong\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7O0NBSUMsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxPQUFPQyxLQUFLLEVBQUVDLElBQUk7SUFDaEMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvc3Ryb25nLmpzPzE0ZjYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5TdHJvbmd9IFN0cm9uZ1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgc3Ryb25nYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge1N0cm9uZ30gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHN0cm9uZyhzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ3N0cm9uZycsXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IHN0YXRlLmFsbChub2RlKVxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOlsic3Ryb25nIiwic3RhdGUiLCJub2RlIiwicmVzdWx0IiwidHlwZSIsInRhZ05hbWUiLCJwcm9wZXJ0aWVzIiwiY2hpbGRyZW4iLCJhbGwiLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableCell: () => (/* binding */ tableCell)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function tableCell(state, node) {\n    // Note: this function is normally not called: see `table-row` for how rows\n    // and their cells are compiled.\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"td\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90YWJsZS1jZWxsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7OztDQUlDLEdBRUQsbURBQW1EO0FBQ25EO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0EsVUFBVUMsS0FBSyxFQUFFQyxJQUFJO0lBQ25DLDJFQUEyRTtJQUMzRSxnQ0FBZ0M7SUFDaEMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvdGFibGUtY2VsbC5qcz9kNzZjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuVGFibGVDZWxsfSBUYWJsZUNlbGxcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHRhYmxlQ2VsbGAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtUYWJsZUNlbGx9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0YWJsZUNlbGwoc3RhdGUsIG5vZGUpIHtcbiAgLy8gTm90ZTogdGhpcyBmdW5jdGlvbiBpcyBub3JtYWxseSBub3QgY2FsbGVkOiBzZWUgYHRhYmxlLXJvd2AgZm9yIGhvdyByb3dzXG4gIC8vIGFuZCB0aGVpciBjZWxscyBhcmUgY29tcGlsZWQuXG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAndGQnLCAvLyBBc3N1bWUgYm9keSBjZWxsLlxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbInRhYmxlQ2VsbCIsInN0YXRlIiwibm9kZSIsInJlc3VsdCIsInR5cGUiLCJ0YWdOYW1lIiwicHJvcGVydGllcyIsImNoaWxkcmVuIiwiYWxsIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table-row.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableRow: () => (/* binding */ tableRow)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */ function tableRow(state, node, parent) {\n    const siblings = parent ? parent.children : undefined;\n    // Generate a body row when without parent.\n    const rowIndex = siblings ? siblings.indexOf(node) : 1;\n    const tagName = rowIndex === 0 ? \"th\" : \"td\";\n    // To do: option to use `style`?\n    const align = parent && parent.type === \"table\" ? parent.align : undefined;\n    const length = align ? align.length : node.children.length;\n    let cellIndex = -1;\n    /** @type {Array<ElementContent>} */ const cells = [];\n    while(++cellIndex < length){\n        // Note: can also be undefined.\n        const cell = node.children[cellIndex];\n        /** @type {Properties} */ const properties = {};\n        const alignValue = align ? align[cellIndex] : undefined;\n        if (alignValue) {\n            properties.align = alignValue;\n        }\n        /** @type {Element} */ let result = {\n            type: \"element\",\n            tagName,\n            properties,\n            children: []\n        };\n        if (cell) {\n            result.children = state.all(cell);\n            state.patch(cell, result);\n            result = state.applyData(cell, result);\n        }\n        cells.push(result);\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"tr\",\n        properties: {},\n        children: state.wrap(cells, true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   table: () => (/* binding */ table)\n/* harmony export */ });\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function table(state, node) {\n    const rows = state.all(node);\n    const firstRow = rows.shift();\n    /** @type {Array<Element>} */ const tableContent = [];\n    if (firstRow) {\n        /** @type {Element} */ const head = {\n            type: \"element\",\n            tagName: \"thead\",\n            properties: {},\n            children: state.wrap([\n                firstRow\n            ], true)\n        };\n        state.patch(node.children[0], head);\n        tableContent.push(head);\n    }\n    if (rows.length > 0) {\n        /** @type {Element} */ const body = {\n            type: \"element\",\n            tagName: \"tbody\",\n            properties: {},\n            children: state.wrap(rows, true)\n        };\n        const start = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_0__.pointStart)(node.children[1]);\n        const end = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_0__.pointEnd)(node.children[node.children.length - 1]);\n        if (start && end) body.position = {\n            start,\n            end\n        };\n        tableContent.push(body);\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"table\",\n        properties: {},\n        children: state.wrap(tableContent, true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90YWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBOzs7O0NBSUMsR0FFdUQ7QUFFeEQ7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0UsTUFBTUMsS0FBSyxFQUFFQyxJQUFJO0lBQy9CLE1BQU1DLE9BQU9GLE1BQU1HLEdBQUcsQ0FBQ0Y7SUFDdkIsTUFBTUcsV0FBV0YsS0FBS0csS0FBSztJQUMzQiwyQkFBMkIsR0FDM0IsTUFBTUMsZUFBZSxFQUFFO0lBRXZCLElBQUlGLFVBQVU7UUFDWixvQkFBb0IsR0FDcEIsTUFBTUcsT0FBTztZQUNYQyxNQUFNO1lBQ05DLFNBQVM7WUFDVEMsWUFBWSxDQUFDO1lBQ2JDLFVBQVVYLE1BQU1ZLElBQUksQ0FBQztnQkFBQ1I7YUFBUyxFQUFFO1FBQ25DO1FBQ0FKLE1BQU1hLEtBQUssQ0FBQ1osS0FBS1UsUUFBUSxDQUFDLEVBQUUsRUFBRUo7UUFDOUJELGFBQWFRLElBQUksQ0FBQ1A7SUFDcEI7SUFFQSxJQUFJTCxLQUFLYSxNQUFNLEdBQUcsR0FBRztRQUNuQixvQkFBb0IsR0FDcEIsTUFBTUMsT0FBTztZQUNYUixNQUFNO1lBQ05DLFNBQVM7WUFDVEMsWUFBWSxDQUFDO1lBQ2JDLFVBQVVYLE1BQU1ZLElBQUksQ0FBQ1YsTUFBTTtRQUM3QjtRQUVBLE1BQU1lLFFBQVFuQiwrREFBVUEsQ0FBQ0csS0FBS1UsUUFBUSxDQUFDLEVBQUU7UUFDekMsTUFBTU8sTUFBTXJCLDZEQUFRQSxDQUFDSSxLQUFLVSxRQUFRLENBQUNWLEtBQUtVLFFBQVEsQ0FBQ0ksTUFBTSxHQUFHLEVBQUU7UUFDNUQsSUFBSUUsU0FBU0MsS0FBS0YsS0FBS0csUUFBUSxHQUFHO1lBQUNGO1lBQU9DO1FBQUc7UUFDN0NaLGFBQWFRLElBQUksQ0FBQ0U7SUFDcEI7SUFFQSxvQkFBb0IsR0FDcEIsTUFBTUksU0FBUztRQUNiWixNQUFNO1FBQ05DLFNBQVM7UUFDVEMsWUFBWSxDQUFDO1FBQ2JDLFVBQVVYLE1BQU1ZLElBQUksQ0FBQ04sY0FBYztJQUNyQztJQUNBTixNQUFNYSxLQUFLLENBQUNaLE1BQU1tQjtJQUNsQixPQUFPcEIsTUFBTXFCLFNBQVMsQ0FBQ3BCLE1BQU1tQjtBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvdGFibGUuanM/YzQ0YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlRhYmxlfSBUYWJsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG5pbXBvcnQge3BvaW50RW5kLCBwb2ludFN0YXJ0fSBmcm9tICd1bmlzdC11dGlsLXBvc2l0aW9uJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHRhYmxlYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge1RhYmxlfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gdGFibGUoc3RhdGUsIG5vZGUpIHtcbiAgY29uc3Qgcm93cyA9IHN0YXRlLmFsbChub2RlKVxuICBjb25zdCBmaXJzdFJvdyA9IHJvd3Muc2hpZnQoKVxuICAvKiogQHR5cGUge0FycmF5PEVsZW1lbnQ+fSAqL1xuICBjb25zdCB0YWJsZUNvbnRlbnQgPSBbXVxuXG4gIGlmIChmaXJzdFJvdykge1xuICAgIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgICBjb25zdCBoZWFkID0ge1xuICAgICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgICAgdGFnTmFtZTogJ3RoZWFkJyxcbiAgICAgIHByb3BlcnRpZXM6IHt9LFxuICAgICAgY2hpbGRyZW46IHN0YXRlLndyYXAoW2ZpcnN0Um93XSwgdHJ1ZSlcbiAgICB9XG4gICAgc3RhdGUucGF0Y2gobm9kZS5jaGlsZHJlblswXSwgaGVhZClcbiAgICB0YWJsZUNvbnRlbnQucHVzaChoZWFkKVxuICB9XG5cbiAgaWYgKHJvd3MubGVuZ3RoID4gMCkge1xuICAgIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgICBjb25zdCBib2R5ID0ge1xuICAgICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgICAgdGFnTmFtZTogJ3Rib2R5JyxcbiAgICAgIHByb3BlcnRpZXM6IHt9LFxuICAgICAgY2hpbGRyZW46IHN0YXRlLndyYXAocm93cywgdHJ1ZSlcbiAgICB9XG5cbiAgICBjb25zdCBzdGFydCA9IHBvaW50U3RhcnQobm9kZS5jaGlsZHJlblsxXSlcbiAgICBjb25zdCBlbmQgPSBwb2ludEVuZChub2RlLmNoaWxkcmVuW25vZGUuY2hpbGRyZW4ubGVuZ3RoIC0gMV0pXG4gICAgaWYgKHN0YXJ0ICYmIGVuZCkgYm9keS5wb3NpdGlvbiA9IHtzdGFydCwgZW5kfVxuICAgIHRhYmxlQ29udGVudC5wdXNoKGJvZHkpXG4gIH1cblxuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ3RhYmxlJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUud3JhcCh0YWJsZUNvbnRlbnQsIHRydWUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6WyJwb2ludEVuZCIsInBvaW50U3RhcnQiLCJ0YWJsZSIsInN0YXRlIiwibm9kZSIsInJvd3MiLCJhbGwiLCJmaXJzdFJvdyIsInNoaWZ0IiwidGFibGVDb250ZW50IiwiaGVhZCIsInR5cGUiLCJ0YWdOYW1lIiwicHJvcGVydGllcyIsImNoaWxkcmVuIiwid3JhcCIsInBhdGNoIiwicHVzaCIsImxlbmd0aCIsImJvZHkiLCJzdGFydCIsImVuZCIsInBvc2l0aW9uIiwicmVzdWx0IiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/text.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var trim_lines__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! trim-lines */ \"(ssr)/./node_modules/trim-lines/index.js\");\n/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastElement | HastText}\n *   hast node.\n */ function text(state, node) {\n    /** @type {HastText} */ const result = {\n        type: \"text\",\n        value: (0,trim_lines__WEBPACK_IMPORTED_MODULE_0__.trimLines)(String(node.value))\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7Ozs7O0NBS0MsR0FFbUM7QUFFcEM7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0MsS0FBS0MsS0FBSyxFQUFFQyxJQUFJO0lBQzlCLHFCQUFxQixHQUNyQixNQUFNQyxTQUFTO1FBQUNDLE1BQU07UUFBUUMsT0FBT04scURBQVNBLENBQUNPLE9BQU9KLEtBQUtHLEtBQUs7SUFBRTtJQUNsRUosTUFBTU0sS0FBSyxDQUFDTCxNQUFNQztJQUNsQixPQUFPRixNQUFNTyxTQUFTLENBQUNOLE1BQU1DO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLWJsb2ctbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzP2M1YjAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gSGFzdEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5UZXh0fSBIYXN0VGV4dFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5UZXh0fSBNZGFzdFRleHRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuaW1wb3J0IHt0cmltTGluZXN9IGZyb20gJ3RyaW0tbGluZXMnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgdGV4dGAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtNZGFzdFRleHR9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtIYXN0RWxlbWVudCB8IEhhc3RUZXh0fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0ZXh0KHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7SGFzdFRleHR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAndGV4dCcsIHZhbHVlOiB0cmltTGluZXMoU3RyaW5nKG5vZGUudmFsdWUpKX1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6WyJ0cmltTGluZXMiLCJ0ZXh0Iiwic3RhdGUiLCJub2RlIiwicmVzdWx0IiwidHlwZSIsInZhbHVlIiwiU3RyaW5nIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function thematicBreak(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"hr\",\n        properties: {},\n        children: []\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90aGVtYXRpYy1icmVhay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Q0FJQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNBLGNBQWNDLEtBQUssRUFBRUMsSUFBSTtJQUN2QyxvQkFBb0IsR0FDcEIsTUFBTUMsU0FBUztRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsWUFBWSxDQUFDO1FBQ2JDLFVBQVUsRUFBRTtJQUNkO0lBQ0FOLE1BQU1PLEtBQUssQ0FBQ04sTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVEsU0FBUyxDQUFDUCxNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvdGhlbWF0aWMtYnJlYWsuanM/ZjgzNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlRoZW1hdGljQnJlYWt9IFRoZW1hdGljQnJlYWtcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHRoZW1hdGljQnJlYWtgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7VGhlbWF0aWNCcmVha30gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRoZW1hdGljQnJlYWsoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdocicsXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IFtdXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6WyJ0aGVtYXRpY0JyZWFrIiwic3RhdGUiLCJub2RlIiwicmVzdWx0IiwidHlwZSIsInRhZ05hbWUiLCJwcm9wZXJ0aWVzIiwiY2hpbGRyZW4iLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toHast: () => (/* binding */ toHast)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var _footer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./footer.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js\");\n/* harmony import */ var _state_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/state.js\");\n/**\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('./state.js').Options} Options\n */ \n\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {HastNodes}\n *   hast tree.\n */ function toHast(tree, options) {\n    const state = (0,_state_js__WEBPACK_IMPORTED_MODULE_0__.createState)(tree, options);\n    const node = state.one(tree, undefined);\n    const foot = (0,_footer_js__WEBPACK_IMPORTED_MODULE_1__.footer)(state);\n    /** @type {HastNodes} */ const result = Array.isArray(node) ? {\n        type: \"root\",\n        children: node\n    } : node || {\n        type: \"root\",\n        children: []\n    };\n    if (foot) {\n        // If there’s a footer, there were definitions, meaning block\n        // content.\n        // So `result` is a parent node.\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(\"children\" in result);\n        result.children.push({\n            type: \"text\",\n            value: \"\\n\"\n        }, foot);\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js":
/*!*******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/revert.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   revert: () => (/* binding */ revert)\n/* harmony export */ });\n/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Nodes} Nodes\n * @typedef {import('mdast').Reference} Reference\n *\n * @typedef {import('./state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Extract<Nodes, Reference>} node\n *   Reference node (image, link).\n * @returns {Array<ElementContent>}\n *   hast content.\n */ function revert(state, node) {\n    const subtype = node.referenceType;\n    let suffix = \"]\";\n    if (subtype === \"collapsed\") {\n        suffix += \"[]\";\n    } else if (subtype === \"full\") {\n        suffix += \"[\" + (node.label || node.identifier) + \"]\";\n    }\n    if (node.type === \"imageReference\") {\n        return [\n            {\n                type: \"text\",\n                value: \"![\" + node.alt + suffix\n            }\n        ];\n    }\n    const contents = state.all(node);\n    const head = contents[0];\n    if (head && head.type === \"text\") {\n        head.value = \"[\" + head.value;\n    } else {\n        contents.unshift({\n            type: \"text\",\n            value: \"[\"\n        });\n    }\n    const tail = contents[contents.length - 1];\n    if (tail && tail.type === \"text\") {\n        tail.value += suffix;\n    } else {\n        contents.push({\n            type: \"text\",\n            value: suffix\n        });\n    }\n    return contents;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/state.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/state.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createState: () => (/* binding */ createState),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var _handlers_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handlers/index.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js\");\n/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').RootContent} HastRootContent\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('mdast').Parents} MdastParents\n *\n * @typedef {import('vfile').VFile} VFile\n *\n * @typedef {import('./footer.js').FootnoteBackContentTemplate} FootnoteBackContentTemplate\n * @typedef {import('./footer.js').FootnoteBackLabelTemplate} FootnoteBackLabelTemplate\n */ /**\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<HastElementContent> | HastElementContent | undefined}\n *   hast node.\n *\n * @typedef {Partial<Record<MdastNodes['type'], Handler>>} Handlers\n *   Handle nodes.\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree (default:\n *   `false`).\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` property on footnotes to prevent them from\n *   *clobbering* (default: `'user-content-'`).\n *\n *   Pass `''` for trusted markdown and when you are careful with\n *   polyfilling.\n *   You could pass a different prefix.\n *\n *   DOM clobbering is this:\n *\n *   ```html\n *   <p id=\"x\"></p>\n *   <script>alert(x) // `x` now refers to the `p#x` DOM element</script>\n *   ```\n *\n *   The above example shows that elements are made available by browsers, by\n *   their ID, on the `window` object.\n *   This is a security risk because you might be expecting some other variable\n *   at that place.\n *   It can also break polyfills.\n *   Using a prefix solves these problems.\n * @property {VFile | null | undefined} [file]\n *   Corresponding virtual file representing the input document (optional).\n * @property {FootnoteBackContentTemplate | string | null | undefined} [footnoteBackContent]\n *   Content of the backreference back to references (default: `defaultFootnoteBackContent`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackContent(_, rereferenceIndex) {\n *     const result = [{type: 'text', value: '↩'}]\n *\n *     if (rereferenceIndex > 1) {\n *       result.push({\n *         type: 'element',\n *         tagName: 'sup',\n *         properties: {},\n *         children: [{type: 'text', value: String(rereferenceIndex)}]\n *       })\n *     }\n *\n *     return result\n *   }\n *   ```\n *\n *   This content is used in the `a` element of each backreference (the `↩`\n *   links).\n * @property {FootnoteBackLabelTemplate | string | null | undefined} [footnoteBackLabel]\n *   Label to describe the backreference back to references (default:\n *   `defaultFootnoteBackLabel`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n *    return (\n *      'Back to reference ' +\n *      (referenceIndex + 1) +\n *      (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n *    )\n *   }\n *   ```\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is used in the `ariaLabel` property on each backreference\n *   (the `↩` links).\n *   It affects users of assistive technology.\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Textual label to use for the footnotes section (default: `'Footnotes'`).\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (default: `{className:\n *   ['sr-only']}`).\n *\n *   Change it to show the label and add other properties.\n *\n *   This label is typically hidden visually (assuming an `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass an empty string.\n *   You can also add different properties.\n *\n *   > **Note**: `id: 'footnote-label'` is always added, because footnote\n *   > calls use it with `aria-describedby` to provide an accessible label.\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   HTML tag name to use for the footnote label element (default: `'h2'`).\n *\n *   Change it to match your document structure.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes (optional).\n * @property {Array<MdastNodes['type']> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed)\n *   (optional).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes (optional).\n *\n * @typedef State\n *   Info passed around.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => HastElement | Type} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {Map<string, MdastDefinition>} definitionById\n *   Definitions by their identifier.\n * @property {Map<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Map<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {(node: MdastNodes, parent: MdastParents | undefined) => Array<HastElementContent> | HastElementContent | undefined} one\n *   Transform an mdast node to hast.\n * @property {Options} options\n *   Configuration.\n * @property {(from: MdastNodes, node: HastNodes) => undefined} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastRootContent>(nodes: Array<Type>, loose?: boolean | undefined) => Array<HastText | Type>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n */ \n\n\n\nconst own = {}.hasOwnProperty;\n/** @type {Options} */ const emptyOptions = {};\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {State}\n *   `state` function.\n */ function createState(tree, options) {\n    const settings = options || emptyOptions;\n    /** @type {Map<string, MdastDefinition>} */ const definitionById = new Map();\n    /** @type {Map<string, MdastFootnoteDefinition>} */ const footnoteById = new Map();\n    /** @type {Map<string, number>} */ const footnoteCounts = new Map();\n    /** @type {Handlers} */ // @ts-expect-error: the root handler returns a root.\n    // Hard to type.\n    const handlers = {\n        ..._handlers_index_js__WEBPACK_IMPORTED_MODULE_0__.handlers,\n        ...settings.handlers\n    };\n    /** @type {State} */ const state = {\n        all,\n        applyData,\n        definitionById,\n        footnoteById,\n        footnoteCounts,\n        footnoteOrder: [],\n        handlers,\n        one,\n        options: settings,\n        patch,\n        wrap\n    };\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(tree, function(node) {\n        if (node.type === \"definition\" || node.type === \"footnoteDefinition\") {\n            const map = node.type === \"definition\" ? definitionById : footnoteById;\n            const id = String(node.identifier).toUpperCase();\n            // Mimick CM behavior of link definitions.\n            // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/9032189/lib/index.js#L20-L21>.\n            if (!map.has(id)) {\n                // @ts-expect-error: node type matches map.\n                map.set(id, node);\n            }\n        }\n    });\n    return state;\n    /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {Array<HastElementContent> | HastElementContent | undefined}\n   *   Resulting hast node.\n   */ function one(node, parent) {\n        const type = node.type;\n        const handle = state.handlers[type];\n        if (own.call(state.handlers, type) && handle) {\n            return handle(state, node, parent);\n        }\n        if (state.options.passThrough && state.options.passThrough.includes(type)) {\n            if (\"children\" in node) {\n                const { children, ...shallow } = node;\n                const result = (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(shallow);\n                // @ts-expect-error: TS doesn’t understand…\n                result.children = state.all(node);\n                // @ts-expect-error: TS doesn’t understand…\n                return result;\n            }\n            // @ts-expect-error: it’s custom.\n            return (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node);\n        }\n        const unknown = state.options.unknownHandler || defaultUnknownHandler;\n        return unknown(state, node, parent);\n    }\n    /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */ function all(parent) {\n        /** @type {Array<HastElementContent>} */ const values = [];\n        if (\"children\" in parent) {\n            const nodes = parent.children;\n            let index = -1;\n            while(++index < nodes.length){\n                const result = state.one(nodes[index], parent);\n                // To do: see if we van clean this? Can we merge texts?\n                if (result) {\n                    if (index && nodes[index - 1].type === \"break\") {\n                        if (!Array.isArray(result) && result.type === \"text\") {\n                            result.value = trimMarkdownSpaceStart(result.value);\n                        }\n                        if (!Array.isArray(result) && result.type === \"element\") {\n                            const head = result.children[0];\n                            if (head && head.type === \"text\") {\n                                head.value = trimMarkdownSpaceStart(head.value);\n                            }\n                        }\n                    }\n                    if (Array.isArray(result)) {\n                        values.push(...result);\n                    } else {\n                        values.push(result);\n                    }\n                }\n            }\n        }\n        return values;\n    }\n}\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */ function patch(from, to) {\n    if (from.position) to.position = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_3__.position)(from);\n}\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {HastElement | Type}\n *   Nothing.\n */ function applyData(from, to) {\n    /** @type {HastElement | Type} */ let result = to;\n    // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n    if (from && from.data) {\n        const hName = from.data.hName;\n        const hChildren = from.data.hChildren;\n        const hProperties = from.data.hProperties;\n        if (typeof hName === \"string\") {\n            // Transforming the node resulted in an element with a different name\n            // than wanted:\n            if (result.type === \"element\") {\n                result.tagName = hName;\n            } else {\n                /** @type {Array<HastElementContent>} */ // @ts-expect-error: assume no doctypes in `root`.\n                const children = \"children\" in result ? result.children : [\n                    result\n                ];\n                result = {\n                    type: \"element\",\n                    tagName: hName,\n                    properties: {},\n                    children\n                };\n            }\n        }\n        if (result.type === \"element\" && hProperties) {\n            Object.assign(result.properties, (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(hProperties));\n        }\n        if (\"children\" in result && result.children && hChildren !== null && hChildren !== undefined) {\n            result.children = hChildren;\n        }\n    }\n    return result;\n}\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastElement | HastText}\n *   Resulting hast node.\n */ function defaultUnknownHandler(state, node) {\n    const data = node.data || {};\n    /** @type {HastElement | HastText} */ const result = \"value\" in node && !(own.call(data, \"hProperties\") || own.call(data, \"hChildren\")) ? {\n        type: \"text\",\n        value: node.value\n    } : {\n        type: \"element\",\n        tagName: \"div\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastRootContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | undefined} [loose=false]\n *   Whether to add line endings at start and end (default: `false`).\n * @returns {Array<HastText | Type>}\n *   Wrapped nodes.\n */ function wrap(nodes, loose) {\n    /** @type {Array<HastText | Type>} */ const result = [];\n    let index = -1;\n    if (loose) {\n        result.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n    }\n    while(++index < nodes.length){\n        if (index) result.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n        result.push(nodes[index]);\n    }\n    if (loose && nodes.length > 0) {\n        result.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n    }\n    return result;\n}\n/**\n * Trim spaces and tabs at the start of `value`.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Result.\n */ function trimMarkdownSpaceStart(value) {\n    let index = 0;\n    let code = value.charCodeAt(index);\n    while(code === 9 || code === 32){\n        index++;\n        code = value.charCodeAt(index);\n    }\n    return value.slice(index);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/state.js\n");

/***/ })

};
;