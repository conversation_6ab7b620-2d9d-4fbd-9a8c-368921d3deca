"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-symbol";
exports.ids = ["vendor-chunks/micromark-util-symbol"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/codes.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codes: () => (/* binding */ codes)\n/* harmony export */ });\n/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */ const codes = /** @type {const} */ {\n    carriageReturn: -5,\n    lineFeed: -4,\n    carriageReturnLineFeed: -3,\n    horizontalTab: -2,\n    virtualSpace: -1,\n    eof: null,\n    nul: 0,\n    soh: 1,\n    stx: 2,\n    etx: 3,\n    eot: 4,\n    enq: 5,\n    ack: 6,\n    bel: 7,\n    bs: 8,\n    ht: 9,\n    lf: 10,\n    vt: 11,\n    ff: 12,\n    cr: 13,\n    so: 14,\n    si: 15,\n    dle: 16,\n    dc1: 17,\n    dc2: 18,\n    dc3: 19,\n    dc4: 20,\n    nak: 21,\n    syn: 22,\n    etb: 23,\n    can: 24,\n    em: 25,\n    sub: 26,\n    esc: 27,\n    fs: 28,\n    gs: 29,\n    rs: 30,\n    us: 31,\n    space: 32,\n    exclamationMark: 33,\n    quotationMark: 34,\n    numberSign: 35,\n    dollarSign: 36,\n    percentSign: 37,\n    ampersand: 38,\n    apostrophe: 39,\n    leftParenthesis: 40,\n    rightParenthesis: 41,\n    asterisk: 42,\n    plusSign: 43,\n    comma: 44,\n    dash: 45,\n    dot: 46,\n    slash: 47,\n    digit0: 48,\n    digit1: 49,\n    digit2: 50,\n    digit3: 51,\n    digit4: 52,\n    digit5: 53,\n    digit6: 54,\n    digit7: 55,\n    digit8: 56,\n    digit9: 57,\n    colon: 58,\n    semicolon: 59,\n    lessThan: 60,\n    equalsTo: 61,\n    greaterThan: 62,\n    questionMark: 63,\n    atSign: 64,\n    uppercaseA: 65,\n    uppercaseB: 66,\n    uppercaseC: 67,\n    uppercaseD: 68,\n    uppercaseE: 69,\n    uppercaseF: 70,\n    uppercaseG: 71,\n    uppercaseH: 72,\n    uppercaseI: 73,\n    uppercaseJ: 74,\n    uppercaseK: 75,\n    uppercaseL: 76,\n    uppercaseM: 77,\n    uppercaseN: 78,\n    uppercaseO: 79,\n    uppercaseP: 80,\n    uppercaseQ: 81,\n    uppercaseR: 82,\n    uppercaseS: 83,\n    uppercaseT: 84,\n    uppercaseU: 85,\n    uppercaseV: 86,\n    uppercaseW: 87,\n    uppercaseX: 88,\n    uppercaseY: 89,\n    uppercaseZ: 90,\n    leftSquareBracket: 91,\n    backslash: 92,\n    rightSquareBracket: 93,\n    caret: 94,\n    underscore: 95,\n    graveAccent: 96,\n    lowercaseA: 97,\n    lowercaseB: 98,\n    lowercaseC: 99,\n    lowercaseD: 100,\n    lowercaseE: 101,\n    lowercaseF: 102,\n    lowercaseG: 103,\n    lowercaseH: 104,\n    lowercaseI: 105,\n    lowercaseJ: 106,\n    lowercaseK: 107,\n    lowercaseL: 108,\n    lowercaseM: 109,\n    lowercaseN: 110,\n    lowercaseO: 111,\n    lowercaseP: 112,\n    lowercaseQ: 113,\n    lowercaseR: 114,\n    lowercaseS: 115,\n    lowercaseT: 116,\n    lowercaseU: 117,\n    lowercaseV: 118,\n    lowercaseW: 119,\n    lowercaseX: 120,\n    lowercaseY: 121,\n    lowercaseZ: 122,\n    leftCurlyBrace: 123,\n    verticalBar: 124,\n    rightCurlyBrace: 125,\n    tilde: 126,\n    del: 127,\n    // Unicode Specials block.\n    byteOrderMarker: 65279,\n    // Unicode Specials block.\n    replacementCharacter: 65533 // `�`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constants: () => (/* binding */ constants)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */ const constants = /** @type {const} */ {\n    attentionSideAfter: 2,\n    attentionSideBefore: 1,\n    atxHeadingOpeningFenceSizeMax: 6,\n    autolinkDomainSizeMax: 63,\n    autolinkSchemeSizeMax: 32,\n    cdataOpeningString: \"CDATA[\",\n    characterGroupPunctuation: 2,\n    characterGroupWhitespace: 1,\n    characterReferenceDecimalSizeMax: 7,\n    characterReferenceHexadecimalSizeMax: 6,\n    characterReferenceNamedSizeMax: 31,\n    codeFencedSequenceSizeMin: 3,\n    contentTypeContent: \"content\",\n    contentTypeDocument: \"document\",\n    contentTypeFlow: \"flow\",\n    contentTypeString: \"string\",\n    contentTypeText: \"text\",\n    hardBreakPrefixSizeMin: 2,\n    htmlBasic: 6,\n    htmlCdata: 5,\n    htmlComment: 2,\n    htmlComplete: 7,\n    htmlDeclaration: 4,\n    htmlInstruction: 3,\n    htmlRawSizeMax: 8,\n    htmlRaw: 1,\n    linkResourceDestinationBalanceMax: 32,\n    linkReferenceSizeMax: 999,\n    listItemValueSizeMax: 10,\n    numericBaseDecimal: 10,\n    numericBaseHexadecimal: 0x10,\n    tabSize: 4,\n    thematicBreakMarkerCountMin: 3,\n    v8MaxSafeChunkSize: 10000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/types.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */ // Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nconst types = /** @type {const} */ {\n    // Generic type for data, such as in a title, a destination, etc.\n    data: \"data\",\n    // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n    // Such as, between a fenced code fence and an info string.\n    whitespace: \"whitespace\",\n    // Generic type for line endings (line feed, carriage return, carriage return +\n    // line feed).\n    lineEnding: \"lineEnding\",\n    // A line ending, but ending a blank line.\n    lineEndingBlank: \"lineEndingBlank\",\n    // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n    // line.\n    linePrefix: \"linePrefix\",\n    // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n    // line.\n    lineSuffix: \"lineSuffix\",\n    // Whole ATX heading:\n    //\n    // ```markdown\n    // #\n    // ## Alpha\n    // ### Bravo ###\n    // ```\n    //\n    // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n    atxHeading: \"atxHeading\",\n    // Sequence of number signs in an ATX heading (`###`).\n    atxHeadingSequence: \"atxHeadingSequence\",\n    // Content in an ATX heading (`alpha`).\n    // Includes text.\n    atxHeadingText: \"atxHeadingText\",\n    // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n    // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n    autolink: \"autolink\",\n    // Email autolink w/o markers (`<EMAIL>`)\n    autolinkEmail: \"autolinkEmail\",\n    // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n    autolinkMarker: \"autolinkMarker\",\n    // Protocol autolink w/o markers (`https://example.com`)\n    autolinkProtocol: \"autolinkProtocol\",\n    // A whole character escape (`\\-`).\n    // Includes `escapeMarker` and `characterEscapeValue`.\n    characterEscape: \"characterEscape\",\n    // The escaped character (`-`).\n    characterEscapeValue: \"characterEscapeValue\",\n    // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n    // Includes `characterReferenceMarker`, an optional\n    // `characterReferenceMarkerNumeric`, in which case an optional\n    // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n    characterReference: \"characterReference\",\n    // The start or end marker (`&` or `;`).\n    characterReferenceMarker: \"characterReferenceMarker\",\n    // Mark reference as numeric (`#`).\n    characterReferenceMarkerNumeric: \"characterReferenceMarkerNumeric\",\n    // Mark reference as numeric (`x` or `X`).\n    characterReferenceMarkerHexadecimal: \"characterReferenceMarkerHexadecimal\",\n    // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n    characterReferenceValue: \"characterReferenceValue\",\n    // Whole fenced code:\n    //\n    // ````markdown\n    // ```js\n    // alert(1)\n    // ```\n    // ````\n    codeFenced: \"codeFenced\",\n    // A fenced code fence, including whitespace, sequence, info, and meta\n    // (` ```js `).\n    codeFencedFence: \"codeFencedFence\",\n    // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n    codeFencedFenceSequence: \"codeFencedFenceSequence\",\n    // Info word (`js`) in a fence.\n    // Includes string.\n    codeFencedFenceInfo: \"codeFencedFenceInfo\",\n    // Meta words (`highlight=\"1\"`) in a fence.\n    // Includes string.\n    codeFencedFenceMeta: \"codeFencedFenceMeta\",\n    // A line of code.\n    codeFlowValue: \"codeFlowValue\",\n    // Whole indented code:\n    //\n    // ```markdown\n    //     alert(1)\n    // ```\n    //\n    // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n    codeIndented: \"codeIndented\",\n    // A text code (``` `alpha` ```).\n    // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n    // `codeTextPadding`.\n    codeText: \"codeText\",\n    codeTextData: \"codeTextData\",\n    // A space or line ending right after or before a tick.\n    codeTextPadding: \"codeTextPadding\",\n    // A text code fence (` `` `).\n    codeTextSequence: \"codeTextSequence\",\n    // Whole content:\n    //\n    // ```markdown\n    // [a]: b\n    // c\n    // =\n    // d\n    // ```\n    //\n    // Includes `paragraph` and `definition`.\n    content: \"content\",\n    // Whole definition:\n    //\n    // ```markdown\n    // [micromark]: https://github.com/micromark/micromark\n    // ```\n    //\n    // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n    // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n    definition: \"definition\",\n    // Destination of a definition (`https://github.com/micromark/micromark` or\n    // `<https://github.com/micromark/micromark>`).\n    // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n    definitionDestination: \"definitionDestination\",\n    // Enclosed destination of a definition\n    // (`<https://github.com/micromark/micromark>`).\n    // Includes `definitionDestinationLiteralMarker` and optionally\n    // `definitionDestinationString`.\n    definitionDestinationLiteral: \"definitionDestinationLiteral\",\n    // Markers of an enclosed definition destination (`<` or `>`).\n    definitionDestinationLiteralMarker: \"definitionDestinationLiteralMarker\",\n    // Unenclosed destination of a definition\n    // (`https://github.com/micromark/micromark`).\n    // Includes `definitionDestinationString`.\n    definitionDestinationRaw: \"definitionDestinationRaw\",\n    // Text in an destination (`https://github.com/micromark/micromark`).\n    // Includes string.\n    definitionDestinationString: \"definitionDestinationString\",\n    // Label of a definition (`[micromark]`).\n    // Includes `definitionLabelMarker` and `definitionLabelString`.\n    definitionLabel: \"definitionLabel\",\n    // Markers of a definition label (`[` or `]`).\n    definitionLabelMarker: \"definitionLabelMarker\",\n    // Value of a definition label (`micromark`).\n    // Includes string.\n    definitionLabelString: \"definitionLabelString\",\n    // Marker between a label and a destination (`:`).\n    definitionMarker: \"definitionMarker\",\n    // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n    // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n    definitionTitle: \"definitionTitle\",\n    // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n    definitionTitleMarker: \"definitionTitleMarker\",\n    // Data without markers in a title (`z`).\n    // Includes string.\n    definitionTitleString: \"definitionTitleString\",\n    // Emphasis (`*alpha*`).\n    // Includes `emphasisSequence` and `emphasisText`.\n    emphasis: \"emphasis\",\n    // Sequence of emphasis markers (`*` or `_`).\n    emphasisSequence: \"emphasisSequence\",\n    // Emphasis text (`alpha`).\n    // Includes text.\n    emphasisText: \"emphasisText\",\n    // The character escape marker (`\\`).\n    escapeMarker: \"escapeMarker\",\n    // A hard break created with a backslash (`\\\\n`).\n    // Note: does not include the line ending.\n    hardBreakEscape: \"hardBreakEscape\",\n    // A hard break created with trailing spaces (`  \\n`).\n    // Does not include the line ending.\n    hardBreakTrailing: \"hardBreakTrailing\",\n    // Flow HTML:\n    //\n    // ```markdown\n    // <div\n    // ```\n    //\n    // Inlcudes `lineEnding`, `htmlFlowData`.\n    htmlFlow: \"htmlFlow\",\n    htmlFlowData: \"htmlFlowData\",\n    // HTML in text (the tag in `a <i> b`).\n    // Includes `lineEnding`, `htmlTextData`.\n    htmlText: \"htmlText\",\n    htmlTextData: \"htmlTextData\",\n    // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n    // `![alpha]`).\n    // Includes `label` and an optional `resource` or `reference`.\n    image: \"image\",\n    // Whole link label (`[*alpha*]`).\n    // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n    label: \"label\",\n    // Text in an label (`*alpha*`).\n    // Includes text.\n    labelText: \"labelText\",\n    // Start a link label (`[`).\n    // Includes a `labelMarker`.\n    labelLink: \"labelLink\",\n    // Start an image label (`![`).\n    // Includes `labelImageMarker` and `labelMarker`.\n    labelImage: \"labelImage\",\n    // Marker of a label (`[` or `]`).\n    labelMarker: \"labelMarker\",\n    // Marker to start an image (`!`).\n    labelImageMarker: \"labelImageMarker\",\n    // End a label (`]`).\n    // Includes `labelMarker`.\n    labelEnd: \"labelEnd\",\n    // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n    // Includes `label` and an optional `resource` or `reference`.\n    link: \"link\",\n    // Whole paragraph:\n    //\n    // ```markdown\n    // alpha\n    // bravo.\n    // ```\n    //\n    // Includes text.\n    paragraph: \"paragraph\",\n    // A reference (`[alpha]` or `[]`).\n    // Includes `referenceMarker` and an optional `referenceString`.\n    reference: \"reference\",\n    // A reference marker (`[` or `]`).\n    referenceMarker: \"referenceMarker\",\n    // Reference text (`alpha`).\n    // Includes string.\n    referenceString: \"referenceString\",\n    // A resource (`(https://example.com \"alpha\")`).\n    // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n    // `whitespace` and `resourceTitle`.\n    resource: \"resource\",\n    // A resource destination (`https://example.com`).\n    // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n    resourceDestination: \"resourceDestination\",\n    // A literal resource destination (`<https://example.com>`).\n    // Includes `resourceDestinationLiteralMarker` and optionally\n    // `resourceDestinationString`.\n    resourceDestinationLiteral: \"resourceDestinationLiteral\",\n    // A resource destination marker (`<` or `>`).\n    resourceDestinationLiteralMarker: \"resourceDestinationLiteralMarker\",\n    // A raw resource destination (`https://example.com`).\n    // Includes `resourceDestinationString`.\n    resourceDestinationRaw: \"resourceDestinationRaw\",\n    // Resource destination text (`https://example.com`).\n    // Includes string.\n    resourceDestinationString: \"resourceDestinationString\",\n    // A resource marker (`(` or `)`).\n    resourceMarker: \"resourceMarker\",\n    // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n    // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n    resourceTitle: \"resourceTitle\",\n    // A resource title marker (`\"`, `'`, `(`, or `)`).\n    resourceTitleMarker: \"resourceTitleMarker\",\n    // Resource destination title (`alpha`).\n    // Includes string.\n    resourceTitleString: \"resourceTitleString\",\n    // Whole setext heading:\n    //\n    // ```markdown\n    // alpha\n    // bravo\n    // =====\n    // ```\n    //\n    // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n    // `setextHeadingLine`.\n    setextHeading: \"setextHeading\",\n    // Content in a setext heading (`alpha\\nbravo`).\n    // Includes text.\n    setextHeadingText: \"setextHeadingText\",\n    // Underline in a setext heading, including whitespace suffix (`==`).\n    // Includes `setextHeadingLineSequence`.\n    setextHeadingLine: \"setextHeadingLine\",\n    // Sequence of equals or dash characters in underline in a setext heading (`-`).\n    setextHeadingLineSequence: \"setextHeadingLineSequence\",\n    // Strong (`**alpha**`).\n    // Includes `strongSequence` and `strongText`.\n    strong: \"strong\",\n    // Sequence of strong markers (`**` or `__`).\n    strongSequence: \"strongSequence\",\n    // Strong text (`alpha`).\n    // Includes text.\n    strongText: \"strongText\",\n    // Whole thematic break:\n    //\n    // ```markdown\n    // * * *\n    // ```\n    //\n    // Includes `thematicBreakSequence` and `whitespace`.\n    thematicBreak: \"thematicBreak\",\n    // A sequence of one or more thematic break markers (`***`).\n    thematicBreakSequence: \"thematicBreakSequence\",\n    // Whole block quote:\n    //\n    // ```markdown\n    // > a\n    // >\n    // > b\n    // ```\n    //\n    // Includes `blockQuotePrefix` and flow.\n    blockQuote: \"blockQuote\",\n    // The `>` or `> ` of a block quote.\n    blockQuotePrefix: \"blockQuotePrefix\",\n    // The `>` of a block quote prefix.\n    blockQuoteMarker: \"blockQuoteMarker\",\n    // The optional ` ` of a block quote prefix.\n    blockQuotePrefixWhitespace: \"blockQuotePrefixWhitespace\",\n    // Whole ordered list:\n    //\n    // ```markdown\n    // 1. a\n    //    b\n    // ```\n    //\n    // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n    // lines.\n    listOrdered: \"listOrdered\",\n    // Whole unordered list:\n    //\n    // ```markdown\n    // - a\n    //   b\n    // ```\n    //\n    // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n    // lines.\n    listUnordered: \"listUnordered\",\n    // The indent of further list item lines.\n    listItemIndent: \"listItemIndent\",\n    // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n    listItemMarker: \"listItemMarker\",\n    // The thing that starts a list item, such as `1. `.\n    // Includes `listItemValue` if ordered, `listItemMarker`, and\n    // `listItemPrefixWhitespace` (unless followed by a line ending).\n    listItemPrefix: \"listItemPrefix\",\n    // The whitespace after a marker.\n    listItemPrefixWhitespace: \"listItemPrefixWhitespace\",\n    // The numerical value of an ordered item.\n    listItemValue: \"listItemValue\",\n    // Internal types used for subtokenizers, compiled away\n    chunkDocument: \"chunkDocument\",\n    chunkContent: \"chunkContent\",\n    chunkFlow: \"chunkFlow\",\n    chunkText: \"chunkText\",\n    chunkString: \"chunkString\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLXV0aWwtc3ltYm9sL2xpYi90eXBlcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Ozs7OztDQVNDLEdBRUQsMkVBQTJFO0FBQzNFLHdDQUF3QztBQUNqQyxNQUFNQSxRQUFRLGtCQUFrQixHQUFJO0lBQ3pDLGlFQUFpRTtJQUNqRUMsTUFBTTtJQUVOLHdFQUF3RTtJQUN4RSwyREFBMkQ7SUFDM0RDLFlBQVk7SUFFWiwrRUFBK0U7SUFDL0UsY0FBYztJQUNkQyxZQUFZO0lBRVosMENBQTBDO0lBQzFDQyxpQkFBaUI7SUFFakIsK0VBQStFO0lBQy9FLFFBQVE7SUFDUkMsWUFBWTtJQUVaLDZFQUE2RTtJQUM3RSxRQUFRO0lBQ1JDLFlBQVk7SUFFWixxQkFBcUI7SUFDckIsRUFBRTtJQUNGLGNBQWM7SUFDZCxJQUFJO0lBQ0osV0FBVztJQUNYLGdCQUFnQjtJQUNoQixNQUFNO0lBQ04sRUFBRTtJQUNGLGlFQUFpRTtJQUNqRUMsWUFBWTtJQUVaLHNEQUFzRDtJQUN0REMsb0JBQW9CO0lBRXBCLHVDQUF1QztJQUN2QyxpQkFBaUI7SUFDakJDLGdCQUFnQjtJQUVoQixvRUFBb0U7SUFDcEUsdUVBQXVFO0lBQ3ZFQyxVQUFVO0lBRVYsbURBQW1EO0lBQ25EQyxlQUFlO0lBRWYsdUVBQXVFO0lBQ3ZFQyxnQkFBZ0I7SUFFaEIsd0RBQXdEO0lBQ3hEQyxrQkFBa0I7SUFFbEIsbUNBQW1DO0lBQ25DLHNEQUFzRDtJQUN0REMsaUJBQWlCO0lBRWpCLCtCQUErQjtJQUMvQkMsc0JBQXNCO0lBRXRCLG9FQUFvRTtJQUNwRSxtREFBbUQ7SUFDbkQsK0RBQStEO0lBQy9ELDBFQUEwRTtJQUMxRUMsb0JBQW9CO0lBRXBCLHdDQUF3QztJQUN4Q0MsMEJBQTBCO0lBRTFCLG1DQUFtQztJQUNuQ0MsaUNBQWlDO0lBRWpDLDBDQUEwQztJQUMxQ0MscUNBQXFDO0lBRXJDLHdFQUF3RTtJQUN4RUMseUJBQXlCO0lBRXpCLHFCQUFxQjtJQUNyQixFQUFFO0lBQ0YsZUFBZTtJQUNmLFFBQVE7SUFDUixXQUFXO0lBQ1gsTUFBTTtJQUNOLE9BQU87SUFDUEMsWUFBWTtJQUVaLHNFQUFzRTtJQUN0RSxlQUFlO0lBQ2ZDLGlCQUFpQjtJQUVqQixxRUFBcUU7SUFDckVDLHlCQUF5QjtJQUV6QiwrQkFBK0I7SUFDL0IsbUJBQW1CO0lBQ25CQyxxQkFBcUI7SUFFckIsMkNBQTJDO0lBQzNDLG1CQUFtQjtJQUNuQkMscUJBQXFCO0lBRXJCLGtCQUFrQjtJQUNsQkMsZUFBZTtJQUVmLHVCQUF1QjtJQUN2QixFQUFFO0lBQ0YsY0FBYztJQUNkLGVBQWU7SUFDZixNQUFNO0lBQ04sRUFBRTtJQUNGLDREQUE0RDtJQUM1REMsY0FBYztJQUVkLGlDQUFpQztJQUNqQyw2RUFBNkU7SUFDN0UscUJBQXFCO0lBQ3JCQyxVQUFVO0lBRVZDLGNBQWM7SUFFZCx1REFBdUQ7SUFDdkRDLGlCQUFpQjtJQUVqQiw4QkFBOEI7SUFDOUJDLGtCQUFrQjtJQUVsQixpQkFBaUI7SUFDakIsRUFBRTtJQUNGLGNBQWM7SUFDZCxTQUFTO0lBQ1QsSUFBSTtJQUNKLElBQUk7SUFDSixJQUFJO0lBQ0osTUFBTTtJQUNOLEVBQUU7SUFDRix5Q0FBeUM7SUFDekNDLFNBQVM7SUFDVCxvQkFBb0I7SUFDcEIsRUFBRTtJQUNGLGNBQWM7SUFDZCxzREFBc0Q7SUFDdEQsTUFBTTtJQUNOLEVBQUU7SUFDRixnRUFBZ0U7SUFDaEUsOEVBQThFO0lBQzlFQyxZQUFZO0lBRVosMkVBQTJFO0lBQzNFLCtDQUErQztJQUMvQyx5RUFBeUU7SUFDekVDLHVCQUF1QjtJQUV2Qix1Q0FBdUM7SUFDdkMsZ0RBQWdEO0lBQ2hELCtEQUErRDtJQUMvRCxpQ0FBaUM7SUFDakNDLDhCQUE4QjtJQUU5Qiw4REFBOEQ7SUFDOURDLG9DQUFvQztJQUVwQyx5Q0FBeUM7SUFDekMsOENBQThDO0lBQzlDLDBDQUEwQztJQUMxQ0MsMEJBQTBCO0lBRTFCLHFFQUFxRTtJQUNyRSxtQkFBbUI7SUFDbkJDLDZCQUE2QjtJQUU3Qix5Q0FBeUM7SUFDekMsZ0VBQWdFO0lBQ2hFQyxpQkFBaUI7SUFFakIsOENBQThDO0lBQzlDQyx1QkFBdUI7SUFFdkIsNkNBQTZDO0lBQzdDLG1CQUFtQjtJQUNuQkMsdUJBQXVCO0lBRXZCLGtEQUFrRDtJQUNsREMsa0JBQWtCO0lBRWxCLGtEQUFrRDtJQUNsRCwyRUFBMkU7SUFDM0VDLGlCQUFpQjtJQUVqQixpRUFBaUU7SUFDakVDLHVCQUF1QjtJQUV2Qix5Q0FBeUM7SUFDekMsbUJBQW1CO0lBQ25CQyx1QkFBdUI7SUFFdkIsd0JBQXdCO0lBQ3hCLGtEQUFrRDtJQUNsREMsVUFBVTtJQUVWLDZDQUE2QztJQUM3Q0Msa0JBQWtCO0lBRWxCLDJCQUEyQjtJQUMzQixpQkFBaUI7SUFDakJDLGNBQWM7SUFFZCxxQ0FBcUM7SUFDckNDLGNBQWM7SUFFZCxpREFBaUQ7SUFDakQsMENBQTBDO0lBQzFDQyxpQkFBaUI7SUFFakIsc0RBQXNEO0lBQ3RELG9DQUFvQztJQUNwQ0MsbUJBQW1CO0lBRW5CLGFBQWE7SUFDYixFQUFFO0lBQ0YsY0FBYztJQUNkLE9BQU87SUFDUCxNQUFNO0lBQ04sRUFBRTtJQUNGLHlDQUF5QztJQUN6Q0MsVUFBVTtJQUVWQyxjQUFjO0lBRWQsdUNBQXVDO0lBQ3ZDLHlDQUF5QztJQUN6Q0MsVUFBVTtJQUVWQyxjQUFjO0lBRWQsc0VBQXNFO0lBQ3RFLGVBQWU7SUFDZiw4REFBOEQ7SUFDOURDLE9BQU87SUFFUCxrQ0FBa0M7SUFDbEMscUVBQXFFO0lBQ3JFQyxPQUFPO0lBRVAsZ0NBQWdDO0lBQ2hDLGlCQUFpQjtJQUNqQkMsV0FBVztJQUVYLDRCQUE0QjtJQUM1Qiw0QkFBNEI7SUFDNUJDLFdBQVc7SUFFWCwrQkFBK0I7SUFDL0IsaURBQWlEO0lBQ2pEQyxZQUFZO0lBRVosa0NBQWtDO0lBQ2xDQyxhQUFhO0lBRWIsa0NBQWtDO0lBQ2xDQyxrQkFBa0I7SUFFbEIscUJBQXFCO0lBQ3JCLDBCQUEwQjtJQUMxQkMsVUFBVTtJQUVWLDhFQUE4RTtJQUM5RSw4REFBOEQ7SUFDOURDLE1BQU07SUFFTixtQkFBbUI7SUFDbkIsRUFBRTtJQUNGLGNBQWM7SUFDZCxRQUFRO0lBQ1IsU0FBUztJQUNULE1BQU07SUFDTixFQUFFO0lBQ0YsaUJBQWlCO0lBQ2pCQyxXQUFXO0lBRVgsbUNBQW1DO0lBQ25DLGdFQUFnRTtJQUNoRUMsV0FBVztJQUVYLG1DQUFtQztJQUNuQ0MsaUJBQWlCO0lBRWpCLDRCQUE0QjtJQUM1QixtQkFBbUI7SUFDbkJDLGlCQUFpQjtJQUVqQixnREFBZ0Q7SUFDaEQsZ0ZBQWdGO0lBQ2hGLG9DQUFvQztJQUNwQ0MsVUFBVTtJQUVWLGtEQUFrRDtJQUNsRCxxRUFBcUU7SUFDckVDLHFCQUFxQjtJQUVyQiw0REFBNEQ7SUFDNUQsNkRBQTZEO0lBQzdELCtCQUErQjtJQUMvQkMsNEJBQTRCO0lBRTVCLDhDQUE4QztJQUM5Q0Msa0NBQWtDO0lBRWxDLHNEQUFzRDtJQUN0RCx3Q0FBd0M7SUFDeENDLHdCQUF3QjtJQUV4QixxREFBcUQ7SUFDckQsbUJBQW1CO0lBQ25CQywyQkFBMkI7SUFFM0Isa0NBQWtDO0lBQ2xDQyxnQkFBZ0I7SUFFaEIseURBQXlEO0lBQ3pELHVFQUF1RTtJQUN2RUMsZUFBZTtJQUVmLG1EQUFtRDtJQUNuREMscUJBQXFCO0lBRXJCLHdDQUF3QztJQUN4QyxtQkFBbUI7SUFDbkJDLHFCQUFxQjtJQUVyQix3QkFBd0I7SUFDeEIsRUFBRTtJQUNGLGNBQWM7SUFDZCxRQUFRO0lBQ1IsUUFBUTtJQUNSLFFBQVE7SUFDUixNQUFNO0lBQ04sRUFBRTtJQUNGLGdFQUFnRTtJQUNoRSx1QkFBdUI7SUFDdkJDLGVBQWU7SUFFZixnREFBZ0Q7SUFDaEQsaUJBQWlCO0lBQ2pCQyxtQkFBbUI7SUFFbkIscUVBQXFFO0lBQ3JFLHdDQUF3QztJQUN4Q0MsbUJBQW1CO0lBRW5CLGdGQUFnRjtJQUNoRkMsMkJBQTJCO0lBRTNCLHdCQUF3QjtJQUN4Qiw4Q0FBOEM7SUFDOUNDLFFBQVE7SUFFUiw2Q0FBNkM7SUFDN0NDLGdCQUFnQjtJQUVoQix5QkFBeUI7SUFDekIsaUJBQWlCO0lBQ2pCQyxZQUFZO0lBRVosd0JBQXdCO0lBQ3hCLEVBQUU7SUFDRixjQUFjO0lBQ2QsUUFBUTtJQUNSLE1BQU07SUFDTixFQUFFO0lBQ0YscURBQXFEO0lBQ3JEQyxlQUFlO0lBRWYsNERBQTREO0lBQzVEQyx1QkFBdUI7SUFFdkIscUJBQXFCO0lBQ3JCLEVBQUU7SUFDRixjQUFjO0lBQ2QsTUFBTTtJQUNOLElBQUk7SUFDSixNQUFNO0lBQ04sTUFBTTtJQUNOLEVBQUU7SUFDRix3Q0FBd0M7SUFDeENDLFlBQVk7SUFDWixvQ0FBb0M7SUFDcENDLGtCQUFrQjtJQUNsQixtQ0FBbUM7SUFDbkNDLGtCQUFrQjtJQUNsQiw0Q0FBNEM7SUFDNUNDLDRCQUE0QjtJQUU1QixzQkFBc0I7SUFDdEIsRUFBRTtJQUNGLGNBQWM7SUFDZCxPQUFPO0lBQ1AsT0FBTztJQUNQLE1BQU07SUFDTixFQUFFO0lBQ0YsK0VBQStFO0lBQy9FLFNBQVM7SUFDVEMsYUFBYTtJQUViLHdCQUF3QjtJQUN4QixFQUFFO0lBQ0YsY0FBYztJQUNkLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLEVBQUU7SUFDRiwrRUFBK0U7SUFDL0UsU0FBUztJQUNUQyxlQUFlO0lBRWYseUNBQXlDO0lBQ3pDQyxnQkFBZ0I7SUFFaEIsK0NBQStDO0lBQy9DQyxnQkFBZ0I7SUFFaEIsb0RBQW9EO0lBQ3BELDZEQUE2RDtJQUM3RCxpRUFBaUU7SUFDakVDLGdCQUFnQjtJQUVoQixpQ0FBaUM7SUFDakNDLDBCQUEwQjtJQUUxQiwwQ0FBMEM7SUFDMUNDLGVBQWU7SUFFZix1REFBdUQ7SUFDdkRDLGVBQWU7SUFDZkMsY0FBYztJQUNkQyxXQUFXO0lBQ1hDLFdBQVc7SUFDWEMsYUFBYTtBQUNmLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9taWNyb21hcmstdXRpbC1zeW1ib2wvbGliL3R5cGVzLmpzP2IzNTkiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBUaGlzIG1vZHVsZSBpcyBjb21waWxlZCBhd2F5IVxuICpcbiAqIEhlcmUgaXMgdGhlIGxpc3Qgb2YgYWxsIHR5cGVzIG9mIHRva2VucyBleHBvc2VkIGJ5IG1pY3JvbWFyaywgd2l0aCBhIHNob3J0XG4gKiBleHBsYW5hdGlvbiBvZiB3aGF0IHRoZXkgaW5jbHVkZSBhbmQgd2hlcmUgdGhleSBhcmUgZm91bmQuXG4gKiBJbiBwaWNraW5nIG5hbWVzLCBnZW5lcmFsbHksIHRoZSBydWxlIGlzIHRvIGJlIGFzIGV4cGxpY2l0IGFzIHBvc3NpYmxlXG4gKiBpbnN0ZWFkIG9mIHJldXNpbmcgbmFtZXMuXG4gKiBGb3IgZXhhbXBsZSwgdGhlcmUgaXMgYSBgZGVmaW5pdGlvbkRlc3RpbmF0aW9uYCBhbmQgYSBgcmVzb3VyY2VEZXN0aW5hdGlvbmAsXG4gKiBpbnN0ZWFkIG9mIG9uZSBzaGFyZWQgbmFtZS5cbiAqL1xuXG4vLyBOb3RlOiB3aGVuIGNoYW5naW5nIHRoZSBuZXh0IHJlY29yZCwgeW91IG11c3QgYWxzbyBjaGFuZ2UgYFRva2VuVHlwZU1hcGBcbi8vIGluIGBtaWNyb21hcmstdXRpbC10eXBlcy9pbmRleC5kLnRzYC5cbmV4cG9ydCBjb25zdCB0eXBlcyA9IC8qKiBAdHlwZSB7Y29uc3R9ICovICh7XG4gIC8vIEdlbmVyaWMgdHlwZSBmb3IgZGF0YSwgc3VjaCBhcyBpbiBhIHRpdGxlLCBhIGRlc3RpbmF0aW9uLCBldGMuXG4gIGRhdGE6ICdkYXRhJyxcblxuICAvLyBHZW5lcmljIHR5cGUgZm9yIHN5bnRhY3RpYyB3aGl0ZXNwYWNlICh0YWJzLCB2aXJ0dWFsIHNwYWNlcywgc3BhY2VzKS5cbiAgLy8gU3VjaCBhcywgYmV0d2VlbiBhIGZlbmNlZCBjb2RlIGZlbmNlIGFuZCBhbiBpbmZvIHN0cmluZy5cbiAgd2hpdGVzcGFjZTogJ3doaXRlc3BhY2UnLFxuXG4gIC8vIEdlbmVyaWMgdHlwZSBmb3IgbGluZSBlbmRpbmdzIChsaW5lIGZlZWQsIGNhcnJpYWdlIHJldHVybiwgY2FycmlhZ2UgcmV0dXJuICtcbiAgLy8gbGluZSBmZWVkKS5cbiAgbGluZUVuZGluZzogJ2xpbmVFbmRpbmcnLFxuXG4gIC8vIEEgbGluZSBlbmRpbmcsIGJ1dCBlbmRpbmcgYSBibGFuayBsaW5lLlxuICBsaW5lRW5kaW5nQmxhbms6ICdsaW5lRW5kaW5nQmxhbmsnLFxuXG4gIC8vIEdlbmVyaWMgdHlwZSBmb3Igd2hpdGVzcGFjZSAodGFicywgdmlydHVhbCBzcGFjZXMsIHNwYWNlcykgYXQgdGhlIHN0YXJ0IG9mIGFcbiAgLy8gbGluZS5cbiAgbGluZVByZWZpeDogJ2xpbmVQcmVmaXgnLFxuXG4gIC8vIEdlbmVyaWMgdHlwZSBmb3Igd2hpdGVzcGFjZSAodGFicywgdmlydHVhbCBzcGFjZXMsIHNwYWNlcykgYXQgdGhlIGVuZCBvZiBhXG4gIC8vIGxpbmUuXG4gIGxpbmVTdWZmaXg6ICdsaW5lU3VmZml4JyxcblxuICAvLyBXaG9sZSBBVFggaGVhZGluZzpcbiAgLy9cbiAgLy8gYGBgbWFya2Rvd25cbiAgLy8gI1xuICAvLyAjIyBBbHBoYVxuICAvLyAjIyMgQnJhdm8gIyMjXG4gIC8vIGBgYFxuICAvL1xuICAvLyBJbmNsdWRlcyBgYXR4SGVhZGluZ1NlcXVlbmNlYCwgYHdoaXRlc3BhY2VgLCBgYXR4SGVhZGluZ1RleHRgLlxuICBhdHhIZWFkaW5nOiAnYXR4SGVhZGluZycsXG5cbiAgLy8gU2VxdWVuY2Ugb2YgbnVtYmVyIHNpZ25zIGluIGFuIEFUWCBoZWFkaW5nIChgIyMjYCkuXG4gIGF0eEhlYWRpbmdTZXF1ZW5jZTogJ2F0eEhlYWRpbmdTZXF1ZW5jZScsXG5cbiAgLy8gQ29udGVudCBpbiBhbiBBVFggaGVhZGluZyAoYGFscGhhYCkuXG4gIC8vIEluY2x1ZGVzIHRleHQuXG4gIGF0eEhlYWRpbmdUZXh0OiAnYXR4SGVhZGluZ1RleHQnLFxuXG4gIC8vIFdob2xlIGF1dG9saW5rIChgPGh0dHBzOi8vZXhhbXBsZS5jb20+YCBvciBgPGFkbWluQGV4YW1wbGUuY29tPmApXG4gIC8vIEluY2x1ZGVzIGBhdXRvbGlua01hcmtlcmAgYW5kIGBhdXRvbGlua1Byb3RvY29sYCBvciBgYXV0b2xpbmtFbWFpbGAuXG4gIGF1dG9saW5rOiAnYXV0b2xpbmsnLFxuXG4gIC8vIEVtYWlsIGF1dG9saW5rIHcvbyBtYXJrZXJzIChgYWRtaW5AZXhhbXBsZS5jb21gKVxuICBhdXRvbGlua0VtYWlsOiAnYXV0b2xpbmtFbWFpbCcsXG5cbiAgLy8gTWFya2VyIGFyb3VuZCBhbiBgYXV0b2xpbmtQcm90b2NvbGAgb3IgYGF1dG9saW5rRW1haWxgIChgPGAgb3IgYD5gKS5cbiAgYXV0b2xpbmtNYXJrZXI6ICdhdXRvbGlua01hcmtlcicsXG5cbiAgLy8gUHJvdG9jb2wgYXV0b2xpbmsgdy9vIG1hcmtlcnMgKGBodHRwczovL2V4YW1wbGUuY29tYClcbiAgYXV0b2xpbmtQcm90b2NvbDogJ2F1dG9saW5rUHJvdG9jb2wnLFxuXG4gIC8vIEEgd2hvbGUgY2hhcmFjdGVyIGVzY2FwZSAoYFxcLWApLlxuICAvLyBJbmNsdWRlcyBgZXNjYXBlTWFya2VyYCBhbmQgYGNoYXJhY3RlckVzY2FwZVZhbHVlYC5cbiAgY2hhcmFjdGVyRXNjYXBlOiAnY2hhcmFjdGVyRXNjYXBlJyxcblxuICAvLyBUaGUgZXNjYXBlZCBjaGFyYWN0ZXIgKGAtYCkuXG4gIGNoYXJhY3RlckVzY2FwZVZhbHVlOiAnY2hhcmFjdGVyRXNjYXBlVmFsdWUnLFxuXG4gIC8vIEEgd2hvbGUgY2hhcmFjdGVyIHJlZmVyZW5jZSAoYCZhbXA7YCwgYCYjODgwMDtgLCBvciBgJiN4MUQzMDY7YCkuXG4gIC8vIEluY2x1ZGVzIGBjaGFyYWN0ZXJSZWZlcmVuY2VNYXJrZXJgLCBhbiBvcHRpb25hbFxuICAvLyBgY2hhcmFjdGVyUmVmZXJlbmNlTWFya2VyTnVtZXJpY2AsIGluIHdoaWNoIGNhc2UgYW4gb3B0aW9uYWxcbiAgLy8gYGNoYXJhY3RlclJlZmVyZW5jZU1hcmtlckhleGFkZWNpbWFsYCwgYW5kIGEgYGNoYXJhY3RlclJlZmVyZW5jZVZhbHVlYC5cbiAgY2hhcmFjdGVyUmVmZXJlbmNlOiAnY2hhcmFjdGVyUmVmZXJlbmNlJyxcblxuICAvLyBUaGUgc3RhcnQgb3IgZW5kIG1hcmtlciAoYCZgIG9yIGA7YCkuXG4gIGNoYXJhY3RlclJlZmVyZW5jZU1hcmtlcjogJ2NoYXJhY3RlclJlZmVyZW5jZU1hcmtlcicsXG5cbiAgLy8gTWFyayByZWZlcmVuY2UgYXMgbnVtZXJpYyAoYCNgKS5cbiAgY2hhcmFjdGVyUmVmZXJlbmNlTWFya2VyTnVtZXJpYzogJ2NoYXJhY3RlclJlZmVyZW5jZU1hcmtlck51bWVyaWMnLFxuXG4gIC8vIE1hcmsgcmVmZXJlbmNlIGFzIG51bWVyaWMgKGB4YCBvciBgWGApLlxuICBjaGFyYWN0ZXJSZWZlcmVuY2VNYXJrZXJIZXhhZGVjaW1hbDogJ2NoYXJhY3RlclJlZmVyZW5jZU1hcmtlckhleGFkZWNpbWFsJyxcblxuICAvLyBWYWx1ZSBvZiBjaGFyYWN0ZXIgcmVmZXJlbmNlIHcvbyBtYXJrZXJzIChgYW1wYCwgYDg4MDBgLCBvciBgMUQzMDZgKS5cbiAgY2hhcmFjdGVyUmVmZXJlbmNlVmFsdWU6ICdjaGFyYWN0ZXJSZWZlcmVuY2VWYWx1ZScsXG5cbiAgLy8gV2hvbGUgZmVuY2VkIGNvZGU6XG4gIC8vXG4gIC8vIGBgYGBtYXJrZG93blxuICAvLyBgYGBqc1xuICAvLyBhbGVydCgxKVxuICAvLyBgYGBcbiAgLy8gYGBgYFxuICBjb2RlRmVuY2VkOiAnY29kZUZlbmNlZCcsXG5cbiAgLy8gQSBmZW5jZWQgY29kZSBmZW5jZSwgaW5jbHVkaW5nIHdoaXRlc3BhY2UsIHNlcXVlbmNlLCBpbmZvLCBhbmQgbWV0YVxuICAvLyAoYCBgYGBqcyBgKS5cbiAgY29kZUZlbmNlZEZlbmNlOiAnY29kZUZlbmNlZEZlbmNlJyxcblxuICAvLyBTZXF1ZW5jZSBvZiBncmF2ZSBhY2NlbnQgb3IgdGlsZGUgY2hhcmFjdGVycyAoYCBgYGAgYCkgaW4gYSBmZW5jZS5cbiAgY29kZUZlbmNlZEZlbmNlU2VxdWVuY2U6ICdjb2RlRmVuY2VkRmVuY2VTZXF1ZW5jZScsXG5cbiAgLy8gSW5mbyB3b3JkIChganNgKSBpbiBhIGZlbmNlLlxuICAvLyBJbmNsdWRlcyBzdHJpbmcuXG4gIGNvZGVGZW5jZWRGZW5jZUluZm86ICdjb2RlRmVuY2VkRmVuY2VJbmZvJyxcblxuICAvLyBNZXRhIHdvcmRzIChgaGlnaGxpZ2h0PVwiMVwiYCkgaW4gYSBmZW5jZS5cbiAgLy8gSW5jbHVkZXMgc3RyaW5nLlxuICBjb2RlRmVuY2VkRmVuY2VNZXRhOiAnY29kZUZlbmNlZEZlbmNlTWV0YScsXG5cbiAgLy8gQSBsaW5lIG9mIGNvZGUuXG4gIGNvZGVGbG93VmFsdWU6ICdjb2RlRmxvd1ZhbHVlJyxcblxuICAvLyBXaG9sZSBpbmRlbnRlZCBjb2RlOlxuICAvL1xuICAvLyBgYGBtYXJrZG93blxuICAvLyAgICAgYWxlcnQoMSlcbiAgLy8gYGBgXG4gIC8vXG4gIC8vIEluY2x1ZGVzIGBsaW5lRW5kaW5nYCwgYGxpbmVQcmVmaXhgLCBhbmQgYGNvZGVGbG93VmFsdWVgLlxuICBjb2RlSW5kZW50ZWQ6ICdjb2RlSW5kZW50ZWQnLFxuXG4gIC8vIEEgdGV4dCBjb2RlIChgYGAgYGFscGhhYCBgYGApLlxuICAvLyBJbmNsdWRlcyBgY29kZVRleHRTZXF1ZW5jZWAsIGBjb2RlVGV4dERhdGFgLCBgbGluZUVuZGluZ2AsIGFuZCBjYW4gaW5jbHVkZVxuICAvLyBgY29kZVRleHRQYWRkaW5nYC5cbiAgY29kZVRleHQ6ICdjb2RlVGV4dCcsXG5cbiAgY29kZVRleHREYXRhOiAnY29kZVRleHREYXRhJyxcblxuICAvLyBBIHNwYWNlIG9yIGxpbmUgZW5kaW5nIHJpZ2h0IGFmdGVyIG9yIGJlZm9yZSBhIHRpY2suXG4gIGNvZGVUZXh0UGFkZGluZzogJ2NvZGVUZXh0UGFkZGluZycsXG5cbiAgLy8gQSB0ZXh0IGNvZGUgZmVuY2UgKGAgYGAgYCkuXG4gIGNvZGVUZXh0U2VxdWVuY2U6ICdjb2RlVGV4dFNlcXVlbmNlJyxcblxuICAvLyBXaG9sZSBjb250ZW50OlxuICAvL1xuICAvLyBgYGBtYXJrZG93blxuICAvLyBbYV06IGJcbiAgLy8gY1xuICAvLyA9XG4gIC8vIGRcbiAgLy8gYGBgXG4gIC8vXG4gIC8vIEluY2x1ZGVzIGBwYXJhZ3JhcGhgIGFuZCBgZGVmaW5pdGlvbmAuXG4gIGNvbnRlbnQ6ICdjb250ZW50JyxcbiAgLy8gV2hvbGUgZGVmaW5pdGlvbjpcbiAgLy9cbiAgLy8gYGBgbWFya2Rvd25cbiAgLy8gW21pY3JvbWFya106IGh0dHBzOi8vZ2l0aHViLmNvbS9taWNyb21hcmsvbWljcm9tYXJrXG4gIC8vIGBgYFxuICAvL1xuICAvLyBJbmNsdWRlcyBgZGVmaW5pdGlvbkxhYmVsYCwgYGRlZmluaXRpb25NYXJrZXJgLCBgd2hpdGVzcGFjZWAsXG4gIC8vIGBkZWZpbml0aW9uRGVzdGluYXRpb25gLCBhbmQgb3B0aW9uYWxseSBgbGluZUVuZGluZ2AgYW5kIGBkZWZpbml0aW9uVGl0bGVgLlxuICBkZWZpbml0aW9uOiAnZGVmaW5pdGlvbicsXG5cbiAgLy8gRGVzdGluYXRpb24gb2YgYSBkZWZpbml0aW9uIChgaHR0cHM6Ly9naXRodWIuY29tL21pY3JvbWFyay9taWNyb21hcmtgIG9yXG4gIC8vIGA8aHR0cHM6Ly9naXRodWIuY29tL21pY3JvbWFyay9taWNyb21hcms+YCkuXG4gIC8vIEluY2x1ZGVzIGBkZWZpbml0aW9uRGVzdGluYXRpb25MaXRlcmFsYCBvciBgZGVmaW5pdGlvbkRlc3RpbmF0aW9uUmF3YC5cbiAgZGVmaW5pdGlvbkRlc3RpbmF0aW9uOiAnZGVmaW5pdGlvbkRlc3RpbmF0aW9uJyxcblxuICAvLyBFbmNsb3NlZCBkZXN0aW5hdGlvbiBvZiBhIGRlZmluaXRpb25cbiAgLy8gKGA8aHR0cHM6Ly9naXRodWIuY29tL21pY3JvbWFyay9taWNyb21hcms+YCkuXG4gIC8vIEluY2x1ZGVzIGBkZWZpbml0aW9uRGVzdGluYXRpb25MaXRlcmFsTWFya2VyYCBhbmQgb3B0aW9uYWxseVxuICAvLyBgZGVmaW5pdGlvbkRlc3RpbmF0aW9uU3RyaW5nYC5cbiAgZGVmaW5pdGlvbkRlc3RpbmF0aW9uTGl0ZXJhbDogJ2RlZmluaXRpb25EZXN0aW5hdGlvbkxpdGVyYWwnLFxuXG4gIC8vIE1hcmtlcnMgb2YgYW4gZW5jbG9zZWQgZGVmaW5pdGlvbiBkZXN0aW5hdGlvbiAoYDxgIG9yIGA+YCkuXG4gIGRlZmluaXRpb25EZXN0aW5hdGlvbkxpdGVyYWxNYXJrZXI6ICdkZWZpbml0aW9uRGVzdGluYXRpb25MaXRlcmFsTWFya2VyJyxcblxuICAvLyBVbmVuY2xvc2VkIGRlc3RpbmF0aW9uIG9mIGEgZGVmaW5pdGlvblxuICAvLyAoYGh0dHBzOi8vZ2l0aHViLmNvbS9taWNyb21hcmsvbWljcm9tYXJrYCkuXG4gIC8vIEluY2x1ZGVzIGBkZWZpbml0aW9uRGVzdGluYXRpb25TdHJpbmdgLlxuICBkZWZpbml0aW9uRGVzdGluYXRpb25SYXc6ICdkZWZpbml0aW9uRGVzdGluYXRpb25SYXcnLFxuXG4gIC8vIFRleHQgaW4gYW4gZGVzdGluYXRpb24gKGBodHRwczovL2dpdGh1Yi5jb20vbWljcm9tYXJrL21pY3JvbWFya2ApLlxuICAvLyBJbmNsdWRlcyBzdHJpbmcuXG4gIGRlZmluaXRpb25EZXN0aW5hdGlvblN0cmluZzogJ2RlZmluaXRpb25EZXN0aW5hdGlvblN0cmluZycsXG5cbiAgLy8gTGFiZWwgb2YgYSBkZWZpbml0aW9uIChgW21pY3JvbWFya11gKS5cbiAgLy8gSW5jbHVkZXMgYGRlZmluaXRpb25MYWJlbE1hcmtlcmAgYW5kIGBkZWZpbml0aW9uTGFiZWxTdHJpbmdgLlxuICBkZWZpbml0aW9uTGFiZWw6ICdkZWZpbml0aW9uTGFiZWwnLFxuXG4gIC8vIE1hcmtlcnMgb2YgYSBkZWZpbml0aW9uIGxhYmVsIChgW2Agb3IgYF1gKS5cbiAgZGVmaW5pdGlvbkxhYmVsTWFya2VyOiAnZGVmaW5pdGlvbkxhYmVsTWFya2VyJyxcblxuICAvLyBWYWx1ZSBvZiBhIGRlZmluaXRpb24gbGFiZWwgKGBtaWNyb21hcmtgKS5cbiAgLy8gSW5jbHVkZXMgc3RyaW5nLlxuICBkZWZpbml0aW9uTGFiZWxTdHJpbmc6ICdkZWZpbml0aW9uTGFiZWxTdHJpbmcnLFxuXG4gIC8vIE1hcmtlciBiZXR3ZWVuIGEgbGFiZWwgYW5kIGEgZGVzdGluYXRpb24gKGA6YCkuXG4gIGRlZmluaXRpb25NYXJrZXI6ICdkZWZpbml0aW9uTWFya2VyJyxcblxuICAvLyBUaXRsZSBvZiBhIGRlZmluaXRpb24gKGBcInhcImAsIGAneSdgLCBvciBgKHopYCkuXG4gIC8vIEluY2x1ZGVzIGBkZWZpbml0aW9uVGl0bGVNYXJrZXJgIGFuZCBvcHRpb25hbGx5IGBkZWZpbml0aW9uVGl0bGVTdHJpbmdgLlxuICBkZWZpbml0aW9uVGl0bGU6ICdkZWZpbml0aW9uVGl0bGUnLFxuXG4gIC8vIE1hcmtlciBhcm91bmQgYSB0aXRsZSBvZiBhIGRlZmluaXRpb24gKGBcImAsIGAnYCwgYChgLCBvciBgKWApLlxuICBkZWZpbml0aW9uVGl0bGVNYXJrZXI6ICdkZWZpbml0aW9uVGl0bGVNYXJrZXInLFxuXG4gIC8vIERhdGEgd2l0aG91dCBtYXJrZXJzIGluIGEgdGl0bGUgKGB6YCkuXG4gIC8vIEluY2x1ZGVzIHN0cmluZy5cbiAgZGVmaW5pdGlvblRpdGxlU3RyaW5nOiAnZGVmaW5pdGlvblRpdGxlU3RyaW5nJyxcblxuICAvLyBFbXBoYXNpcyAoYCphbHBoYSpgKS5cbiAgLy8gSW5jbHVkZXMgYGVtcGhhc2lzU2VxdWVuY2VgIGFuZCBgZW1waGFzaXNUZXh0YC5cbiAgZW1waGFzaXM6ICdlbXBoYXNpcycsXG5cbiAgLy8gU2VxdWVuY2Ugb2YgZW1waGFzaXMgbWFya2VycyAoYCpgIG9yIGBfYCkuXG4gIGVtcGhhc2lzU2VxdWVuY2U6ICdlbXBoYXNpc1NlcXVlbmNlJyxcblxuICAvLyBFbXBoYXNpcyB0ZXh0IChgYWxwaGFgKS5cbiAgLy8gSW5jbHVkZXMgdGV4dC5cbiAgZW1waGFzaXNUZXh0OiAnZW1waGFzaXNUZXh0JyxcblxuICAvLyBUaGUgY2hhcmFjdGVyIGVzY2FwZSBtYXJrZXIgKGBcXGApLlxuICBlc2NhcGVNYXJrZXI6ICdlc2NhcGVNYXJrZXInLFxuXG4gIC8vIEEgaGFyZCBicmVhayBjcmVhdGVkIHdpdGggYSBiYWNrc2xhc2ggKGBcXFxcbmApLlxuICAvLyBOb3RlOiBkb2VzIG5vdCBpbmNsdWRlIHRoZSBsaW5lIGVuZGluZy5cbiAgaGFyZEJyZWFrRXNjYXBlOiAnaGFyZEJyZWFrRXNjYXBlJyxcblxuICAvLyBBIGhhcmQgYnJlYWsgY3JlYXRlZCB3aXRoIHRyYWlsaW5nIHNwYWNlcyAoYCAgXFxuYCkuXG4gIC8vIERvZXMgbm90IGluY2x1ZGUgdGhlIGxpbmUgZW5kaW5nLlxuICBoYXJkQnJlYWtUcmFpbGluZzogJ2hhcmRCcmVha1RyYWlsaW5nJyxcblxuICAvLyBGbG93IEhUTUw6XG4gIC8vXG4gIC8vIGBgYG1hcmtkb3duXG4gIC8vIDxkaXZcbiAgLy8gYGBgXG4gIC8vXG4gIC8vIElubGN1ZGVzIGBsaW5lRW5kaW5nYCwgYGh0bWxGbG93RGF0YWAuXG4gIGh0bWxGbG93OiAnaHRtbEZsb3cnLFxuXG4gIGh0bWxGbG93RGF0YTogJ2h0bWxGbG93RGF0YScsXG5cbiAgLy8gSFRNTCBpbiB0ZXh0ICh0aGUgdGFnIGluIGBhIDxpPiBiYCkuXG4gIC8vIEluY2x1ZGVzIGBsaW5lRW5kaW5nYCwgYGh0bWxUZXh0RGF0YWAuXG4gIGh0bWxUZXh0OiAnaHRtbFRleHQnLFxuXG4gIGh0bWxUZXh0RGF0YTogJ2h0bWxUZXh0RGF0YScsXG5cbiAgLy8gV2hvbGUgaW1hZ2UgKGAhW2FscGhhXShicmF2bylgLCBgIVthbHBoYV1bYnJhdm9dYCwgYCFbYWxwaGFdW11gLCBvclxuICAvLyBgIVthbHBoYV1gKS5cbiAgLy8gSW5jbHVkZXMgYGxhYmVsYCBhbmQgYW4gb3B0aW9uYWwgYHJlc291cmNlYCBvciBgcmVmZXJlbmNlYC5cbiAgaW1hZ2U6ICdpbWFnZScsXG5cbiAgLy8gV2hvbGUgbGluayBsYWJlbCAoYFsqYWxwaGEqXWApLlxuICAvLyBJbmNsdWRlcyBgbGFiZWxMaW5rYCBvciBgbGFiZWxJbWFnZWAsIGBsYWJlbFRleHRgLCBhbmQgYGxhYmVsRW5kYC5cbiAgbGFiZWw6ICdsYWJlbCcsXG5cbiAgLy8gVGV4dCBpbiBhbiBsYWJlbCAoYCphbHBoYSpgKS5cbiAgLy8gSW5jbHVkZXMgdGV4dC5cbiAgbGFiZWxUZXh0OiAnbGFiZWxUZXh0JyxcblxuICAvLyBTdGFydCBhIGxpbmsgbGFiZWwgKGBbYCkuXG4gIC8vIEluY2x1ZGVzIGEgYGxhYmVsTWFya2VyYC5cbiAgbGFiZWxMaW5rOiAnbGFiZWxMaW5rJyxcblxuICAvLyBTdGFydCBhbiBpbWFnZSBsYWJlbCAoYCFbYCkuXG4gIC8vIEluY2x1ZGVzIGBsYWJlbEltYWdlTWFya2VyYCBhbmQgYGxhYmVsTWFya2VyYC5cbiAgbGFiZWxJbWFnZTogJ2xhYmVsSW1hZ2UnLFxuXG4gIC8vIE1hcmtlciBvZiBhIGxhYmVsIChgW2Agb3IgYF1gKS5cbiAgbGFiZWxNYXJrZXI6ICdsYWJlbE1hcmtlcicsXG5cbiAgLy8gTWFya2VyIHRvIHN0YXJ0IGFuIGltYWdlIChgIWApLlxuICBsYWJlbEltYWdlTWFya2VyOiAnbGFiZWxJbWFnZU1hcmtlcicsXG5cbiAgLy8gRW5kIGEgbGFiZWwgKGBdYCkuXG4gIC8vIEluY2x1ZGVzIGBsYWJlbE1hcmtlcmAuXG4gIGxhYmVsRW5kOiAnbGFiZWxFbmQnLFxuXG4gIC8vIFdob2xlIGxpbmsgKGBbYWxwaGFdKGJyYXZvKWAsIGBbYWxwaGFdW2JyYXZvXWAsIGBbYWxwaGFdW11gLCBvciBgW2FscGhhXWApLlxuICAvLyBJbmNsdWRlcyBgbGFiZWxgIGFuZCBhbiBvcHRpb25hbCBgcmVzb3VyY2VgIG9yIGByZWZlcmVuY2VgLlxuICBsaW5rOiAnbGluaycsXG5cbiAgLy8gV2hvbGUgcGFyYWdyYXBoOlxuICAvL1xuICAvLyBgYGBtYXJrZG93blxuICAvLyBhbHBoYVxuICAvLyBicmF2by5cbiAgLy8gYGBgXG4gIC8vXG4gIC8vIEluY2x1ZGVzIHRleHQuXG4gIHBhcmFncmFwaDogJ3BhcmFncmFwaCcsXG5cbiAgLy8gQSByZWZlcmVuY2UgKGBbYWxwaGFdYCBvciBgW11gKS5cbiAgLy8gSW5jbHVkZXMgYHJlZmVyZW5jZU1hcmtlcmAgYW5kIGFuIG9wdGlvbmFsIGByZWZlcmVuY2VTdHJpbmdgLlxuICByZWZlcmVuY2U6ICdyZWZlcmVuY2UnLFxuXG4gIC8vIEEgcmVmZXJlbmNlIG1hcmtlciAoYFtgIG9yIGBdYCkuXG4gIHJlZmVyZW5jZU1hcmtlcjogJ3JlZmVyZW5jZU1hcmtlcicsXG5cbiAgLy8gUmVmZXJlbmNlIHRleHQgKGBhbHBoYWApLlxuICAvLyBJbmNsdWRlcyBzdHJpbmcuXG4gIHJlZmVyZW5jZVN0cmluZzogJ3JlZmVyZW5jZVN0cmluZycsXG5cbiAgLy8gQSByZXNvdXJjZSAoYChodHRwczovL2V4YW1wbGUuY29tIFwiYWxwaGFcIilgKS5cbiAgLy8gSW5jbHVkZXMgYHJlc291cmNlTWFya2VyYCwgYW4gb3B0aW9uYWwgYHJlc291cmNlRGVzdGluYXRpb25gIHdpdGggYW4gb3B0aW9uYWxcbiAgLy8gYHdoaXRlc3BhY2VgIGFuZCBgcmVzb3VyY2VUaXRsZWAuXG4gIHJlc291cmNlOiAncmVzb3VyY2UnLFxuXG4gIC8vIEEgcmVzb3VyY2UgZGVzdGluYXRpb24gKGBodHRwczovL2V4YW1wbGUuY29tYCkuXG4gIC8vIEluY2x1ZGVzIGByZXNvdXJjZURlc3RpbmF0aW9uTGl0ZXJhbGAgb3IgYHJlc291cmNlRGVzdGluYXRpb25SYXdgLlxuICByZXNvdXJjZURlc3RpbmF0aW9uOiAncmVzb3VyY2VEZXN0aW5hdGlvbicsXG5cbiAgLy8gQSBsaXRlcmFsIHJlc291cmNlIGRlc3RpbmF0aW9uIChgPGh0dHBzOi8vZXhhbXBsZS5jb20+YCkuXG4gIC8vIEluY2x1ZGVzIGByZXNvdXJjZURlc3RpbmF0aW9uTGl0ZXJhbE1hcmtlcmAgYW5kIG9wdGlvbmFsbHlcbiAgLy8gYHJlc291cmNlRGVzdGluYXRpb25TdHJpbmdgLlxuICByZXNvdXJjZURlc3RpbmF0aW9uTGl0ZXJhbDogJ3Jlc291cmNlRGVzdGluYXRpb25MaXRlcmFsJyxcblxuICAvLyBBIHJlc291cmNlIGRlc3RpbmF0aW9uIG1hcmtlciAoYDxgIG9yIGA+YCkuXG4gIHJlc291cmNlRGVzdGluYXRpb25MaXRlcmFsTWFya2VyOiAncmVzb3VyY2VEZXN0aW5hdGlvbkxpdGVyYWxNYXJrZXInLFxuXG4gIC8vIEEgcmF3IHJlc291cmNlIGRlc3RpbmF0aW9uIChgaHR0cHM6Ly9leGFtcGxlLmNvbWApLlxuICAvLyBJbmNsdWRlcyBgcmVzb3VyY2VEZXN0aW5hdGlvblN0cmluZ2AuXG4gIHJlc291cmNlRGVzdGluYXRpb25SYXc6ICdyZXNvdXJjZURlc3RpbmF0aW9uUmF3JyxcblxuICAvLyBSZXNvdXJjZSBkZXN0aW5hdGlvbiB0ZXh0IChgaHR0cHM6Ly9leGFtcGxlLmNvbWApLlxuICAvLyBJbmNsdWRlcyBzdHJpbmcuXG4gIHJlc291cmNlRGVzdGluYXRpb25TdHJpbmc6ICdyZXNvdXJjZURlc3RpbmF0aW9uU3RyaW5nJyxcblxuICAvLyBBIHJlc291cmNlIG1hcmtlciAoYChgIG9yIGApYCkuXG4gIHJlc291cmNlTWFya2VyOiAncmVzb3VyY2VNYXJrZXInLFxuXG4gIC8vIEEgcmVzb3VyY2UgdGl0bGUgKGBcImFscGhhXCJgLCBgJ2FscGhhJ2AsIG9yIGAoYWxwaGEpYCkuXG4gIC8vIEluY2x1ZGVzIGByZXNvdXJjZVRpdGxlTWFya2VyYCBhbmQgb3B0aW9uYWxseSBgcmVzb3VyY2VUaXRsZVN0cmluZ2AuXG4gIHJlc291cmNlVGl0bGU6ICdyZXNvdXJjZVRpdGxlJyxcblxuICAvLyBBIHJlc291cmNlIHRpdGxlIG1hcmtlciAoYFwiYCwgYCdgLCBgKGAsIG9yIGApYCkuXG4gIHJlc291cmNlVGl0bGVNYXJrZXI6ICdyZXNvdXJjZVRpdGxlTWFya2VyJyxcblxuICAvLyBSZXNvdXJjZSBkZXN0aW5hdGlvbiB0aXRsZSAoYGFscGhhYCkuXG4gIC8vIEluY2x1ZGVzIHN0cmluZy5cbiAgcmVzb3VyY2VUaXRsZVN0cmluZzogJ3Jlc291cmNlVGl0bGVTdHJpbmcnLFxuXG4gIC8vIFdob2xlIHNldGV4dCBoZWFkaW5nOlxuICAvL1xuICAvLyBgYGBtYXJrZG93blxuICAvLyBhbHBoYVxuICAvLyBicmF2b1xuICAvLyA9PT09PVxuICAvLyBgYGBcbiAgLy9cbiAgLy8gSW5jbHVkZXMgYHNldGV4dEhlYWRpbmdUZXh0YCwgYGxpbmVFbmRpbmdgLCBgbGluZVByZWZpeGAsIGFuZFxuICAvLyBgc2V0ZXh0SGVhZGluZ0xpbmVgLlxuICBzZXRleHRIZWFkaW5nOiAnc2V0ZXh0SGVhZGluZycsXG5cbiAgLy8gQ29udGVudCBpbiBhIHNldGV4dCBoZWFkaW5nIChgYWxwaGFcXG5icmF2b2ApLlxuICAvLyBJbmNsdWRlcyB0ZXh0LlxuICBzZXRleHRIZWFkaW5nVGV4dDogJ3NldGV4dEhlYWRpbmdUZXh0JyxcblxuICAvLyBVbmRlcmxpbmUgaW4gYSBzZXRleHQgaGVhZGluZywgaW5jbHVkaW5nIHdoaXRlc3BhY2Ugc3VmZml4IChgPT1gKS5cbiAgLy8gSW5jbHVkZXMgYHNldGV4dEhlYWRpbmdMaW5lU2VxdWVuY2VgLlxuICBzZXRleHRIZWFkaW5nTGluZTogJ3NldGV4dEhlYWRpbmdMaW5lJyxcblxuICAvLyBTZXF1ZW5jZSBvZiBlcXVhbHMgb3IgZGFzaCBjaGFyYWN0ZXJzIGluIHVuZGVybGluZSBpbiBhIHNldGV4dCBoZWFkaW5nIChgLWApLlxuICBzZXRleHRIZWFkaW5nTGluZVNlcXVlbmNlOiAnc2V0ZXh0SGVhZGluZ0xpbmVTZXF1ZW5jZScsXG5cbiAgLy8gU3Ryb25nIChgKiphbHBoYSoqYCkuXG4gIC8vIEluY2x1ZGVzIGBzdHJvbmdTZXF1ZW5jZWAgYW5kIGBzdHJvbmdUZXh0YC5cbiAgc3Ryb25nOiAnc3Ryb25nJyxcblxuICAvLyBTZXF1ZW5jZSBvZiBzdHJvbmcgbWFya2VycyAoYCoqYCBvciBgX19gKS5cbiAgc3Ryb25nU2VxdWVuY2U6ICdzdHJvbmdTZXF1ZW5jZScsXG5cbiAgLy8gU3Ryb25nIHRleHQgKGBhbHBoYWApLlxuICAvLyBJbmNsdWRlcyB0ZXh0LlxuICBzdHJvbmdUZXh0OiAnc3Ryb25nVGV4dCcsXG5cbiAgLy8gV2hvbGUgdGhlbWF0aWMgYnJlYWs6XG4gIC8vXG4gIC8vIGBgYG1hcmtkb3duXG4gIC8vICogKiAqXG4gIC8vIGBgYFxuICAvL1xuICAvLyBJbmNsdWRlcyBgdGhlbWF0aWNCcmVha1NlcXVlbmNlYCBhbmQgYHdoaXRlc3BhY2VgLlxuICB0aGVtYXRpY0JyZWFrOiAndGhlbWF0aWNCcmVhaycsXG5cbiAgLy8gQSBzZXF1ZW5jZSBvZiBvbmUgb3IgbW9yZSB0aGVtYXRpYyBicmVhayBtYXJrZXJzIChgKioqYCkuXG4gIHRoZW1hdGljQnJlYWtTZXF1ZW5jZTogJ3RoZW1hdGljQnJlYWtTZXF1ZW5jZScsXG5cbiAgLy8gV2hvbGUgYmxvY2sgcXVvdGU6XG4gIC8vXG4gIC8vIGBgYG1hcmtkb3duXG4gIC8vID4gYVxuICAvLyA+XG4gIC8vID4gYlxuICAvLyBgYGBcbiAgLy9cbiAgLy8gSW5jbHVkZXMgYGJsb2NrUXVvdGVQcmVmaXhgIGFuZCBmbG93LlxuICBibG9ja1F1b3RlOiAnYmxvY2tRdW90ZScsXG4gIC8vIFRoZSBgPmAgb3IgYD4gYCBvZiBhIGJsb2NrIHF1b3RlLlxuICBibG9ja1F1b3RlUHJlZml4OiAnYmxvY2tRdW90ZVByZWZpeCcsXG4gIC8vIFRoZSBgPmAgb2YgYSBibG9jayBxdW90ZSBwcmVmaXguXG4gIGJsb2NrUXVvdGVNYXJrZXI6ICdibG9ja1F1b3RlTWFya2VyJyxcbiAgLy8gVGhlIG9wdGlvbmFsIGAgYCBvZiBhIGJsb2NrIHF1b3RlIHByZWZpeC5cbiAgYmxvY2tRdW90ZVByZWZpeFdoaXRlc3BhY2U6ICdibG9ja1F1b3RlUHJlZml4V2hpdGVzcGFjZScsXG5cbiAgLy8gV2hvbGUgb3JkZXJlZCBsaXN0OlxuICAvL1xuICAvLyBgYGBtYXJrZG93blxuICAvLyAxLiBhXG4gIC8vICAgIGJcbiAgLy8gYGBgXG4gIC8vXG4gIC8vIEluY2x1ZGVzIGBsaXN0SXRlbVByZWZpeGAsIGZsb3csIGFuZCBvcHRpb25hbGx5ICBgbGlzdEl0ZW1JbmRlbnRgIG9uIGZ1cnRoZXJcbiAgLy8gbGluZXMuXG4gIGxpc3RPcmRlcmVkOiAnbGlzdE9yZGVyZWQnLFxuXG4gIC8vIFdob2xlIHVub3JkZXJlZCBsaXN0OlxuICAvL1xuICAvLyBgYGBtYXJrZG93blxuICAvLyAtIGFcbiAgLy8gICBiXG4gIC8vIGBgYFxuICAvL1xuICAvLyBJbmNsdWRlcyBgbGlzdEl0ZW1QcmVmaXhgLCBmbG93LCBhbmQgb3B0aW9uYWxseSAgYGxpc3RJdGVtSW5kZW50YCBvbiBmdXJ0aGVyXG4gIC8vIGxpbmVzLlxuICBsaXN0VW5vcmRlcmVkOiAnbGlzdFVub3JkZXJlZCcsXG5cbiAgLy8gVGhlIGluZGVudCBvZiBmdXJ0aGVyIGxpc3QgaXRlbSBsaW5lcy5cbiAgbGlzdEl0ZW1JbmRlbnQ6ICdsaXN0SXRlbUluZGVudCcsXG5cbiAgLy8gQSBtYXJrZXIsIGFzIGluLCBgKmAsIGArYCwgYC1gLCBgLmAsIG9yIGApYC5cbiAgbGlzdEl0ZW1NYXJrZXI6ICdsaXN0SXRlbU1hcmtlcicsXG5cbiAgLy8gVGhlIHRoaW5nIHRoYXQgc3RhcnRzIGEgbGlzdCBpdGVtLCBzdWNoIGFzIGAxLiBgLlxuICAvLyBJbmNsdWRlcyBgbGlzdEl0ZW1WYWx1ZWAgaWYgb3JkZXJlZCwgYGxpc3RJdGVtTWFya2VyYCwgYW5kXG4gIC8vIGBsaXN0SXRlbVByZWZpeFdoaXRlc3BhY2VgICh1bmxlc3MgZm9sbG93ZWQgYnkgYSBsaW5lIGVuZGluZykuXG4gIGxpc3RJdGVtUHJlZml4OiAnbGlzdEl0ZW1QcmVmaXgnLFxuXG4gIC8vIFRoZSB3aGl0ZXNwYWNlIGFmdGVyIGEgbWFya2VyLlxuICBsaXN0SXRlbVByZWZpeFdoaXRlc3BhY2U6ICdsaXN0SXRlbVByZWZpeFdoaXRlc3BhY2UnLFxuXG4gIC8vIFRoZSBudW1lcmljYWwgdmFsdWUgb2YgYW4gb3JkZXJlZCBpdGVtLlxuICBsaXN0SXRlbVZhbHVlOiAnbGlzdEl0ZW1WYWx1ZScsXG5cbiAgLy8gSW50ZXJuYWwgdHlwZXMgdXNlZCBmb3Igc3VidG9rZW5pemVycywgY29tcGlsZWQgYXdheVxuICBjaHVua0RvY3VtZW50OiAnY2h1bmtEb2N1bWVudCcsXG4gIGNodW5rQ29udGVudDogJ2NodW5rQ29udGVudCcsXG4gIGNodW5rRmxvdzogJ2NodW5rRmxvdycsXG4gIGNodW5rVGV4dDogJ2NodW5rVGV4dCcsXG4gIGNodW5rU3RyaW5nOiAnY2h1bmtTdHJpbmcnXG59KVxuIl0sIm5hbWVzIjpbInR5cGVzIiwiZGF0YSIsIndoaXRlc3BhY2UiLCJsaW5lRW5kaW5nIiwibGluZUVuZGluZ0JsYW5rIiwibGluZVByZWZpeCIsImxpbmVTdWZmaXgiLCJhdHhIZWFkaW5nIiwiYXR4SGVhZGluZ1NlcXVlbmNlIiwiYXR4SGVhZGluZ1RleHQiLCJhdXRvbGluayIsImF1dG9saW5rRW1haWwiLCJhdXRvbGlua01hcmtlciIsImF1dG9saW5rUHJvdG9jb2wiLCJjaGFyYWN0ZXJFc2NhcGUiLCJjaGFyYWN0ZXJFc2NhcGVWYWx1ZSIsImNoYXJhY3RlclJlZmVyZW5jZSIsImNoYXJhY3RlclJlZmVyZW5jZU1hcmtlciIsImNoYXJhY3RlclJlZmVyZW5jZU1hcmtlck51bWVyaWMiLCJjaGFyYWN0ZXJSZWZlcmVuY2VNYXJrZXJIZXhhZGVjaW1hbCIsImNoYXJhY3RlclJlZmVyZW5jZVZhbHVlIiwiY29kZUZlbmNlZCIsImNvZGVGZW5jZWRGZW5jZSIsImNvZGVGZW5jZWRGZW5jZVNlcXVlbmNlIiwiY29kZUZlbmNlZEZlbmNlSW5mbyIsImNvZGVGZW5jZWRGZW5jZU1ldGEiLCJjb2RlRmxvd1ZhbHVlIiwiY29kZUluZGVudGVkIiwiY29kZVRleHQiLCJjb2RlVGV4dERhdGEiLCJjb2RlVGV4dFBhZGRpbmciLCJjb2RlVGV4dFNlcXVlbmNlIiwiY29udGVudCIsImRlZmluaXRpb24iLCJkZWZpbml0aW9uRGVzdGluYXRpb24iLCJkZWZpbml0aW9uRGVzdGluYXRpb25MaXRlcmFsIiwiZGVmaW5pdGlvbkRlc3RpbmF0aW9uTGl0ZXJhbE1hcmtlciIsImRlZmluaXRpb25EZXN0aW5hdGlvblJhdyIsImRlZmluaXRpb25EZXN0aW5hdGlvblN0cmluZyIsImRlZmluaXRpb25MYWJlbCIsImRlZmluaXRpb25MYWJlbE1hcmtlciIsImRlZmluaXRpb25MYWJlbFN0cmluZyIsImRlZmluaXRpb25NYXJrZXIiLCJkZWZpbml0aW9uVGl0bGUiLCJkZWZpbml0aW9uVGl0bGVNYXJrZXIiLCJkZWZpbml0aW9uVGl0bGVTdHJpbmciLCJlbXBoYXNpcyIsImVtcGhhc2lzU2VxdWVuY2UiLCJlbXBoYXNpc1RleHQiLCJlc2NhcGVNYXJrZXIiLCJoYXJkQnJlYWtFc2NhcGUiLCJoYXJkQnJlYWtUcmFpbGluZyIsImh0bWxGbG93IiwiaHRtbEZsb3dEYXRhIiwiaHRtbFRleHQiLCJodG1sVGV4dERhdGEiLCJpbWFnZSIsImxhYmVsIiwibGFiZWxUZXh0IiwibGFiZWxMaW5rIiwibGFiZWxJbWFnZSIsImxhYmVsTWFya2VyIiwibGFiZWxJbWFnZU1hcmtlciIsImxhYmVsRW5kIiwibGluayIsInBhcmFncmFwaCIsInJlZmVyZW5jZSIsInJlZmVyZW5jZU1hcmtlciIsInJlZmVyZW5jZVN0cmluZyIsInJlc291cmNlIiwicmVzb3VyY2VEZXN0aW5hdGlvbiIsInJlc291cmNlRGVzdGluYXRpb25MaXRlcmFsIiwicmVzb3VyY2VEZXN0aW5hdGlvbkxpdGVyYWxNYXJrZXIiLCJyZXNvdXJjZURlc3RpbmF0aW9uUmF3IiwicmVzb3VyY2VEZXN0aW5hdGlvblN0cmluZyIsInJlc291cmNlTWFya2VyIiwicmVzb3VyY2VUaXRsZSIsInJlc291cmNlVGl0bGVNYXJrZXIiLCJyZXNvdXJjZVRpdGxlU3RyaW5nIiwic2V0ZXh0SGVhZGluZyIsInNldGV4dEhlYWRpbmdUZXh0Iiwic2V0ZXh0SGVhZGluZ0xpbmUiLCJzZXRleHRIZWFkaW5nTGluZVNlcXVlbmNlIiwic3Ryb25nIiwic3Ryb25nU2VxdWVuY2UiLCJzdHJvbmdUZXh0IiwidGhlbWF0aWNCcmVhayIsInRoZW1hdGljQnJlYWtTZXF1ZW5jZSIsImJsb2NrUXVvdGUiLCJibG9ja1F1b3RlUHJlZml4IiwiYmxvY2tRdW90ZU1hcmtlciIsImJsb2NrUXVvdGVQcmVmaXhXaGl0ZXNwYWNlIiwibGlzdE9yZGVyZWQiLCJsaXN0VW5vcmRlcmVkIiwibGlzdEl0ZW1JbmRlbnQiLCJsaXN0SXRlbU1hcmtlciIsImxpc3RJdGVtUHJlZml4IiwibGlzdEl0ZW1QcmVmaXhXaGl0ZXNwYWNlIiwibGlzdEl0ZW1WYWx1ZSIsImNodW5rRG9jdW1lbnQiLCJjaHVua0NvbnRlbnQiLCJjaHVua0Zsb3ciLCJjaHVua1RleHQiLCJjaHVua1N0cmluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/values.js":
/*!**********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/values.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   values: () => (/* binding */ values)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */ const values = /** @type {const} */ {\n    ht: \"\t\",\n    lf: \"\\n\",\n    cr: \"\\r\",\n    space: \" \",\n    exclamationMark: \"!\",\n    quotationMark: '\"',\n    numberSign: \"#\",\n    dollarSign: \"$\",\n    percentSign: \"%\",\n    ampersand: \"&\",\n    apostrophe: \"'\",\n    leftParenthesis: \"(\",\n    rightParenthesis: \")\",\n    asterisk: \"*\",\n    plusSign: \"+\",\n    comma: \",\",\n    dash: \"-\",\n    dot: \".\",\n    slash: \"/\",\n    digit0: \"0\",\n    digit1: \"1\",\n    digit2: \"2\",\n    digit3: \"3\",\n    digit4: \"4\",\n    digit5: \"5\",\n    digit6: \"6\",\n    digit7: \"7\",\n    digit8: \"8\",\n    digit9: \"9\",\n    colon: \":\",\n    semicolon: \";\",\n    lessThan: \"<\",\n    equalsTo: \"=\",\n    greaterThan: \">\",\n    questionMark: \"?\",\n    atSign: \"@\",\n    uppercaseA: \"A\",\n    uppercaseB: \"B\",\n    uppercaseC: \"C\",\n    uppercaseD: \"D\",\n    uppercaseE: \"E\",\n    uppercaseF: \"F\",\n    uppercaseG: \"G\",\n    uppercaseH: \"H\",\n    uppercaseI: \"I\",\n    uppercaseJ: \"J\",\n    uppercaseK: \"K\",\n    uppercaseL: \"L\",\n    uppercaseM: \"M\",\n    uppercaseN: \"N\",\n    uppercaseO: \"O\",\n    uppercaseP: \"P\",\n    uppercaseQ: \"Q\",\n    uppercaseR: \"R\",\n    uppercaseS: \"S\",\n    uppercaseT: \"T\",\n    uppercaseU: \"U\",\n    uppercaseV: \"V\",\n    uppercaseW: \"W\",\n    uppercaseX: \"X\",\n    uppercaseY: \"Y\",\n    uppercaseZ: \"Z\",\n    leftSquareBracket: \"[\",\n    backslash: \"\\\\\",\n    rightSquareBracket: \"]\",\n    caret: \"^\",\n    underscore: \"_\",\n    graveAccent: \"`\",\n    lowercaseA: \"a\",\n    lowercaseB: \"b\",\n    lowercaseC: \"c\",\n    lowercaseD: \"d\",\n    lowercaseE: \"e\",\n    lowercaseF: \"f\",\n    lowercaseG: \"g\",\n    lowercaseH: \"h\",\n    lowercaseI: \"i\",\n    lowercaseJ: \"j\",\n    lowercaseK: \"k\",\n    lowercaseL: \"l\",\n    lowercaseM: \"m\",\n    lowercaseN: \"n\",\n    lowercaseO: \"o\",\n    lowercaseP: \"p\",\n    lowercaseQ: \"q\",\n    lowercaseR: \"r\",\n    lowercaseS: \"s\",\n    lowercaseT: \"t\",\n    lowercaseU: \"u\",\n    lowercaseV: \"v\",\n    lowercaseW: \"w\",\n    lowercaseX: \"x\",\n    lowercaseY: \"y\",\n    lowercaseZ: \"z\",\n    leftCurlyBrace: \"{\",\n    verticalBar: \"|\",\n    rightCurlyBrace: \"}\",\n    tilde: \"~\",\n    replacementCharacter: \"�\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLXV0aWwtc3ltYm9sL2xpYi92YWx1ZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7Ozs7O0NBT0MsR0FDTSxNQUFNQSxTQUFTLGtCQUFrQixHQUFJO0lBQzFDQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxPQUFPO0lBQ1BDLGlCQUFpQjtJQUNqQkMsZUFBZTtJQUNmQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsYUFBYTtJQUNiQyxXQUFXO0lBQ1hDLFlBQVk7SUFDWkMsaUJBQWlCO0lBQ2pCQyxrQkFBa0I7SUFDbEJDLFVBQVU7SUFDVkMsVUFBVTtJQUNWQyxPQUFPO0lBQ1BDLE1BQU07SUFDTkMsS0FBSztJQUNMQyxPQUFPO0lBQ1BDLFFBQVE7SUFDUkMsUUFBUTtJQUNSQyxRQUFRO0lBQ1JDLFFBQVE7SUFDUkMsUUFBUTtJQUNSQyxRQUFRO0lBQ1JDLFFBQVE7SUFDUkMsUUFBUTtJQUNSQyxRQUFRO0lBQ1JDLFFBQVE7SUFDUkMsT0FBTztJQUNQQyxXQUFXO0lBQ1hDLFVBQVU7SUFDVkMsVUFBVTtJQUNWQyxhQUFhO0lBQ2JDLGNBQWM7SUFDZEMsUUFBUTtJQUNSQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsbUJBQW1CO0lBQ25CQyxXQUFXO0lBQ1hDLG9CQUFvQjtJQUNwQkMsT0FBTztJQUNQQyxZQUFZO0lBQ1pDLGFBQWE7SUFDYkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxZQUFZO0lBQ1pDLGdCQUFnQjtJQUNoQkMsYUFBYTtJQUNiQyxpQkFBaUI7SUFDakJDLE9BQU87SUFDUEMsc0JBQXNCO0FBQ3hCLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9taWNyb21hcmstdXRpbC1zeW1ib2wvbGliL3ZhbHVlcy5qcz84OGM4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVGhpcyBtb2R1bGUgaXMgY29tcGlsZWQgYXdheSFcbiAqXG4gKiBXaGlsZSBtaWNyb21hcmsgd29ya3MgYmFzZWQgb24gY2hhcmFjdGVyIGNvZGVzLCB0aGlzIG1vZHVsZSBpbmNsdWRlcyB0aGVcbiAqIHN0cmluZyB2ZXJzaW9ucyBvZiDigJllbS5cbiAqIFRoZSBDMCBibG9jaywgZXhjZXB0IGZvciBMRiwgQ1IsIEhULCBhbmQgdy8gdGhlIHJlcGxhY2VtZW50IGNoYXJhY3RlciBhZGRlZCxcbiAqIGFyZSBhdmFpbGFibGUgaGVyZS5cbiAqL1xuZXhwb3J0IGNvbnN0IHZhbHVlcyA9IC8qKiBAdHlwZSB7Y29uc3R9ICovICh7XG4gIGh0OiAnXFx0JyxcbiAgbGY6ICdcXG4nLFxuICBjcjogJ1xccicsXG4gIHNwYWNlOiAnICcsXG4gIGV4Y2xhbWF0aW9uTWFyazogJyEnLFxuICBxdW90YXRpb25NYXJrOiAnXCInLFxuICBudW1iZXJTaWduOiAnIycsXG4gIGRvbGxhclNpZ246ICckJyxcbiAgcGVyY2VudFNpZ246ICclJyxcbiAgYW1wZXJzYW5kOiAnJicsXG4gIGFwb3N0cm9waGU6IFwiJ1wiLFxuICBsZWZ0UGFyZW50aGVzaXM6ICcoJyxcbiAgcmlnaHRQYXJlbnRoZXNpczogJyknLFxuICBhc3RlcmlzazogJyonLFxuICBwbHVzU2lnbjogJysnLFxuICBjb21tYTogJywnLFxuICBkYXNoOiAnLScsXG4gIGRvdDogJy4nLFxuICBzbGFzaDogJy8nLFxuICBkaWdpdDA6ICcwJyxcbiAgZGlnaXQxOiAnMScsXG4gIGRpZ2l0MjogJzInLFxuICBkaWdpdDM6ICczJyxcbiAgZGlnaXQ0OiAnNCcsXG4gIGRpZ2l0NTogJzUnLFxuICBkaWdpdDY6ICc2JyxcbiAgZGlnaXQ3OiAnNycsXG4gIGRpZ2l0ODogJzgnLFxuICBkaWdpdDk6ICc5JyxcbiAgY29sb246ICc6JyxcbiAgc2VtaWNvbG9uOiAnOycsXG4gIGxlc3NUaGFuOiAnPCcsXG4gIGVxdWFsc1RvOiAnPScsXG4gIGdyZWF0ZXJUaGFuOiAnPicsXG4gIHF1ZXN0aW9uTWFyazogJz8nLFxuICBhdFNpZ246ICdAJyxcbiAgdXBwZXJjYXNlQTogJ0EnLFxuICB1cHBlcmNhc2VCOiAnQicsXG4gIHVwcGVyY2FzZUM6ICdDJyxcbiAgdXBwZXJjYXNlRDogJ0QnLFxuICB1cHBlcmNhc2VFOiAnRScsXG4gIHVwcGVyY2FzZUY6ICdGJyxcbiAgdXBwZXJjYXNlRzogJ0cnLFxuICB1cHBlcmNhc2VIOiAnSCcsXG4gIHVwcGVyY2FzZUk6ICdJJyxcbiAgdXBwZXJjYXNlSjogJ0onLFxuICB1cHBlcmNhc2VLOiAnSycsXG4gIHVwcGVyY2FzZUw6ICdMJyxcbiAgdXBwZXJjYXNlTTogJ00nLFxuICB1cHBlcmNhc2VOOiAnTicsXG4gIHVwcGVyY2FzZU86ICdPJyxcbiAgdXBwZXJjYXNlUDogJ1AnLFxuICB1cHBlcmNhc2VROiAnUScsXG4gIHVwcGVyY2FzZVI6ICdSJyxcbiAgdXBwZXJjYXNlUzogJ1MnLFxuICB1cHBlcmNhc2VUOiAnVCcsXG4gIHVwcGVyY2FzZVU6ICdVJyxcbiAgdXBwZXJjYXNlVjogJ1YnLFxuICB1cHBlcmNhc2VXOiAnVycsXG4gIHVwcGVyY2FzZVg6ICdYJyxcbiAgdXBwZXJjYXNlWTogJ1knLFxuICB1cHBlcmNhc2VaOiAnWicsXG4gIGxlZnRTcXVhcmVCcmFja2V0OiAnWycsXG4gIGJhY2tzbGFzaDogJ1xcXFwnLFxuICByaWdodFNxdWFyZUJyYWNrZXQ6ICddJyxcbiAgY2FyZXQ6ICdeJyxcbiAgdW5kZXJzY29yZTogJ18nLFxuICBncmF2ZUFjY2VudDogJ2AnLFxuICBsb3dlcmNhc2VBOiAnYScsXG4gIGxvd2VyY2FzZUI6ICdiJyxcbiAgbG93ZXJjYXNlQzogJ2MnLFxuICBsb3dlcmNhc2VEOiAnZCcsXG4gIGxvd2VyY2FzZUU6ICdlJyxcbiAgbG93ZXJjYXNlRjogJ2YnLFxuICBsb3dlcmNhc2VHOiAnZycsXG4gIGxvd2VyY2FzZUg6ICdoJyxcbiAgbG93ZXJjYXNlSTogJ2knLFxuICBsb3dlcmNhc2VKOiAnaicsXG4gIGxvd2VyY2FzZUs6ICdrJyxcbiAgbG93ZXJjYXNlTDogJ2wnLFxuICBsb3dlcmNhc2VNOiAnbScsXG4gIGxvd2VyY2FzZU46ICduJyxcbiAgbG93ZXJjYXNlTzogJ28nLFxuICBsb3dlcmNhc2VQOiAncCcsXG4gIGxvd2VyY2FzZVE6ICdxJyxcbiAgbG93ZXJjYXNlUjogJ3InLFxuICBsb3dlcmNhc2VTOiAncycsXG4gIGxvd2VyY2FzZVQ6ICd0JyxcbiAgbG93ZXJjYXNlVTogJ3UnLFxuICBsb3dlcmNhc2VWOiAndicsXG4gIGxvd2VyY2FzZVc6ICd3JyxcbiAgbG93ZXJjYXNlWDogJ3gnLFxuICBsb3dlcmNhc2VZOiAneScsXG4gIGxvd2VyY2FzZVo6ICd6JyxcbiAgbGVmdEN1cmx5QnJhY2U6ICd7JyxcbiAgdmVydGljYWxCYXI6ICd8JyxcbiAgcmlnaHRDdXJseUJyYWNlOiAnfScsXG4gIHRpbGRlOiAnficsXG4gIHJlcGxhY2VtZW50Q2hhcmFjdGVyOiAn77+9J1xufSlcbiJdLCJuYW1lcyI6WyJ2YWx1ZXMiLCJodCIsImxmIiwiY3IiLCJzcGFjZSIsImV4Y2xhbWF0aW9uTWFyayIsInF1b3RhdGlvbk1hcmsiLCJudW1iZXJTaWduIiwiZG9sbGFyU2lnbiIsInBlcmNlbnRTaWduIiwiYW1wZXJzYW5kIiwiYXBvc3Ryb3BoZSIsImxlZnRQYXJlbnRoZXNpcyIsInJpZ2h0UGFyZW50aGVzaXMiLCJhc3RlcmlzayIsInBsdXNTaWduIiwiY29tbWEiLCJkYXNoIiwiZG90Iiwic2xhc2giLCJkaWdpdDAiLCJkaWdpdDEiLCJkaWdpdDIiLCJkaWdpdDMiLCJkaWdpdDQiLCJkaWdpdDUiLCJkaWdpdDYiLCJkaWdpdDciLCJkaWdpdDgiLCJkaWdpdDkiLCJjb2xvbiIsInNlbWljb2xvbiIsImxlc3NUaGFuIiwiZXF1YWxzVG8iLCJncmVhdGVyVGhhbiIsInF1ZXN0aW9uTWFyayIsImF0U2lnbiIsInVwcGVyY2FzZUEiLCJ1cHBlcmNhc2VCIiwidXBwZXJjYXNlQyIsInVwcGVyY2FzZUQiLCJ1cHBlcmNhc2VFIiwidXBwZXJjYXNlRiIsInVwcGVyY2FzZUciLCJ1cHBlcmNhc2VIIiwidXBwZXJjYXNlSSIsInVwcGVyY2FzZUoiLCJ1cHBlcmNhc2VLIiwidXBwZXJjYXNlTCIsInVwcGVyY2FzZU0iLCJ1cHBlcmNhc2VOIiwidXBwZXJjYXNlTyIsInVwcGVyY2FzZVAiLCJ1cHBlcmNhc2VRIiwidXBwZXJjYXNlUiIsInVwcGVyY2FzZVMiLCJ1cHBlcmNhc2VUIiwidXBwZXJjYXNlVSIsInVwcGVyY2FzZVYiLCJ1cHBlcmNhc2VXIiwidXBwZXJjYXNlWCIsInVwcGVyY2FzZVkiLCJ1cHBlcmNhc2VaIiwibGVmdFNxdWFyZUJyYWNrZXQiLCJiYWNrc2xhc2giLCJyaWdodFNxdWFyZUJyYWNrZXQiLCJjYXJldCIsInVuZGVyc2NvcmUiLCJncmF2ZUFjY2VudCIsImxvd2VyY2FzZUEiLCJsb3dlcmNhc2VCIiwibG93ZXJjYXNlQyIsImxvd2VyY2FzZUQiLCJsb3dlcmNhc2VFIiwibG93ZXJjYXNlRiIsImxvd2VyY2FzZUciLCJsb3dlcmNhc2VIIiwibG93ZXJjYXNlSSIsImxvd2VyY2FzZUoiLCJsb3dlcmNhc2VLIiwibG93ZXJjYXNlTCIsImxvd2VyY2FzZU0iLCJsb3dlcmNhc2VOIiwibG93ZXJjYXNlTyIsImxvd2VyY2FzZVAiLCJsb3dlcmNhc2VRIiwibG93ZXJjYXNlUiIsImxvd2VyY2FzZVMiLCJsb3dlcmNhc2VUIiwibG93ZXJjYXNlVSIsImxvd2VyY2FzZVYiLCJsb3dlcmNhc2VXIiwibG93ZXJjYXNlWCIsImxvd2VyY2FzZVkiLCJsb3dlcmNhc2VaIiwibGVmdEN1cmx5QnJhY2UiLCJ2ZXJ0aWNhbEJhciIsInJpZ2h0Q3VybHlCcmFjZSIsInRpbGRlIiwicmVwbGFjZW1lbnRDaGFyYWN0ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/values.js\n");

/***/ })

};
;