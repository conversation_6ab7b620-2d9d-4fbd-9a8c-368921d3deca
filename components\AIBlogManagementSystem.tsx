"use client";

import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { cn } from "@/lib/utils";
import { 
  FileText, 
  Users, 
  MessageSquare, 
  Database, 
  Settings, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Send,
  Globe,
  Tag,
  User,
  Zap,
  Search,
  Filter,
  MoreHorizontal,
  ChevronDown,
  Star,
  Calendar,
  BarChart3,
  TrendingUp,
  CheckCircle,
  Clock,
  AlertCircle,
  Save,
  Loader2,
  RefreshCw,
  BookOpen,
  Target,
  Layers,
  X
} from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

// 导入数据库服务
import {
  projectService,
  authorService,
  promptService,
  blogSeriesService,
  blogPostService,
  generationHistoryService,
  syncService
} from '@/lib/database';

// 导入类型
import type {
  Project,
  Author,
  Prompt,
  BlogSeries,
  BlogPost,
  BlogPostInsert,
  BlogPostUpdate,
  AuthorInsert,
  PromptInsert,
  BlogSeriesInsert,
  ProjectInsert
} from '@/lib/supabase';

// 表单状态接口
interface GenerateForm {
  keywords: string;
  title: string;
  language: string;
  authorId: string;
  promptId: string;
  seriesId: string;
  category: string;
  tags: string;
  autoSeo: boolean;
  customSeoTitle: string;
  customSeoDescription: string;
  wordCount: string;
}

interface AuthorForm {
  name: string;
  email: string;
  bio: string;
  avatarUrl: string;
}

interface PromptForm {
  name: string;
  content: string;
  category: string;
  language: string;
  variables: string;
}

interface SeriesForm {
  name: string;
  description: string;
}

interface ProjectForm {
  name: string;
  description: string;
  databaseUrl: string;
}

const AIBlogManagementSystem = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState('generate');
  const [selectedProject, setSelectedProject] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 预览状态
  const [previewContent, setPreviewContent] = useState<{
    title: string;
    content: string;
    seoTitle: string;
    seoDescription: string;
  } | null>(null);

  // 编辑状态
  const [isEditing, setIsEditing] = useState(false);
  const [editingContent, setEditingContent] = useState<{
    title: string;
    content: string;
    seoTitle: string;
    seoDescription: string;
  } | null>(null);

  // 数据状态
  const [projects, setProjects] = useState<Project[]>([]);
  const [authors, setAuthors] = useState<Author[]>([]);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [blogSeries, setBlogSeries] = useState<BlogSeries[]>([]);
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);

  // 表单状态
  const [generateForm, setGenerateForm] = useState<GenerateForm>({
    keywords: '',
    title: '',
    language: 'zh',
    authorId: '',
    promptId: '',
    seriesId: '',
    category: '',
    tags: '',
    autoSeo: true,
    customSeoTitle: '',
    customSeoDescription: '',
    wordCount: '1500'
  });

  const [authorForm, setAuthorForm] = useState<AuthorForm>({
    name: '',
    email: '',
    bio: '',
    avatarUrl: ''
  });

  const [promptForm, setPromptForm] = useState<PromptForm>({
    name: '',
    content: '',
    category: 'general',
    language: 'zh',
    variables: ''
  });

  const [seriesForm, setSeriesForm] = useState<SeriesForm>({
    name: '',
    description: ''
  });

  const [projectForm, setProjectForm] = useState<ProjectForm>({
    name: '',
    description: '',
    databaseUrl: ''
  });

  // 对话框状态
  const [showAuthorDialog, setShowAuthorDialog] = useState(false);
  const [showPromptDialog, setShowPromptDialog] = useState(false);
  const [showSeriesDialog, setShowSeriesDialog] = useState(false);
  const [showProjectDialog, setShowProjectDialog] = useState(false);
  const [showPostPreview, setShowPostPreview] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [previewPost, setPreviewPost] = useState<BlogPost | null>(null);

  // 搜索和过滤状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [authorFilter, setAuthorFilter] = useState('all');
  const [seriesFilter, setSeriesFilter] = useState('all');

  // 语言选项
  const languageOptions = [
    { value: 'zh', label: '中文' },
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Español' },
    { value: 'fr', label: 'Français' },
    { value: 'de', label: 'Deutsch' },
    { value: 'ja', label: '日本語' },
    { value: 'ko', label: '한국어' }
  ];

  // 提示词类别选项
  const promptCategories = [
    { value: 'general', label: '通用' },
    { value: 'technical', label: '技术' },
    { value: 'tutorial', label: '教程' },
    { value: 'review', label: '评测' },
    { value: 'news', label: '新闻' },
    { value: 'opinion', label: '观点' },
    { value: 'howto', label: '操作指南' },
    { value: 'listicle', label: '列表文章' }
  ];

  // 初始化数据
  useEffect(() => {
    loadProjects();
  }, []);

  useEffect(() => {
    if (selectedProject) {
      loadProjectData();
    }
  }, [selectedProject]);

  const loadProjects = async () => {
    try {
      setLoading(true);
      const projectsData = await projectService.getAll();
      setProjects(projectsData);
      
      if (projectsData.length > 0 && !selectedProject) {
        setSelectedProject(projectsData[0].id);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载项目失败');
    } finally {
      setLoading(false);
    }
  };

  const loadProjectData = async () => {
    if (!selectedProject) return;
    
    try {
      setLoading(true);
      const [authorsData, promptsData, seriesData, postsData] = await Promise.all([
        authorService.getByProject(selectedProject),
        promptService.getByProject(selectedProject),
        blogSeriesService.getByProject(selectedProject),
        blogPostService.getByProject(selectedProject)
      ]);

      setAuthors(authorsData);
      setPrompts(promptsData);
      setBlogSeries(seriesData);
      setBlogPosts(postsData);

      // 设置默认值
      if (authorsData.length > 0 && !generateForm.authorId) {
        setGenerateForm(prev => ({ ...prev, authorId: authorsData[0].id }));
      }
      if (promptsData.length > 0 && !generateForm.promptId) {
        setGenerateForm(prev => ({ ...prev, promptId: promptsData[0].id }));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // AI博文生成功能
  const generateBlogPost = async () => {
    const startTime = Date.now();
    if (!generateForm.keywords.trim()) {
      setError('请输入关键词');
      return;
    }

    if (!generateForm.authorId) {
      setError('请选择作者');
      return;
    }

    if (!generateForm.promptId) {
      setError('请选择提示词模板');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // 获取提示词内容
      const prompt = await promptService.getById(generateForm.promptId);
      if (!prompt) {
        throw new Error('找不到选择的提示词模板');
      }

      // 构建生成参数
      const seriesContext = generateForm.seriesId && generateForm.seriesId !== 'none'
        ? await blogSeriesService.getSeriesContext(generateForm.seriesId)
        : '';

      // 调用AI服务生成内容
      const generatedContent = await callQwenAPI({
        keywords: generateForm.keywords,
        title: generateForm.title,
        prompt: prompt.content,
        language: generateForm.language,
        seriesContext,
        wordCount: generateForm.wordCount
      });

      // 创建博文
      const newPost: BlogPostInsert = {
        project_id: selectedProject,
        author_id: generateForm.authorId,
        series_id: generateForm.seriesId && generateForm.seriesId !== 'none' ? generateForm.seriesId : null,
        prompt_id: generateForm.promptId,
        title: generatedContent.title,
        content: generatedContent.content,
        language: generateForm.language,
        seo_title: generateForm.autoSeo ? generatedContent.seoTitle : generateForm.customSeoTitle,
        seo_description: generateForm.autoSeo ? generatedContent.seoDescription : generateForm.customSeoDescription,
        seo_keywords: generateForm.keywords.split(',').map(k => k.trim()),
        category: generateForm.category || null,
        tags: generateForm.tags ? generateForm.tags.split(',').map(t => t.trim()) : [],
        status: 'draft',
        generation_params: {
          keywords: generateForm.keywords,
          inputTitle: generateForm.title,
          language: generateForm.language,
          autoSeo: generateForm.autoSeo,
          seriesContext: seriesContext ? 'included' : 'none'
        },
        series_context: seriesContext || null
      };

      const createdPost = await blogPostService.create(newPost);

      // 记录生成历史
      await generationHistoryService.create({
        post_id: createdPost.id,
        prompt_used: prompt.content,
        input_keywords: generateForm.keywords.split(',').map(k => k.trim()),
        input_title: generateForm.title || null,
        generation_params: {
          ...newPost.generation_params,
          usage: generatedContent.usage
        },
        ai_model: generatedContent.model || 'qwen-turbo',
        response_time: Date.now() - startTime,
        success: true
      });

      // 如果是系列文章，更新系列总结
      if (generateForm.seriesId && generateForm.seriesId !== 'none') {
        await updateSeriesSummary(generateForm.seriesId, createdPost);
      }

      // 设置预览内容
      setPreviewContent({
        title: generatedContent.title,
        content: generatedContent.content,
        seoTitle: generatedContent.seoTitle,
        seoDescription: generatedContent.seoDescription
      });

      // 刷新博文列表
      await loadProjectData();

      // 重置表单
      setGenerateForm(prev => ({
        ...prev,
        keywords: '',
        title: '',
        category: '',
        tags: '',
        customSeoTitle: '',
        customSeoDescription: ''
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : '生成博文失败');
    } finally {
      setLoading(false);
    }
  };

  // 调用Qwen API生成博文
  const callQwenAPI = async (params: {
    keywords: string;
    title: string;
    prompt: string;
    language: string;
    seriesContext: string;
    wordCount: string;
  }) => {
    try {
      const requestBody = JSON.stringify({
        keywords: params.keywords,
        title: params.title,
        prompt: params.prompt,
        language: params.language,
        seriesContext: params.seriesContext,
        wordCount: params.wordCount
      });

      const response = await fetch('/api/generate-blog', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
        },
        body: requestBody
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API调用失败: ${response.status}`);
      }

      const data = await response.json();
      
      return {
        title: data.title,
        content: data.content,
        seoTitle: data.seoTitle,
        seoDescription: data.seoDescription,
        usage: data.usage,
        model: data.model
      };
    } catch (error) {
      console.error('Qwen API调用失败:', error);
      throw error;
    }
  };

  // 更新系列总结
  const updateSeriesSummary = async (seriesId: string, newPost: BlogPost) => {
    try {
      const seriesPosts = await blogPostService.getBySeries(seriesId);
      const series = await blogSeriesService.getById(seriesId);
      
      if (!series) return;

      // 构建新的总结
      const summary = `系列包含${seriesPosts.length + 1}篇文章，最新文章《${newPost.title}》涵盖了${newPost.seo_keywords?.join(', ')}等内容。`;
      
      await blogSeriesService.updateSummary(seriesId, summary);
    } catch (err) {
      console.error('更新系列总结失败:', err);
    }
  };

  // 作者管理
  const handleCreateAuthor = async () => {
    if (!authorForm.name.trim()) {
      setError('请输入作者姓名');
      return;
    }

    try {
      setLoading(true);
      const newAuthor: AuthorInsert = {
        project_id: selectedProject,
        name: authorForm.name,
        email: authorForm.email || null,
        bio: authorForm.bio || null,
        avatar_url: authorForm.avatarUrl || null
      };

      if (editingItem) {
        await authorService.update(editingItem.id, newAuthor);
      } else {
        await authorService.create(newAuthor);
      }

      await loadProjectData();
      setShowAuthorDialog(false);
      setAuthorForm({ name: '', email: '', bio: '', avatarUrl: '' });
      setEditingItem(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存作者失败');
    } finally {
      setLoading(false);
    }
  };

  // 提示词管理
  const handleCreatePrompt = async () => {
    if (!promptForm.name.trim() || !promptForm.content.trim()) {
      setError('请输入提示词名称和内容');
      return;
    }

    try {
      setLoading(true);
      const newPrompt: PromptInsert = {
        project_id: selectedProject,
        name: promptForm.name,
        content: promptForm.content,
        category: promptForm.category,
        language: promptForm.language,
        variables: promptForm.variables ? JSON.parse(`{${promptForm.variables}}`) : null
      };

      if (editingItem) {
        await promptService.update(editingItem.id, newPrompt);
      } else {
        await promptService.create(newPrompt);
      }

      await loadProjectData();
      setShowPromptDialog(false);
      setPromptForm({ name: '', content: '', category: 'general', language: 'zh', variables: '' });
      setEditingItem(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存提示词失败');
    } finally {
      setLoading(false);
    }
  };

  // 项目管理
  const handleCreateProject = async () => {
    if (!projectForm.name.trim()) {
      setError('请输入项目名称');
      return;
    }

    try {
      setLoading(true);
      const newProject: ProjectInsert = {
        name: projectForm.name,
        description: projectForm.description || null,
        database_url: projectForm.databaseUrl || null
      };

      if (editingItem) {
        await projectService.update(editingItem.id, newProject);
      } else {
        await projectService.create(newProject);
      }

      await loadProjects();
      setShowProjectDialog(false);
      setProjectForm({ name: '', description: '', databaseUrl: '' });
      setEditingItem(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存项目失败');
    } finally {
      setLoading(false);
    }
  };

  // 系列管理
  const handleCreateSeries = async () => {
    if (!seriesForm.name.trim()) {
      setError('请输入系列名称');
      return;
    }

    try {
      setLoading(true);
      const newSeries: BlogSeriesInsert = {
        project_id: selectedProject,
        name: seriesForm.name,
        description: seriesForm.description || null
      };

      if (editingItem) {
        await blogSeriesService.update(editingItem.id, newSeries);
      } else {
        await blogSeriesService.create(newSeries);
      }

      await loadProjectData();
      setShowSeriesDialog(false);
      setSeriesForm({ name: '', description: '' });
      setEditingItem(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存系列失败');
    } finally {
      setLoading(false);
    }
  };

  // 博文管理
  const handlePublishPost = async (postId: string) => {
    try {
      setLoading(true);
      await blogPostService.publish(postId);
      await loadProjectData();
    } catch (err) {
      setError(err instanceof Error ? err.message : '发布博文失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePost = async (postId: string) => {
    if (!confirm('确定要删除这篇博文吗？')) return;

    try {
      setLoading(true);
      await blogPostService.delete(postId);
      await loadProjectData();
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除博文失败');
    } finally {
      setLoading(false);
    }
  };

  const handlePreviewPost = (post: BlogPost) => {
    setPreviewPost(post);
    setShowPostPreview(true);
  };

  // 编辑功能
  const handleEditPreview = () => {
    if (previewContent) {
      setEditingContent({ ...previewContent });
      setIsEditing(true);
    }
  };

  // 自动调整文本区域高度
  useEffect(() => {
    if (isEditing && editingContent) {
      // 延迟执行以确保DOM已更新
      setTimeout(() => {
        const textarea = document.getElementById('editContent') as HTMLTextAreaElement;
        if (textarea) {
          textarea.style.height = 'auto';
          textarea.style.height = Math.max(500, textarea.scrollHeight) + 'px';
        }
      }, 100);
    }
  }, [isEditing, editingContent]);

  const handleEditPost = (post: BlogPost) => {
    setEditingContent({
      title: post.title,
      content: post.content,
      seoTitle: post.seo_title || '',
      seoDescription: post.seo_description || ''
    });
    setIsEditing(true);
    setShowPostPreview(false);
  };

  const handleSaveEdit = async () => {
    if (!editingContent) return;

    try {
      setLoading(true);

      // 如果是从预览内容编辑，更新预览内容
      if (previewContent) {
        setPreviewContent({ ...editingContent });
      }

      // 如果是从博文管理编辑，更新数据库中的博文
      if (previewPost) {
        const updateData: BlogPostUpdate = {
          title: editingContent.title,
          content: editingContent.content,
          seo_title: editingContent.seoTitle,
          seo_description: editingContent.seoDescription
        };
        await blogPostService.update(previewPost.id, updateData);
        await loadProjectData();
      }

      setIsEditing(false);
      setEditingContent(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditingContent(null);
  };

  // 项目同步
  const handleSyncPost = async (postId: string, targetProjectId: string) => {
    try {
      setLoading(true);
      await syncService.syncBlogPost(selectedProject, targetProjectId, postId);
      alert('博文同步成功！');
    } catch (err) {
      setError(err instanceof Error ? err.message : '同步博文失败');
    } finally {
      setLoading(false);
    }
  };

  // 状态徽章组件
  const StatusBadge = ({ status }: { status: string }) => {
    const variants = {
      published: 'default',
      draft: 'secondary',
      scheduled: 'outline',
      archived: 'destructive'
    } as const;
    
    const labels = {
      published: '已发布',
      draft: '草稿',
      scheduled: '定时发布',
      archived: '已归档'
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    );
  };

  // 过滤博文
  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = !searchKeyword || 
      post.title.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      post.content.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      (post.seo_keywords && post.seo_keywords.some(k => k.toLowerCase().includes(searchKeyword.toLowerCase())));
    
    const matchesStatus = statusFilter === 'all' || post.status === statusFilter;
    const matchesAuthor = authorFilter === 'all' || post.author_id === authorFilter;
    const matchesSeries = seriesFilter === 'all' || 
      (seriesFilter === 'none' && !post.series_id) || 
      post.series_id === seriesFilter;
    
    return matchesSearch && matchesStatus && matchesAuthor && matchesSeries;
  });

  // 统计数据
  const stats = {
    totalPosts: blogPosts.length,
    publishedPosts: blogPosts.filter(p => p.status === 'published').length,
    draftPosts: blogPosts.filter(p => p.status === 'draft').length,
    totalAuthors: <AUTHORS>
    activePrompts: prompts.filter(p => p.is_active).length,
    totalSeries: blogSeries.length
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 错误提示 */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md mb-4 mx-6 mt-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            <span>{error}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setError(null)}
              className="ml-auto"
            >
              ✕
            </Button>
          </div>
        </div>
      )}

      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Zap className="h-8 w-8 text-primary" />
                <h1 className="text-2xl font-bold text-foreground">AI Blog Manager</h1>
              </div>
              <Badge variant="outline">v3.0</Badge>
            </div>
            
            <div className="flex items-center gap-4">
              <Select value={selectedProject} onValueChange={setSelectedProject}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="选择项目" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      <div className="flex items-center gap-2">
                        <div className={cn(
                          "w-2 h-2 rounded-full",
                          project.is_active ? "bg-green-500" : "bg-gray-400"
                        )} />
                        {project.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowProjectDialog(true)}
              >
                <Settings className="h-4 w-4 mr-2" />
                项目管理
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Stats Dashboard */}
      <div className="container mx-auto px-6 py-6">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">总博文</p>
                  <p className="text-2xl font-bold">{stats.totalPosts}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm text-muted-foreground">已发布</p>
                  <p className="text-2xl font-bold">{stats.publishedPosts}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="text-sm text-muted-foreground">草稿</p>
                  <p className="text-2xl font-bold">{stats.draftPosts}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-sm text-muted-foreground">作者</p>
                  <p className="text-2xl font-bold">{stats.totalAuthors}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-teal-500" />
                <div>
                  <p className="text-sm text-muted-foreground">活跃提示词</p>
                  <p className="text-2xl font-bold">{stats.activePrompts}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-indigo-500" />
                <div>
                  <p className="text-sm text-muted-foreground">系列</p>
                  <p className="text-2xl font-bold">{stats.totalSeries}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              博文生成
            </TabsTrigger>
            <TabsTrigger value="manage" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              博文管理
            </TabsTrigger>
            <TabsTrigger value="series" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              系列管理
            </TabsTrigger>
            <TabsTrigger value="prompts" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              提示词管理
            </TabsTrigger>
            <TabsTrigger value="authors" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              作者管理
            </TabsTrigger>
            <TabsTrigger value="projects" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              项目管理
            </TabsTrigger>
          </TabsList>

          {/* 博文生成页面 */}
          <TabsContent value="generate" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  AI博文生成
                </CardTitle>
                <CardDescription>
                  使用AI技术生成高质量SEO优化的博文内容
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="keywords">关键词 *</Label>
                      <Input
                        id="keywords"
                        placeholder="输入关键词，用逗号分隔"
                        value={generateForm.keywords}
                        onChange={(e) => setGenerateForm(prev => ({ ...prev, keywords: e.target.value }))}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="title">文章标题</Label>
                      <Input
                        id="title"
                        placeholder="留空将自动生成标题"
                        value={generateForm.title}
                        onChange={(e) => setGenerateForm(prev => ({ ...prev, title: e.target.value }))}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="language">语言</Label>
                      <Select value={generateForm.language} onValueChange={(value) => setGenerateForm(prev => ({ ...prev, language: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {languageOptions.map(lang => (
                            <SelectItem key={lang.value} value={lang.value}>
                              {lang.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="wordCount">文章字数</Label>
                      <Select value={generateForm.wordCount} onValueChange={(value) => setGenerateForm(prev => ({ ...prev, wordCount: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="500">短文 (~500字)</SelectItem>
                          <SelectItem value="800">简短 (~800字)</SelectItem>
                          <SelectItem value="1200">中等 (~1200字)</SelectItem>
                          <SelectItem value="1500">标准 (~1500字)</SelectItem>
                          <SelectItem value="2000">详细 (~2000字)</SelectItem>
                          <SelectItem value="3000">深度 (~3000字)</SelectItem>
                          <SelectItem value="5000">长文 (~5000字)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="author">作者 *</Label>
                      <Select value={generateForm.authorId} onValueChange={(value) => setGenerateForm(prev => ({ ...prev, authorId: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择作者" />
                        </SelectTrigger>
                        <SelectContent>
                          {authors.map(author => (
                            <SelectItem key={author.id} value={author.id}>
                              {author.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="prompt">提示词模板 *</Label>
                      <Select value={generateForm.promptId} onValueChange={(value) => setGenerateForm(prev => ({ ...prev, promptId: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择提示词模板" />
                        </SelectTrigger>
                        <SelectContent>
                          {prompts.map(prompt => (
                            <SelectItem key={prompt.id} value={prompt.id}>
                              {prompt.name} ({prompt.category})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="series">所属系列</Label>
                      <Select value={generateForm.seriesId} onValueChange={(value) => setGenerateForm(prev => ({ ...prev, seriesId: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择系列（可选）" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">不属于任何系列</SelectItem>
                          {blogSeries.map(series => (
                            <SelectItem key={series.id} value={series.id}>
                              {series.name} ({series.total_posts}篇)
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="category">分类</Label>
                      <Input
                        id="category"
                        placeholder="文章分类"
                        value={generateForm.category}
                        onChange={(e) => setGenerateForm(prev => ({ ...prev, category: e.target.value }))}
                      />
                    </div>

                    <div>
                      <Label htmlFor="tags">标签</Label>
                      <Input
                        id="tags"
                        placeholder="标签，用逗号分隔"
                        value={generateForm.tags}
                        onChange={(e) => setGenerateForm(prev => ({ ...prev, tags: e.target.value }))}
                      />
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="autoSeo"
                          checked={generateForm.autoSeo}
                          onChange={(e) => setGenerateForm(prev => ({ ...prev, autoSeo: e.target.checked }))}
                          className="rounded"
                        />
                        <Label htmlFor="autoSeo">自动生成SEO信息</Label>
                      </div>

                      {!generateForm.autoSeo && (
                        <>
                          <div>
                            <Label htmlFor="customSeoTitle">自定义SEO标题</Label>
                            <Input
                              id="customSeoTitle"
                              placeholder="SEO标题"
                              value={generateForm.customSeoTitle}
                              onChange={(e) => setGenerateForm(prev => ({ ...prev, customSeoTitle: e.target.value }))}
                            />
                          </div>
                          <div>
                            <Label htmlFor="customSeoDescription">自定义SEO描述</Label>
                            <Textarea
                              id="customSeoDescription"
                              placeholder="SEO描述"
                              value={generateForm.customSeoDescription}
                              onChange={(e) => setGenerateForm(prev => ({ ...prev, customSeoDescription: e.target.value }))}
                              rows={3}
                            />
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button 
                    onClick={generateBlogPost}
                    disabled={loading}
                    className="min-w-[120px]"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        生成中...
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        生成博文
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 博文预览 */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    博文预览
                  </CardTitle>
                  {previewContent && (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleEditPreview}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setPreviewContent(null)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
                <CardDescription>
                  {previewContent ? '生成的博文内容预览（Markdown渲染）' : '博文生成后将在此处显示预览'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {previewContent ? (
                  <>
                    {/* SEO信息 */}
                    <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                      <h4 className="font-semibold text-sm text-gray-700">SEO信息</h4>
                      <div>
                        <span className="text-xs text-gray-500">SEO标题:</span>
                        <p className="text-sm font-medium">{previewContent.seoTitle}</p>
                      </div>
                      <div>
                        <span className="text-xs text-gray-500">SEO描述:</span>
                        <p className="text-sm text-gray-600">{previewContent.seoDescription}</p>
                      </div>
                    </div>

                    {/* 博文内容 */}
                    <div className="border rounded-lg p-6 bg-white">
                      <div className="prose prose-sm max-w-none">
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm]}
                          rehypePlugins={[rehypeHighlight]}
                          components={{
                            h1: ({children}) => <h1 className="text-2xl font-bold mb-4 text-gray-900">{children}</h1>,
                            h2: ({children}) => <h2 className="text-xl font-semibold mb-3 mt-6 text-gray-800">{children}</h2>,
                            h3: ({children}) => <h3 className="text-lg font-medium mb-2 mt-4 text-gray-700">{children}</h3>,
                            p: ({children}) => <p className="mb-3 text-gray-600 leading-relaxed">{children}</p>,
                            ul: ({children}) => <ul className="mb-3 ml-4 space-y-1">{children}</ul>,
                            ol: ({children}) => <ol className="mb-3 ml-4 space-y-1">{children}</ol>,
                            li: ({children}) => <li className="text-gray-600">{children}</li>,
                            strong: ({children}) => <strong className="font-semibold text-gray-800">{children}</strong>,
                            code: ({children}) => <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                            pre: ({children}) => <pre className="bg-gray-100 p-3 rounded-lg overflow-x-auto mb-3">{children}</pre>,
                            blockquote: ({children}) => <blockquote className="border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-3">{children}</blockquote>
                          }}
                        >
                          {previewContent.content}
                        </ReactMarkdown>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="border rounded-lg p-12 bg-gray-50 text-center">
                    <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 text-lg mb-2">暂无预览内容</p>
                    <p className="text-gray-400 text-sm">请先生成博文以查看预览效果</p>
                  </div>
                )}
              </CardContent>
            </Card>


          </TabsContent>

          {/* 博文管理页面 */}
          <TabsContent value="manage" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  博文管理
                </CardTitle>
                <CardDescription>
                  管理、编辑和发布您的博文内容
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* 搜索和过滤 */}
                <div className="flex flex-col md:flex-row gap-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="搜索博文..."
                        value={searchKeyword}
                        onChange={(e) => setSearchKeyword(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有状态</SelectItem>
                      <SelectItem value="draft">草稿</SelectItem>
                      <SelectItem value="published">已发布</SelectItem>
                      <SelectItem value="scheduled">定时发布</SelectItem>
                      <SelectItem value="archived">已归档</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={authorFilter} onValueChange={setAuthorFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有作者</SelectItem>
                      {authors.map(author => (
                        <SelectItem key={author.id} value={author.id}>
                          {author.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={seriesFilter} onValueChange={setSeriesFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有系列</SelectItem>
                      <SelectItem value="none">无系列</SelectItem>
                      {blogSeries.map(series => (
                        <SelectItem key={series.id} value={series.id}>
                          {series.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 博文列表 */}
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>标题</TableHead>
                        <TableHead>作者</TableHead>
                        <TableHead>系列</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPosts.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                            {searchKeyword || statusFilter !== 'all' || authorFilter !== 'all' || seriesFilter !== 'all' 
                              ? '没有找到匹配的博文' 
                              : '还没有博文，去生成第一篇吧！'
                            }
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredPosts.map(post => {
                          const author = authors.find(a => a.id === post.author_id);
                          const series = blogSeries.find(s => s.id === post.series_id);
                          
                          return (
                            <TableRow key={post.id}>
                              <TableCell>
                                <div>
                                  <div
                                    className="font-medium cursor-pointer hover:text-blue-600 hover:underline transition-colors"
                                    onClick={() => handlePreviewPost(post)}
                                  >
                                    {post.title}
                                  </div>
                                  {post.seo_keywords && post.seo_keywords.length > 0 && (
                                    <div className="flex flex-wrap gap-1 mt-1">
                                      {post.seo_keywords.slice(0, 3).map(keyword => (
                                        <Badge key={keyword} variant="outline" className="text-xs">
                                          {keyword}
                                        </Badge>
                                      ))}
                                      {post.seo_keywords.length > 3 && (
                                        <Badge variant="outline" className="text-xs">
                                          +{post.seo_keywords.length - 3}
                                        </Badge>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>{author?.name || '未知'}</TableCell>
                              <TableCell>
                                {series ? (
                                  <Badge variant="secondary" className="text-xs">
                                    {series.name}
                                  </Badge>
                                ) : '无'}
                              </TableCell>
                              <TableCell>
                                <StatusBadge status={post.status} />
                              </TableCell>
                              <TableCell>
                                {new Date(post.created_at).toLocaleDateString('zh-CN')}
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handlePreviewPost(post)}
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>

                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEditPost(post)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>

                                  {post.status === 'draft' && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handlePublishPost(post.id)}
                                    >
                                      <Send className="h-4 w-4" />
                                    </Button>
                                  )}
                                  
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeletePost(post.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 系列管理页面 */}
          <TabsContent value="series" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="h-5 w-5" />
                      系列管理
                    </CardTitle>
                    <CardDescription>
                      管理博文系列，组织相关文章
                    </CardDescription>
                  </div>
                  <Button onClick={() => setShowSeriesDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    新建系列
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6">
                  {blogSeries.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      还没有博文系列，创建第一个吧！
                    </div>
                  ) : (
                    blogSeries.map(series => {
                      const seriesPosts = blogPosts.filter(post => post.series_id === series.id);
                      const publishedPosts = seriesPosts.filter(post => post.status === 'published');
                      const draftPosts = seriesPosts.filter(post => post.status === 'draft');
                      
                      return (
                        <Card key={series.id} className="border-l-4 border-l-primary">
                          <CardContent className="p-6">
                            <div className="flex items-start justify-between mb-4">
                              <div className="flex-1">
                                <div className="flex items-center gap-3 mb-2">
                                  <h3 className="text-xl font-semibold">{series.name}</h3>
                                  <Badge variant="secondary" className="text-sm">
                                    {series.total_posts} 篇文章
                                  </Badge>
                                  {publishedPosts.length > 0 && (
                                    <Badge variant="default" className="text-sm">
                                      {publishedPosts.length} 已发布
                                    </Badge>
                                  )}
                                  {draftPosts.length > 0 && (
                                    <Badge variant="outline" className="text-sm">
                                      {draftPosts.length} 草稿
                                    </Badge>
                                  )}
                                </div>
                                {series.description && (
                                  <p className="text-muted-foreground mb-3">
                                    {series.description}
                                  </p>
                                )}
                                <p className="text-sm text-muted-foreground">
                                  创建时间: {new Date(series.created_at).toLocaleDateString('zh-CN')}
                                </p>
                              </div>
                              <div className="flex items-center gap-2 ml-4">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setEditingItem(series);
                                    setSeriesForm({
                                      name: series.name,
                                      description: series.description || ''
                                    });
                                    setShowSeriesDialog(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={async () => {
                                    if (series.total_posts > 0) {
                                      alert('不能删除包含文章的系列，请先移除所有文章');
                                      return;
                                    }
                                    if (confirm('确定要删除这个系列吗？')) {
                                      await blogSeriesService.delete(series.id);
                                      await loadProjectData();
                                    }
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>

                            {/* 系列文章列表 */}
                            {seriesPosts.length > 0 && (
                              <div className="mt-4 pt-4 border-t border-border">
                                <h4 className="font-medium mb-3 flex items-center gap-2">
                                  <Layers className="h-4 w-4" />
                                  系列文章
                                </h4>
                                <div className="space-y-2">
                                  {seriesPosts
                                    .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
                                    .map((post, index) => {
                                      const author = authors.find(a => a.id === post.author_id);
                                      
                                      return (
                                        <div 
                                          key={post.id} 
                                          className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                                        >
                                          <div className="flex items-center gap-3 flex-1">
                                            <Badge variant="outline" className="text-xs">
                                              #{index + 1}
                                            </Badge>
                                            <div className="flex-1">
                                              <h5
                                                className="font-medium text-sm cursor-pointer hover:text-blue-600 hover:underline transition-colors"
                                                onClick={() => handlePreviewPost(post)}
                                              >
                                                {post.title}
                                              </h5>
                                              <div className="flex items-center gap-2 mt-1">
                                                <span className="text-xs text-muted-foreground">
                                                  作者: {author?.name || '未知'}
                                                </span>
                                                <span className="text-xs text-muted-foreground">•</span>
                                                <span className="text-xs text-muted-foreground">
                                                  {new Date(post.created_at).toLocaleDateString('zh-CN')}
                                                </span>
                                              </div>
                                            </div>
                                          </div>
                                          <div className="flex items-center gap-2">
                                            <StatusBadge status={post.status} />
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => handlePreviewPost(post)}
                                            >
                                              <Eye className="h-4 w-4" />
                                            </Button>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => handleEditPost(post)}
                                            >
                                              <Edit className="h-4 w-4" />
                                            </Button>
                                            {post.status === 'draft' && (
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handlePublishPost(post.id)}
                                              >
                                                <Send className="h-4 w-4" />
                                              </Button>
                                            )}
                                          </div>
                                        </div>
                                      );
                                    })}
                                </div>
                              </div>
                            )}

                            {/* 系列总结 */}
                            {series.summary && (
                              <div className="mt-4 pt-4 border-t border-border">
                                <h4 className="font-medium mb-2 flex items-center gap-2">
                                  <BarChart3 className="h-4 w-4" />
                                  系列总结
                                </h4>
                                <p className="text-sm text-muted-foreground bg-muted/30 p-3 rounded-lg">
                                  {series.summary}
                                </p>
                              </div>
                            )}

                            {/* 快速操作 */}
                            <div className="mt-4 pt-4 border-t border-border">
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setGenerateForm(prev => ({ ...prev, seriesId: series.id }));
                                    setActiveTab('generate');
                                  }}
                                >
                                  <Plus className="h-4 w-4 mr-1" />
                                  添加文章
                                </Button>
                                
                                {seriesPosts.length > 0 && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setSeriesFilter(series.id);
                                      setActiveTab('manage');
                                    }}
                                  >
                                    <FileText className="h-4 w-4 mr-1" />
                                    查看文章
                                  </Button>
                                )}
                                
                                {publishedPosts.length >= 2 && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={async () => {
                                      try {
                                        const posts = await blogPostService.getBySeries(series.id);
                                        const summary = `本系列共包含 ${posts.length} 篇文章，涵盖了${posts.map(p => p.seo_keywords?.slice(0, 2).join(', ')).filter(Boolean).join('、')}等主题。系列从${posts[0]?.title}开始，逐步深入探讨相关概念和实践应用。`;
                                        await blogSeriesService.updateSummary(series.id, summary);
                                        await loadProjectData();
                                        alert('系列总结更新成功！');
                                      } catch (err) {
                                        setError(err instanceof Error ? err.message : '更新系列总结失败');
                                      }
                                    }}
                                  >
                                    <RefreshCw className="h-4 w-4 mr-1" />
                                    更新总结
                                  </Button>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 提示词管理页面 */}
          <TabsContent value="prompts" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <MessageSquare className="h-5 w-5" />
                      提示词管理
                    </CardTitle>
                    <CardDescription>
                      管理AI生成博文的提示词模板
                    </CardDescription>
                  </div>
                  <Button onClick={() => setShowPromptDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    新建提示词
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  {prompts.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      还没有提示词模板，创建第一个吧！
                    </div>
                  ) : (
                    prompts.map(prompt => (
                      <Card key={prompt.id}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h3 className="font-medium">{prompt.name}</h3>
                                <Badge variant="outline">{prompt.category}</Badge>
                                <Badge variant="secondary">{prompt.language}</Badge>
                                {prompt.is_active && (
                                  <Badge variant="default" className="text-xs">
                                    活跃
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground line-clamp-2">
                                {prompt.content}
                              </p>
                            </div>
                            <div className="flex items-center gap-2 ml-4">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setEditingItem(prompt);
                                  setPromptForm({
                                    name: prompt.name,
                                    content: prompt.content,
                                    category: prompt.category,
                                    language: prompt.language,
                                    variables: prompt.variables ? JSON.stringify(prompt.variables).slice(1, -1) : ''
                                  });
                                  setShowPromptDialog(true);
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={async () => {
                                  if (confirm('确定要删除这个提示词吗？')) {
                                    await promptService.delete(prompt.id);
                                    await loadProjectData();
                                  }
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 作者管理页面 */}
          <TabsContent value="authors" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      作者管理
                    </CardTitle>
                    <CardDescription>
                      管理博文作者信息
                    </CardDescription>
                  </div>
                  <Button onClick={() => setShowAuthorDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    新建作者
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  {authors.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      还没有作者信息，创建第一个吧！
                    </div>
                  ) : (
                    authors.map(author => (
                      <Card key={author.id}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-4">
                              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                                {author.avatar_url ? (
                                  <img 
                                    src={author.avatar_url} 
                                    alt={author.name}
                                    className="w-12 h-12 rounded-full object-cover"
                                  />
                                ) : (
                                  <User className="h-6 w-6 text-primary" />
                                )}
                              </div>
                              <div>
                                <h3 className="font-medium">{author.name}</h3>
                                {author.email && (
                                  <p className="text-sm text-muted-foreground">{author.email}</p>
                                )}
                                {author.bio && (
                                  <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                    {author.bio}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setEditingItem(author);
                                  setAuthorForm({
                                    name: author.name,
                                    email: author.email || '',
                                    bio: author.bio || '',
                                    avatarUrl: author.avatar_url || ''
                                  });
                                  setShowAuthorDialog(true);
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={async () => {
                                  if (confirm('确定要删除这个作者吗？')) {
                                    await authorService.delete(author.id);
                                    await loadProjectData();
                                  }
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 项目管理页面 */}
          <TabsContent value="projects" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Database className="h-5 w-5" />
                      项目管理
                    </CardTitle>
                    <CardDescription>
                      管理多个博客项目和数据同步
                    </CardDescription>
                  </div>
                  <Button onClick={() => setShowProjectDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    新建项目
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  {projects.map(project => (
                    <Card key={project.id}>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-medium">{project.name}</h3>
                              <div className={cn(
                                "w-2 h-2 rounded-full",
                                project.is_active ? "bg-green-500" : "bg-gray-400"
                              )} />
                              {selectedProject === project.id && (
                                <Badge variant="default" className="text-xs">
                                  当前项目
                                </Badge>
                              )}
                            </div>
                            {project.description && (
                              <p className="text-sm text-muted-foreground mb-2">
                                {project.description}
                              </p>
                            )}
                            <p className="text-xs text-muted-foreground">
                              创建时间: {new Date(project.created_at).toLocaleDateString('zh-CN')}
                            </p>
                          </div>
                          <div className="flex items-center gap-2 ml-4">
                            {selectedProject !== project.id && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedProject(project.id)}
                              >
                                <Target className="h-4 w-4 mr-1" />
                                切换
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setEditingItem(project);
                                setProjectForm({
                                  name: project.name,
                                  description: project.description || '',
                                  databaseUrl: project.database_url || ''
                                });
                                setShowProjectDialog(true);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            {projects.length > 1 && selectedProject !== project.id && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={async () => {
                                  if (confirm('确定要删除这个项目吗？删除后将无法恢复，且所有关联数据都将被删除。')) {
                                    try {
                                      setLoading(true);
                                      await projectService.delete(project.id);
                                      await loadProjects();
                                    } catch (err) {
                                      setError(err instanceof Error ? err.message : '删除项目失败');
                                    } finally {
                                      setLoading(false);
                                    }
                                  }
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                        
                        {/* 项目同步区域 */}
                        {selectedProject !== project.id && (
                          <div className="mt-4 pt-4 border-t border-border">
                            <h4 className="text-sm font-medium mb-2">数据同步</h4>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={async () => {
                                  const postIds = blogPosts.filter(p => p.status === 'published').map(p => p.id);
                                  if (postIds.length === 0) {
                                    alert('没有已发布的博文可以同步');
                                    return;
                                  }
                                  
                                  for (const postId of postIds) {
                                    try {
                                      await handleSyncPost(postId, project.id);
                                    } catch (err) {
                                      console.error('同步博文失败:', err);
                                    }
                                  }
                                }}
                              >
                                <RefreshCw className="h-4 w-4 mr-1" />
                                同步博文
                              </Button>
                              
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={async () => {
                                  for (const author of authors) {
                                    try {
                                      await syncService.syncAuthor(selectedProject, project.id, author.id);
                                    } catch (err) {
                                      console.error('同步作者失败:', err);
                                    }
                                  }
                                  alert('作者信息同步完成！');
                                }}
                              >
                                <Users className="h-4 w-4 mr-1" />
                                同步作者
                              </Button>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* 对话框 - 作者 */}
      <Dialog open={showAuthorDialog} onOpenChange={setShowAuthorDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingItem ? '编辑作者' : '新建作者'}
            </DialogTitle>
            <DialogDescription>
              填写作者的基本信息
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="authorName">姓名 *</Label>
              <Input
                id="authorName"
                value={authorForm.name}
                onChange={(e) => setAuthorForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="输入作者姓名"
              />
            </div>
            <div>
              <Label htmlFor="authorEmail">邮箱</Label>
              <Input
                id="authorEmail"
                type="email"
                value={authorForm.email}
                onChange={(e) => setAuthorForm(prev => ({ ...prev, email: e.target.value }))}
                placeholder="输入邮箱地址"
              />
            </div>
            <div>
              <Label htmlFor="authorBio">简介</Label>
              <Textarea
                id="authorBio"
                value={authorForm.bio}
                onChange={(e) => setAuthorForm(prev => ({ ...prev, bio: e.target.value }))}
                placeholder="输入作者简介"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="authorAvatar">头像URL</Label>
              <Input
                id="authorAvatar"
                value={authorForm.avatarUrl}
                onChange={(e) => setAuthorForm(prev => ({ ...prev, avatarUrl: e.target.value }))}
                placeholder="输入头像图片URL"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowAuthorDialog(false);
              setAuthorForm({ name: '', email: '', bio: '', avatarUrl: '' });
              setEditingItem(null);
            }}>
              取消
            </Button>
            <Button onClick={handleCreateAuthor} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 对话框 - 提示词 */}
      <Dialog open={showPromptDialog} onOpenChange={setShowPromptDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>
              {editingItem ? '编辑提示词' : '新建提示词'}
            </DialogTitle>
            <DialogDescription>
              创建或编辑AI生成博文的提示词模板
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="promptName">名称 *</Label>
                <Input
                  id="promptName"
                  value={promptForm.name}
                  onChange={(e) => setPromptForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="输入提示词名称"
                />
              </div>
              <div>
                <Label htmlFor="promptCategory">类别</Label>
                <Select value={promptForm.category} onValueChange={(value) => setPromptForm(prev => ({ ...prev, category: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {promptCategories.map(cat => (
                      <SelectItem key={cat.value} value={cat.value}>
                        {cat.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="promptLanguage">语言</Label>
                <Select value={promptForm.language} onValueChange={(value) => setPromptForm(prev => ({ ...prev, language: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {languageOptions.map(lang => (
                      <SelectItem key={lang.value} value={lang.value}>
                        {lang.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="promptVariables">变量 (JSON格式)</Label>
                <Input
                  id="promptVariables"
                  value={promptForm.variables}
                  onChange={(e) => setPromptForm(prev => ({ ...prev, variables: e.target.value }))}
                  placeholder='"topic": "主题", "style": "风格"'
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="promptContent">提示词内容 *</Label>
              <Textarea
                id="promptContent"
                value={promptForm.content}
                onChange={(e) => setPromptForm(prev => ({ ...prev, content: e.target.value }))}
                placeholder="输入提示词内容，可以使用 {变量名} 作为占位符"
                rows={8}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowPromptDialog(false);
              setPromptForm({ name: '', content: '', category: 'general', language: 'zh', variables: '' });
              setEditingItem(null);
            }}>
              取消
            </Button>
            <Button onClick={handleCreatePrompt} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 对话框 - 系列 */}
      <Dialog open={showSeriesDialog} onOpenChange={setShowSeriesDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingItem ? '编辑系列' : '新建系列'}
            </DialogTitle>
            <DialogDescription>
              创建或编辑博文系列信息
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="seriesName">系列名称 *</Label>
              <Input
                id="seriesName"
                value={seriesForm.name}
                onChange={(e) => setSeriesForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="输入系列名称"
              />
            </div>
            <div>
              <Label htmlFor="seriesDescription">系列描述</Label>
              <Textarea
                id="seriesDescription"
                value={seriesForm.description}
                onChange={(e) => setSeriesForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="输入系列描述"
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowSeriesDialog(false);
              setSeriesForm({ name: '', description: '' });
              setEditingItem(null);
            }}>
              取消
            </Button>
            <Button onClick={handleCreateSeries} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 对话框 - 项目 */}
      <Dialog open={showProjectDialog} onOpenChange={setShowProjectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingItem ? '编辑项目' : '新建项目'}
            </DialogTitle>
            <DialogDescription>
              创建或编辑博客项目信息
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="projectName">项目名称 *</Label>
              <Input
                id="projectName"
                value={projectForm.name}
                onChange={(e) => setProjectForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="输入项目名称"
              />
            </div>
            <div>
              <Label htmlFor="projectDescription">项目描述</Label>
              <Textarea
                id="projectDescription"
                value={projectForm.description}
                onChange={(e) => setProjectForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="输入项目描述"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="projectDatabaseUrl">数据库URL</Label>
              <Input
                id="projectDatabaseUrl"
                value={projectForm.databaseUrl}
                onChange={(e) => setProjectForm(prev => ({ ...prev, databaseUrl: e.target.value }))}
                placeholder="外部数据库连接URL（可选）"
              />
              <p className="text-xs text-muted-foreground mt-1">
                用于连接外部项目数据库进行数据同步
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowProjectDialog(false);
              setProjectForm({ name: '', description: '', databaseUrl: '' });
              setEditingItem(null);
            }}>
              取消
            </Button>
            <Button onClick={handleCreateProject} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 对话框 - 博文预览 */}
      <Dialog open={showPostPreview} onOpenChange={setShowPostPreview}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                博文预览
              </DialogTitle>
              {previewPost && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditPost(previewPost)}
                >
                  <Edit className="h-4 w-4 mr-1" />
                  编辑
                </Button>
              )}
            </div>
          </DialogHeader>
          {previewPost && (
            <div className="space-y-6">
              {/* 博文信息 */}
              <div className="border-b pb-4">
                <h2 className="text-3xl font-bold mb-3">{previewPost.title}</h2>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>作者: {authors.find(a => a.id === previewPost.author_id)?.name}</span>
                  <span>创建时间: {new Date(previewPost.created_at).toLocaleDateString('zh-CN')}</span>
                  <StatusBadge status={previewPost.status} />
                </div>
              </div>

              {/* SEO信息 */}
              {previewPost.seo_title && (
                <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                  <h3 className="font-semibold text-sm text-gray-700">SEO信息</h3>
                  <div>
                    <span className="text-xs text-gray-500">SEO标题:</span>
                    <p className="text-sm font-medium">{previewPost.seo_title}</p>
                  </div>
                  {previewPost.seo_description && (
                    <div>
                      <span className="text-xs text-gray-500">SEO描述:</span>
                      <p className="text-sm text-gray-600">{previewPost.seo_description}</p>
                    </div>
                  )}
                  {previewPost.seo_keywords && previewPost.seo_keywords.length > 0 && (
                    <div>
                      <span className="text-xs text-gray-500">关键词:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {previewPost.seo_keywords.map(keyword => (
                          <Badge key={keyword} variant="outline" className="text-xs">
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 博文内容 */}
              <div className="border rounded-lg p-6 bg-white">
                <div className="prose prose-lg max-w-none">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeHighlight]}
                    components={{
                      h1: ({children}) => <h1 className="text-3xl font-bold mb-6 text-gray-900 border-b pb-2">{children}</h1>,
                      h2: ({children}) => <h2 className="text-2xl font-semibold mb-4 mt-8 text-gray-800">{children}</h2>,
                      h3: ({children}) => <h3 className="text-xl font-medium mb-3 mt-6 text-gray-700">{children}</h3>,
                      h4: ({children}) => <h4 className="text-lg font-medium mb-2 mt-4 text-gray-700">{children}</h4>,
                      p: ({children}) => <p className="mb-4 text-gray-600 leading-relaxed text-base">{children}</p>,
                      ul: ({children}) => <ul className="mb-4 ml-6 space-y-2">{children}</ul>,
                      ol: ({children}) => <ol className="mb-4 ml-6 space-y-2">{children}</ol>,
                      li: ({children}) => <li className="text-gray-600">{children}</li>,
                      strong: ({children}) => <strong className="font-semibold text-gray-800">{children}</strong>,
                      em: ({children}) => <em className="italic text-gray-700">{children}</em>,
                      code: ({children}) => <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono text-gray-800">{children}</code>,
                      pre: ({children}) => <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto mb-4 border">{children}</pre>,
                      blockquote: ({children}) => <blockquote className="border-l-4 border-blue-300 pl-4 italic text-gray-600 mb-4 bg-blue-50 py-2">{children}</blockquote>,
                      a: ({children, href}) => <a href={href} className="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer">{children}</a>,
                      table: ({children}) => <table className="min-w-full border-collapse border border-gray-300 mb-4">{children}</table>,
                      th: ({children}) => <th className="border border-gray-300 px-4 py-2 bg-gray-100 font-semibold text-left">{children}</th>,
                      td: ({children}) => <td className="border border-gray-300 px-4 py-2">{children}</td>,
                      hr: () => <hr className="my-8 border-gray-300" />
                    }}
                  >
                    {previewPost.content}
                  </ReactMarkdown>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPostPreview(false)}>
              关闭
            </Button>
            {previewPost && previewPost.status === 'draft' && (
              <Button onClick={() => {
                handlePublishPost(previewPost.id);
                setShowPostPreview(false);
              }}>
                <Send className="h-4 w-4 mr-2" />
                发布博文
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 对话框 - 编辑博文 */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5" />
              编辑博文
            </DialogTitle>
          </DialogHeader>
          {editingContent && (
            <div className="space-y-4">
              {/* 标题编辑 */}
              <div>
                <Label htmlFor="editTitle">博文标题</Label>
                <Input
                  id="editTitle"
                  value={editingContent.title}
                  onChange={(e) => setEditingContent(prev => prev ? { ...prev, title: e.target.value } : null)}
                  placeholder="输入博文标题"
                />
              </div>

              {/* SEO信息编辑 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="editSeoTitle">SEO标题</Label>
                  <Input
                    id="editSeoTitle"
                    value={editingContent.seoTitle}
                    onChange={(e) => setEditingContent(prev => prev ? { ...prev, seoTitle: e.target.value } : null)}
                    placeholder="SEO标题"
                  />
                </div>
                <div>
                  <Label htmlFor="editSeoDescription">SEO描述</Label>
                  <Input
                    id="editSeoDescription"
                    value={editingContent.seoDescription}
                    onChange={(e) => setEditingContent(prev => prev ? { ...prev, seoDescription: e.target.value } : null)}
                    placeholder="SEO描述"
                  />
                </div>
              </div>

              {/* 内容编辑和实时预览 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* 内容编辑 */}
                <div className="space-y-2">
                  <Label htmlFor="editContent">博文内容 (Markdown)</Label>
                  <Textarea
                    id="editContent"
                    value={editingContent.content}
                    onChange={(e) => setEditingContent(prev => prev ? { ...prev, content: e.target.value } : null)}
                    placeholder="输入博文内容，支持Markdown格式"
                    className="w-full font-mono text-sm resize-none border rounded-lg p-3"
                    style={{
                      height: 'auto',
                      minHeight: '500px',
                      maxHeight: 'none',
                      overflow: 'hidden'
                    }}
                    onInput={(e) => {
                      const target = e.target as HTMLTextAreaElement;
                      target.style.height = 'auto';
                      target.style.height = Math.max(500, target.scrollHeight) + 'px';
                    }}
                  />
                </div>

                {/* 实时预览 */}
                <div className="space-y-2">
                  <Label>实时预览</Label>
                  <div className="border rounded-lg p-4 bg-white min-h-[500px]">
                    <div className="prose prose-sm max-w-none">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        rehypePlugins={[rehypeHighlight]}
                        components={{
                          h1: ({children}) => <h1 className="text-xl font-bold mb-3 text-gray-900 border-b pb-2">{children}</h1>,
                          h2: ({children}) => <h2 className="text-lg font-semibold mb-2 mt-4 text-gray-800">{children}</h2>,
                          h3: ({children}) => <h3 className="text-base font-medium mb-2 mt-3 text-gray-700">{children}</h3>,
                          h4: ({children}) => <h4 className="text-sm font-medium mb-1 mt-2 text-gray-700">{children}</h4>,
                          p: ({children}) => <p className="mb-3 text-gray-600 leading-relaxed text-sm">{children}</p>,
                          ul: ({children}) => <ul className="mb-3 ml-4 space-y-1">{children}</ul>,
                          ol: ({children}) => <ol className="mb-3 ml-4 space-y-1">{children}</ol>,
                          li: ({children}) => <li className="text-gray-600 text-sm">{children}</li>,
                          strong: ({children}) => <strong className="font-semibold text-gray-800">{children}</strong>,
                          em: ({children}) => <em className="italic text-gray-700">{children}</em>,
                          code: ({children}) => <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono text-gray-800">{children}</code>,
                          pre: ({children}) => <pre className="bg-gray-100 p-3 rounded overflow-x-auto mb-3 text-xs border">{children}</pre>,
                          blockquote: ({children}) => <blockquote className="border-l-3 border-blue-300 pl-3 italic text-gray-600 mb-3 bg-blue-50 py-2 text-sm">{children}</blockquote>,
                          a: ({children, href}) => <a href={href} className="text-blue-600 hover:text-blue-800 underline text-sm" target="_blank" rel="noopener noreferrer">{children}</a>,
                          table: ({children}) => <table className="min-w-full border-collapse border border-gray-300 mb-3 text-xs">{children}</table>,
                          th: ({children}) => <th className="border border-gray-300 px-2 py-1 bg-gray-100 font-semibold text-left text-xs">{children}</th>,
                          td: ({children}) => <td className="border border-gray-300 px-2 py-1 text-xs">{children}</td>,
                          hr: () => <hr className="my-4 border-gray-300" />
                        }}
                      >
                        {editingContent.content || (
                          <div className="text-gray-400 text-center py-8">
                            <div className="text-4xl mb-2">📝</div>
                            <p>开始输入内容以查看预览...</p>
                          </div>
                        )}
                      </ReactMarkdown>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelEdit}>
              取消
            </Button>
            <Button onClick={handleSaveEdit} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AIBlogManagementSystem;