"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/comma-separated-tokens";
exports.ids = ["vendor-chunks/comma-separated-tokens"];
exports.modules = {

/***/ "(ssr)/./node_modules/comma-separated-tokens/index.js":
/*!******************************************************!*\
  !*** ./node_modules/comma-separated-tokens/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/**\n * @typedef Options\n *   Configuration for `stringify`.\n * @property {boolean} [padLeft=true]\n *   Whether to pad a space before a token.\n * @property {boolean} [padRight=false]\n *   Whether to pad a space after a token.\n */ /**\n * @typedef {Options} StringifyOptions\n *   Please use `StringifyOptions` instead.\n */ /**\n * Parse comma-separated tokens to an array.\n *\n * @param {string} value\n *   Comma-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */ function parse(value) {\n    /** @type {Array<string>} */ const tokens = [];\n    const input = String(value || \"\");\n    let index = input.indexOf(\",\");\n    let start = 0;\n    /** @type {boolean} */ let end = false;\n    while(!end){\n        if (index === -1) {\n            index = input.length;\n            end = true;\n        }\n        const token = input.slice(start, index).trim();\n        if (token || !end) {\n            tokens.push(token);\n        }\n        start = index + 1;\n        index = input.indexOf(\",\", start);\n    }\n    return tokens;\n}\n/**\n * Serialize an array of strings or numbers to comma-separated tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @param {Options} [options]\n *   Configuration for `stringify` (optional).\n * @returns {string}\n *   Comma-separated tokens.\n */ function stringify(values, options) {\n    const settings = options || {};\n    // Ensure the last empty entry is seen.\n    const input = values[values.length - 1] === \"\" ? [\n        ...values,\n        \"\"\n    ] : values;\n    return input.join((settings.padRight ? \" \" : \"\") + \",\" + (settings.padLeft === false ? \"\" : \" \")).trim();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY29tbWEtc2VwYXJhdGVkLXRva2Vucy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBOzs7Ozs7O0NBT0MsR0FFRDs7O0NBR0MsR0FFRDs7Ozs7OztDQU9DLEdBQ00sU0FBU0EsTUFBTUMsS0FBSztJQUN6QiwwQkFBMEIsR0FDMUIsTUFBTUMsU0FBUyxFQUFFO0lBQ2pCLE1BQU1DLFFBQVFDLE9BQU9ILFNBQVM7SUFDOUIsSUFBSUksUUFBUUYsTUFBTUcsT0FBTyxDQUFDO0lBQzFCLElBQUlDLFFBQVE7SUFDWixvQkFBb0IsR0FDcEIsSUFBSUMsTUFBTTtJQUVWLE1BQU8sQ0FBQ0EsSUFBSztRQUNYLElBQUlILFVBQVUsQ0FBQyxHQUFHO1lBQ2hCQSxRQUFRRixNQUFNTSxNQUFNO1lBQ3BCRCxNQUFNO1FBQ1I7UUFFQSxNQUFNRSxRQUFRUCxNQUFNUSxLQUFLLENBQUNKLE9BQU9GLE9BQU9PLElBQUk7UUFFNUMsSUFBSUYsU0FBUyxDQUFDRixLQUFLO1lBQ2pCTixPQUFPVyxJQUFJLENBQUNIO1FBQ2Q7UUFFQUgsUUFBUUYsUUFBUTtRQUNoQkEsUUFBUUYsTUFBTUcsT0FBTyxDQUFDLEtBQUtDO0lBQzdCO0lBRUEsT0FBT0w7QUFDVDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNZLFVBQVVDLE1BQU0sRUFBRUMsT0FBTztJQUN2QyxNQUFNQyxXQUFXRCxXQUFXLENBQUM7SUFFN0IsdUNBQXVDO0lBQ3ZDLE1BQU1iLFFBQVFZLE1BQU0sQ0FBQ0EsT0FBT04sTUFBTSxHQUFHLEVBQUUsS0FBSyxLQUFLO1dBQUlNO1FBQVE7S0FBRyxHQUFHQTtJQUVuRSxPQUFPWixNQUNKZSxJQUFJLENBQ0gsQ0FBQ0QsU0FBU0UsUUFBUSxHQUFHLE1BQU0sRUFBQyxJQUMxQixNQUNDRixDQUFBQSxTQUFTRyxPQUFPLEtBQUssUUFBUSxLQUFLLEdBQUUsR0FFeENSLElBQUk7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2NvbW1hLXNlcGFyYXRlZC10b2tlbnMvaW5kZXguanM/NDk3NSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIE9wdGlvbnNcbiAqICAgQ29uZmlndXJhdGlvbiBmb3IgYHN0cmluZ2lmeWAuXG4gKiBAcHJvcGVydHkge2Jvb2xlYW59IFtwYWRMZWZ0PXRydWVdXG4gKiAgIFdoZXRoZXIgdG8gcGFkIGEgc3BhY2UgYmVmb3JlIGEgdG9rZW4uXG4gKiBAcHJvcGVydHkge2Jvb2xlYW59IFtwYWRSaWdodD1mYWxzZV1cbiAqICAgV2hldGhlciB0byBwYWQgYSBzcGFjZSBhZnRlciBhIHRva2VuLlxuICovXG5cbi8qKlxuICogQHR5cGVkZWYge09wdGlvbnN9IFN0cmluZ2lmeU9wdGlvbnNcbiAqICAgUGxlYXNlIHVzZSBgU3RyaW5naWZ5T3B0aW9uc2AgaW5zdGVhZC5cbiAqL1xuXG4vKipcbiAqIFBhcnNlIGNvbW1hLXNlcGFyYXRlZCB0b2tlbnMgdG8gYW4gYXJyYXkuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHZhbHVlXG4gKiAgIENvbW1hLXNlcGFyYXRlZCB0b2tlbnMuXG4gKiBAcmV0dXJucyB7QXJyYXk8c3RyaW5nPn1cbiAqICAgTGlzdCBvZiB0b2tlbnMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZSh2YWx1ZSkge1xuICAvKiogQHR5cGUge0FycmF5PHN0cmluZz59ICovXG4gIGNvbnN0IHRva2VucyA9IFtdXG4gIGNvbnN0IGlucHV0ID0gU3RyaW5nKHZhbHVlIHx8ICcnKVxuICBsZXQgaW5kZXggPSBpbnB1dC5pbmRleE9mKCcsJylcbiAgbGV0IHN0YXJ0ID0gMFxuICAvKiogQHR5cGUge2Jvb2xlYW59ICovXG4gIGxldCBlbmQgPSBmYWxzZVxuXG4gIHdoaWxlICghZW5kKSB7XG4gICAgaWYgKGluZGV4ID09PSAtMSkge1xuICAgICAgaW5kZXggPSBpbnB1dC5sZW5ndGhcbiAgICAgIGVuZCA9IHRydWVcbiAgICB9XG5cbiAgICBjb25zdCB0b2tlbiA9IGlucHV0LnNsaWNlKHN0YXJ0LCBpbmRleCkudHJpbSgpXG5cbiAgICBpZiAodG9rZW4gfHwgIWVuZCkge1xuICAgICAgdG9rZW5zLnB1c2godG9rZW4pXG4gICAgfVxuXG4gICAgc3RhcnQgPSBpbmRleCArIDFcbiAgICBpbmRleCA9IGlucHV0LmluZGV4T2YoJywnLCBzdGFydClcbiAgfVxuXG4gIHJldHVybiB0b2tlbnNcbn1cblxuLyoqXG4gKiBTZXJpYWxpemUgYW4gYXJyYXkgb2Ygc3RyaW5ncyBvciBudW1iZXJzIHRvIGNvbW1hLXNlcGFyYXRlZCB0b2tlbnMuXG4gKlxuICogQHBhcmFtIHtBcnJheTxzdHJpbmd8bnVtYmVyPn0gdmFsdWVzXG4gKiAgIExpc3Qgb2YgdG9rZW5zLlxuICogQHBhcmFtIHtPcHRpb25zfSBbb3B0aW9uc11cbiAqICAgQ29uZmlndXJhdGlvbiBmb3IgYHN0cmluZ2lmeWAgKG9wdGlvbmFsKS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIENvbW1hLXNlcGFyYXRlZCB0b2tlbnMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJpbmdpZnkodmFsdWVzLCBvcHRpb25zKSB7XG4gIGNvbnN0IHNldHRpbmdzID0gb3B0aW9ucyB8fCB7fVxuXG4gIC8vIEVuc3VyZSB0aGUgbGFzdCBlbXB0eSBlbnRyeSBpcyBzZWVuLlxuICBjb25zdCBpbnB1dCA9IHZhbHVlc1t2YWx1ZXMubGVuZ3RoIC0gMV0gPT09ICcnID8gWy4uLnZhbHVlcywgJyddIDogdmFsdWVzXG5cbiAgcmV0dXJuIGlucHV0XG4gICAgLmpvaW4oXG4gICAgICAoc2V0dGluZ3MucGFkUmlnaHQgPyAnICcgOiAnJykgK1xuICAgICAgICAnLCcgK1xuICAgICAgICAoc2V0dGluZ3MucGFkTGVmdCA9PT0gZmFsc2UgPyAnJyA6ICcgJylcbiAgICApXG4gICAgLnRyaW0oKVxufVxuIl0sIm5hbWVzIjpbInBhcnNlIiwidmFsdWUiLCJ0b2tlbnMiLCJpbnB1dCIsIlN0cmluZyIsImluZGV4IiwiaW5kZXhPZiIsInN0YXJ0IiwiZW5kIiwibGVuZ3RoIiwidG9rZW4iLCJzbGljZSIsInRyaW0iLCJwdXNoIiwic3RyaW5naWZ5IiwidmFsdWVzIiwib3B0aW9ucyIsInNldHRpbmdzIiwiam9pbiIsInBhZFJpZ2h0IiwicGFkTGVmdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/comma-separated-tokens/index.js\n");

/***/ })

};
;