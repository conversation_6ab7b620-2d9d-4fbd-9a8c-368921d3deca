"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-string";
exports.ids = ["vendor-chunks/mdast-util-to-string"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-string/lib/index.js":
/*!********************************************************!*\
  !*** ./node_modules/mdast-util-to-string/lib/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toString: () => (/* binding */ toString)\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Nodes} Nodes\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s (default: `true`).\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML (default: `true`).\n */ /** @type {Options} */ const emptyOptions = {};\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} [value]\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */ function toString(value, options) {\n    const settings = options || emptyOptions;\n    const includeImageAlt = typeof settings.includeImageAlt === \"boolean\" ? settings.includeImageAlt : true;\n    const includeHtml = typeof settings.includeHtml === \"boolean\" ? settings.includeHtml : true;\n    return one(value, includeImageAlt, includeHtml);\n}\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */ function one(value, includeImageAlt, includeHtml) {\n    if (node(value)) {\n        if (\"value\" in value) {\n            return value.type === \"html\" && !includeHtml ? \"\" : value.value;\n        }\n        if (includeImageAlt && \"alt\" in value && value.alt) {\n            return value.alt;\n        }\n        if (\"children\" in value) {\n            return all(value.children, includeImageAlt, includeHtml);\n        }\n    }\n    if (Array.isArray(value)) {\n        return all(value, includeImageAlt, includeHtml);\n    }\n    return \"\";\n}\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */ function all(values, includeImageAlt, includeHtml) {\n    /** @type {Array<string>} */ const result = [];\n    let index = -1;\n    while(++index < values.length){\n        result[index] = one(values[index], includeImageAlt, includeHtml);\n    }\n    return result.join(\"\");\n}\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Nodes}\n *   Whether `value` is a node.\n */ function node(value) {\n    return Boolean(value && typeof value === \"object\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-string/lib/index.js\n");

/***/ })

};
;