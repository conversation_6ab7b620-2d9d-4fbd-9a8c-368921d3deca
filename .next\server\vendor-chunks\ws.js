"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ws";
exports.ids = ["vendor-chunks/ws"];
exports.modules = {

/***/ "(ssr)/./node_modules/ws/lib/buffer-util.js":
/*!********************************************!*\
  !*** ./node_modules/ws/lib/buffer-util.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { EMPTY_BUFFER } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst FastBuffer = Buffer[Symbol.species];\n/**\n * Merges an array of buffers into a new buffer.\n *\n * @param {Buffer[]} list The array of buffers to concat\n * @param {Number} totalLength The total length of buffers in the list\n * @return {Buffer} The resulting buffer\n * @public\n */ function concat(list, totalLength) {\n    if (list.length === 0) return EMPTY_BUFFER;\n    if (list.length === 1) return list[0];\n    const target = Buffer.allocUnsafe(totalLength);\n    let offset = 0;\n    for(let i = 0; i < list.length; i++){\n        const buf = list[i];\n        target.set(buf, offset);\n        offset += buf.length;\n    }\n    if (offset < totalLength) {\n        return new FastBuffer(target.buffer, target.byteOffset, offset);\n    }\n    return target;\n}\n/**\n * Masks a buffer using the given mask.\n *\n * @param {Buffer} source The buffer to mask\n * @param {Buffer} mask The mask to use\n * @param {Buffer} output The buffer where to store the result\n * @param {Number} offset The offset at which to start writing\n * @param {Number} length The number of bytes to mask.\n * @public\n */ function _mask(source, mask, output, offset, length) {\n    for(let i = 0; i < length; i++){\n        output[offset + i] = source[i] ^ mask[i & 3];\n    }\n}\n/**\n * Unmasks a buffer using the given mask.\n *\n * @param {Buffer} buffer The buffer to unmask\n * @param {Buffer} mask The mask to use\n * @public\n */ function _unmask(buffer, mask) {\n    for(let i = 0; i < buffer.length; i++){\n        buffer[i] ^= mask[i & 3];\n    }\n}\n/**\n * Converts a buffer to an `ArrayBuffer`.\n *\n * @param {Buffer} buf The buffer to convert\n * @return {ArrayBuffer} Converted buffer\n * @public\n */ function toArrayBuffer(buf) {\n    if (buf.length === buf.buffer.byteLength) {\n        return buf.buffer;\n    }\n    return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.length);\n}\n/**\n * Converts `data` to a `Buffer`.\n *\n * @param {*} data The data to convert\n * @return {Buffer} The buffer\n * @throws {TypeError}\n * @public\n */ function toBuffer(data) {\n    toBuffer.readOnly = true;\n    if (Buffer.isBuffer(data)) return data;\n    let buf;\n    if (data instanceof ArrayBuffer) {\n        buf = new FastBuffer(data);\n    } else if (ArrayBuffer.isView(data)) {\n        buf = new FastBuffer(data.buffer, data.byteOffset, data.byteLength);\n    } else {\n        buf = Buffer.from(data);\n        toBuffer.readOnly = false;\n    }\n    return buf;\n}\nmodule.exports = {\n    concat,\n    mask: _mask,\n    toArrayBuffer,\n    toBuffer,\n    unmask: _unmask\n};\n/* istanbul ignore else  */ if (!process.env.WS_NO_BUFFER_UTIL) {\n    try {\n        const bufferUtil = __webpack_require__(/*! bufferutil */ \"?32c4\");\n        module.exports.mask = function(source, mask, output, offset, length) {\n            if (length < 48) _mask(source, mask, output, offset, length);\n            else bufferUtil.mask(source, mask, output, offset, length);\n        };\n        module.exports.unmask = function(buffer, mask) {\n            if (buffer.length < 32) _unmask(buffer, mask);\n            else bufferUtil.unmask(buffer, mask);\n        };\n    } catch (e) {\n    // Continue regardless of the error.\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvbGliL2J1ZmZlci11dGlsLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsTUFBTSxFQUFFQSxZQUFZLEVBQUUsR0FBR0MsbUJBQU9BLENBQUM7QUFFakMsTUFBTUMsYUFBYUMsTUFBTSxDQUFDQyxPQUFPQyxPQUFPLENBQUM7QUFFekM7Ozs7Ozs7Q0FPQyxHQUNELFNBQVNDLE9BQU9DLElBQUksRUFBRUMsV0FBVztJQUMvQixJQUFJRCxLQUFLRSxNQUFNLEtBQUssR0FBRyxPQUFPVDtJQUM5QixJQUFJTyxLQUFLRSxNQUFNLEtBQUssR0FBRyxPQUFPRixJQUFJLENBQUMsRUFBRTtJQUVyQyxNQUFNRyxTQUFTUCxPQUFPUSxXQUFXLENBQUNIO0lBQ2xDLElBQUlJLFNBQVM7SUFFYixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSU4sS0FBS0UsTUFBTSxFQUFFSSxJQUFLO1FBQ3BDLE1BQU1DLE1BQU1QLElBQUksQ0FBQ00sRUFBRTtRQUNuQkgsT0FBT0ssR0FBRyxDQUFDRCxLQUFLRjtRQUNoQkEsVUFBVUUsSUFBSUwsTUFBTTtJQUN0QjtJQUVBLElBQUlHLFNBQVNKLGFBQWE7UUFDeEIsT0FBTyxJQUFJTixXQUFXUSxPQUFPTSxNQUFNLEVBQUVOLE9BQU9PLFVBQVUsRUFBRUw7SUFDMUQ7SUFFQSxPQUFPRjtBQUNUO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ0QsU0FBU1EsTUFBTUMsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE1BQU0sRUFBRVQsTUFBTSxFQUFFSCxNQUFNO0lBQ2pELElBQUssSUFBSUksSUFBSSxHQUFHQSxJQUFJSixRQUFRSSxJQUFLO1FBQy9CUSxNQUFNLENBQUNULFNBQVNDLEVBQUUsR0FBR00sTUFBTSxDQUFDTixFQUFFLEdBQUdPLElBQUksQ0FBQ1AsSUFBSSxFQUFFO0lBQzlDO0FBQ0Y7QUFFQTs7Ozs7O0NBTUMsR0FDRCxTQUFTUyxRQUFRTixNQUFNLEVBQUVJLElBQUk7SUFDM0IsSUFBSyxJQUFJUCxJQUFJLEdBQUdBLElBQUlHLE9BQU9QLE1BQU0sRUFBRUksSUFBSztRQUN0Q0csTUFBTSxDQUFDSCxFQUFFLElBQUlPLElBQUksQ0FBQ1AsSUFBSSxFQUFFO0lBQzFCO0FBQ0Y7QUFFQTs7Ozs7O0NBTUMsR0FDRCxTQUFTVSxjQUFjVCxHQUFHO0lBQ3hCLElBQUlBLElBQUlMLE1BQU0sS0FBS0ssSUFBSUUsTUFBTSxDQUFDUSxVQUFVLEVBQUU7UUFDeEMsT0FBT1YsSUFBSUUsTUFBTTtJQUNuQjtJQUVBLE9BQU9GLElBQUlFLE1BQU0sQ0FBQ1MsS0FBSyxDQUFDWCxJQUFJRyxVQUFVLEVBQUVILElBQUlHLFVBQVUsR0FBR0gsSUFBSUwsTUFBTTtBQUNyRTtBQUVBOzs7Ozs7O0NBT0MsR0FDRCxTQUFTaUIsU0FBU0MsSUFBSTtJQUNwQkQsU0FBU0UsUUFBUSxHQUFHO0lBRXBCLElBQUl6QixPQUFPMEIsUUFBUSxDQUFDRixPQUFPLE9BQU9BO0lBRWxDLElBQUliO0lBRUosSUFBSWEsZ0JBQWdCRyxhQUFhO1FBQy9CaEIsTUFBTSxJQUFJWixXQUFXeUI7SUFDdkIsT0FBTyxJQUFJRyxZQUFZQyxNQUFNLENBQUNKLE9BQU87UUFDbkNiLE1BQU0sSUFBSVosV0FBV3lCLEtBQUtYLE1BQU0sRUFBRVcsS0FBS1YsVUFBVSxFQUFFVSxLQUFLSCxVQUFVO0lBQ3BFLE9BQU87UUFDTFYsTUFBTVgsT0FBTzZCLElBQUksQ0FBQ0w7UUFDbEJELFNBQVNFLFFBQVEsR0FBRztJQUN0QjtJQUVBLE9BQU9kO0FBQ1Q7QUFFQW1CLE9BQU9DLE9BQU8sR0FBRztJQUNmNUI7SUFDQWMsTUFBTUY7SUFDTks7SUFDQUc7SUFDQVMsUUFBUWI7QUFDVjtBQUVBLHlCQUF5QixHQUN6QixJQUFJLENBQUNjLFFBQVFDLEdBQUcsQ0FBQ0MsaUJBQWlCLEVBQUU7SUFDbEMsSUFBSTtRQUNGLE1BQU1DLGFBQWF0QyxtQkFBT0EsQ0FBQztRQUUzQmdDLG1CQUFtQixHQUFHLFNBQVVkLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxNQUFNLEVBQUVULE1BQU0sRUFBRUgsTUFBTTtZQUNsRSxJQUFJQSxTQUFTLElBQUlTLE1BQU1DLFFBQVFDLE1BQU1DLFFBQVFULFFBQVFIO2lCQUNoRDhCLFdBQVduQixJQUFJLENBQUNELFFBQVFDLE1BQU1DLFFBQVFULFFBQVFIO1FBQ3JEO1FBRUF3QixxQkFBcUIsR0FBRyxTQUFVakIsTUFBTSxFQUFFSSxJQUFJO1lBQzVDLElBQUlKLE9BQU9QLE1BQU0sR0FBRyxJQUFJYSxRQUFRTixRQUFRSTtpQkFDbkNtQixXQUFXSixNQUFNLENBQUNuQixRQUFRSTtRQUNqQztJQUNGLEVBQUUsT0FBT29CLEdBQUc7SUFDVixvQ0FBb0M7SUFDdEM7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL3dzL2xpYi9idWZmZXItdXRpbC5qcz82ZTBlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuY29uc3QgeyBFTVBUWV9CVUZGRVIgfSA9IHJlcXVpcmUoJy4vY29uc3RhbnRzJyk7XG5cbmNvbnN0IEZhc3RCdWZmZXIgPSBCdWZmZXJbU3ltYm9sLnNwZWNpZXNdO1xuXG4vKipcbiAqIE1lcmdlcyBhbiBhcnJheSBvZiBidWZmZXJzIGludG8gYSBuZXcgYnVmZmVyLlxuICpcbiAqIEBwYXJhbSB7QnVmZmVyW119IGxpc3QgVGhlIGFycmF5IG9mIGJ1ZmZlcnMgdG8gY29uY2F0XG4gKiBAcGFyYW0ge051bWJlcn0gdG90YWxMZW5ndGggVGhlIHRvdGFsIGxlbmd0aCBvZiBidWZmZXJzIGluIHRoZSBsaXN0XG4gKiBAcmV0dXJuIHtCdWZmZXJ9IFRoZSByZXN1bHRpbmcgYnVmZmVyXG4gKiBAcHVibGljXG4gKi9cbmZ1bmN0aW9uIGNvbmNhdChsaXN0LCB0b3RhbExlbmd0aCkge1xuICBpZiAobGlzdC5sZW5ndGggPT09IDApIHJldHVybiBFTVBUWV9CVUZGRVI7XG4gIGlmIChsaXN0Lmxlbmd0aCA9PT0gMSkgcmV0dXJuIGxpc3RbMF07XG5cbiAgY29uc3QgdGFyZ2V0ID0gQnVmZmVyLmFsbG9jVW5zYWZlKHRvdGFsTGVuZ3RoKTtcbiAgbGV0IG9mZnNldCA9IDA7XG5cbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBsaXN0Lmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3QgYnVmID0gbGlzdFtpXTtcbiAgICB0YXJnZXQuc2V0KGJ1Ziwgb2Zmc2V0KTtcbiAgICBvZmZzZXQgKz0gYnVmLmxlbmd0aDtcbiAgfVxuXG4gIGlmIChvZmZzZXQgPCB0b3RhbExlbmd0aCkge1xuICAgIHJldHVybiBuZXcgRmFzdEJ1ZmZlcih0YXJnZXQuYnVmZmVyLCB0YXJnZXQuYnl0ZU9mZnNldCwgb2Zmc2V0KTtcbiAgfVxuXG4gIHJldHVybiB0YXJnZXQ7XG59XG5cbi8qKlxuICogTWFza3MgYSBidWZmZXIgdXNpbmcgdGhlIGdpdmVuIG1hc2suXG4gKlxuICogQHBhcmFtIHtCdWZmZXJ9IHNvdXJjZSBUaGUgYnVmZmVyIHRvIG1hc2tcbiAqIEBwYXJhbSB7QnVmZmVyfSBtYXNrIFRoZSBtYXNrIHRvIHVzZVxuICogQHBhcmFtIHtCdWZmZXJ9IG91dHB1dCBUaGUgYnVmZmVyIHdoZXJlIHRvIHN0b3JlIHRoZSByZXN1bHRcbiAqIEBwYXJhbSB7TnVtYmVyfSBvZmZzZXQgVGhlIG9mZnNldCBhdCB3aGljaCB0byBzdGFydCB3cml0aW5nXG4gKiBAcGFyYW0ge051bWJlcn0gbGVuZ3RoIFRoZSBudW1iZXIgb2YgYnl0ZXMgdG8gbWFzay5cbiAqIEBwdWJsaWNcbiAqL1xuZnVuY3Rpb24gX21hc2soc291cmNlLCBtYXNrLCBvdXRwdXQsIG9mZnNldCwgbGVuZ3RoKSB7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgbGVuZ3RoOyBpKyspIHtcbiAgICBvdXRwdXRbb2Zmc2V0ICsgaV0gPSBzb3VyY2VbaV0gXiBtYXNrW2kgJiAzXTtcbiAgfVxufVxuXG4vKipcbiAqIFVubWFza3MgYSBidWZmZXIgdXNpbmcgdGhlIGdpdmVuIG1hc2suXG4gKlxuICogQHBhcmFtIHtCdWZmZXJ9IGJ1ZmZlciBUaGUgYnVmZmVyIHRvIHVubWFza1xuICogQHBhcmFtIHtCdWZmZXJ9IG1hc2sgVGhlIG1hc2sgdG8gdXNlXG4gKiBAcHVibGljXG4gKi9cbmZ1bmN0aW9uIF91bm1hc2soYnVmZmVyLCBtYXNrKSB7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgYnVmZmVyLmxlbmd0aDsgaSsrKSB7XG4gICAgYnVmZmVyW2ldIF49IG1hc2tbaSAmIDNdO1xuICB9XG59XG5cbi8qKlxuICogQ29udmVydHMgYSBidWZmZXIgdG8gYW4gYEFycmF5QnVmZmVyYC5cbiAqXG4gKiBAcGFyYW0ge0J1ZmZlcn0gYnVmIFRoZSBidWZmZXIgdG8gY29udmVydFxuICogQHJldHVybiB7QXJyYXlCdWZmZXJ9IENvbnZlcnRlZCBidWZmZXJcbiAqIEBwdWJsaWNcbiAqL1xuZnVuY3Rpb24gdG9BcnJheUJ1ZmZlcihidWYpIHtcbiAgaWYgKGJ1Zi5sZW5ndGggPT09IGJ1Zi5idWZmZXIuYnl0ZUxlbmd0aCkge1xuICAgIHJldHVybiBidWYuYnVmZmVyO1xuICB9XG5cbiAgcmV0dXJuIGJ1Zi5idWZmZXIuc2xpY2UoYnVmLmJ5dGVPZmZzZXQsIGJ1Zi5ieXRlT2Zmc2V0ICsgYnVmLmxlbmd0aCk7XG59XG5cbi8qKlxuICogQ29udmVydHMgYGRhdGFgIHRvIGEgYEJ1ZmZlcmAuXG4gKlxuICogQHBhcmFtIHsqfSBkYXRhIFRoZSBkYXRhIHRvIGNvbnZlcnRcbiAqIEByZXR1cm4ge0J1ZmZlcn0gVGhlIGJ1ZmZlclxuICogQHRocm93cyB7VHlwZUVycm9yfVxuICogQHB1YmxpY1xuICovXG5mdW5jdGlvbiB0b0J1ZmZlcihkYXRhKSB7XG4gIHRvQnVmZmVyLnJlYWRPbmx5ID0gdHJ1ZTtcblxuICBpZiAoQnVmZmVyLmlzQnVmZmVyKGRhdGEpKSByZXR1cm4gZGF0YTtcblxuICBsZXQgYnVmO1xuXG4gIGlmIChkYXRhIGluc3RhbmNlb2YgQXJyYXlCdWZmZXIpIHtcbiAgICBidWYgPSBuZXcgRmFzdEJ1ZmZlcihkYXRhKTtcbiAgfSBlbHNlIGlmIChBcnJheUJ1ZmZlci5pc1ZpZXcoZGF0YSkpIHtcbiAgICBidWYgPSBuZXcgRmFzdEJ1ZmZlcihkYXRhLmJ1ZmZlciwgZGF0YS5ieXRlT2Zmc2V0LCBkYXRhLmJ5dGVMZW5ndGgpO1xuICB9IGVsc2Uge1xuICAgIGJ1ZiA9IEJ1ZmZlci5mcm9tKGRhdGEpO1xuICAgIHRvQnVmZmVyLnJlYWRPbmx5ID0gZmFsc2U7XG4gIH1cblxuICByZXR1cm4gYnVmO1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgY29uY2F0LFxuICBtYXNrOiBfbWFzayxcbiAgdG9BcnJheUJ1ZmZlcixcbiAgdG9CdWZmZXIsXG4gIHVubWFzazogX3VubWFza1xufTtcblxuLyogaXN0YW5idWwgaWdub3JlIGVsc2UgICovXG5pZiAoIXByb2Nlc3MuZW52LldTX05PX0JVRkZFUl9VVElMKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgYnVmZmVyVXRpbCA9IHJlcXVpcmUoJ2J1ZmZlcnV0aWwnKTtcblxuICAgIG1vZHVsZS5leHBvcnRzLm1hc2sgPSBmdW5jdGlvbiAoc291cmNlLCBtYXNrLCBvdXRwdXQsIG9mZnNldCwgbGVuZ3RoKSB7XG4gICAgICBpZiAobGVuZ3RoIDwgNDgpIF9tYXNrKHNvdXJjZSwgbWFzaywgb3V0cHV0LCBvZmZzZXQsIGxlbmd0aCk7XG4gICAgICBlbHNlIGJ1ZmZlclV0aWwubWFzayhzb3VyY2UsIG1hc2ssIG91dHB1dCwgb2Zmc2V0LCBsZW5ndGgpO1xuICAgIH07XG5cbiAgICBtb2R1bGUuZXhwb3J0cy51bm1hc2sgPSBmdW5jdGlvbiAoYnVmZmVyLCBtYXNrKSB7XG4gICAgICBpZiAoYnVmZmVyLmxlbmd0aCA8IDMyKSBfdW5tYXNrKGJ1ZmZlciwgbWFzayk7XG4gICAgICBlbHNlIGJ1ZmZlclV0aWwudW5tYXNrKGJ1ZmZlciwgbWFzayk7XG4gICAgfTtcbiAgfSBjYXRjaCAoZSkge1xuICAgIC8vIENvbnRpbnVlIHJlZ2FyZGxlc3Mgb2YgdGhlIGVycm9yLlxuICB9XG59XG4iXSwibmFtZXMiOlsiRU1QVFlfQlVGRkVSIiwicmVxdWlyZSIsIkZhc3RCdWZmZXIiLCJCdWZmZXIiLCJTeW1ib2wiLCJzcGVjaWVzIiwiY29uY2F0IiwibGlzdCIsInRvdGFsTGVuZ3RoIiwibGVuZ3RoIiwidGFyZ2V0IiwiYWxsb2NVbnNhZmUiLCJvZmZzZXQiLCJpIiwiYnVmIiwic2V0IiwiYnVmZmVyIiwiYnl0ZU9mZnNldCIsIl9tYXNrIiwic291cmNlIiwibWFzayIsIm91dHB1dCIsIl91bm1hc2siLCJ0b0FycmF5QnVmZmVyIiwiYnl0ZUxlbmd0aCIsInNsaWNlIiwidG9CdWZmZXIiLCJkYXRhIiwicmVhZE9ubHkiLCJpc0J1ZmZlciIsIkFycmF5QnVmZmVyIiwiaXNWaWV3IiwiZnJvbSIsIm1vZHVsZSIsImV4cG9ydHMiLCJ1bm1hc2siLCJwcm9jZXNzIiwiZW52IiwiV1NfTk9fQlVGRkVSX1VUSUwiLCJidWZmZXJVdGlsIiwiZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/buffer-util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/constants.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/constants.js ***!
  \******************************************/
/***/ ((module) => {

eval("\nconst BINARY_TYPES = [\n    \"nodebuffer\",\n    \"arraybuffer\",\n    \"fragments\"\n];\nconst hasBlob = typeof Blob !== \"undefined\";\nif (hasBlob) BINARY_TYPES.push(\"blob\");\nmodule.exports = {\n    BINARY_TYPES,\n    EMPTY_BUFFER: Buffer.alloc(0),\n    GUID: \"258EAFA5-E914-47DA-95CA-C5AB0DC85B11\",\n    hasBlob,\n    kForOnEventAttribute: Symbol(\"kIsForOnEventAttribute\"),\n    kListener: Symbol(\"kListener\"),\n    kStatusCode: Symbol(\"status-code\"),\n    kWebSocket: Symbol(\"websocket\"),\n    NOOP: ()=>{}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLE1BQU1BLGVBQWU7SUFBQztJQUFjO0lBQWU7Q0FBWTtBQUMvRCxNQUFNQyxVQUFVLE9BQU9DLFNBQVM7QUFFaEMsSUFBSUQsU0FBU0QsYUFBYUcsSUFBSSxDQUFDO0FBRS9CQyxPQUFPQyxPQUFPLEdBQUc7SUFDZkw7SUFDQU0sY0FBY0MsT0FBT0MsS0FBSyxDQUFDO0lBQzNCQyxNQUFNO0lBQ05SO0lBQ0FTLHNCQUFzQkMsT0FBTztJQUM3QkMsV0FBV0QsT0FBTztJQUNsQkUsYUFBYUYsT0FBTztJQUNwQkcsWUFBWUgsT0FBTztJQUNuQkksTUFBTSxLQUFPO0FBQ2YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy93cy9saWIvY29uc3RhbnRzLmpzP2MyYTMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCBCSU5BUllfVFlQRVMgPSBbJ25vZGVidWZmZXInLCAnYXJyYXlidWZmZXInLCAnZnJhZ21lbnRzJ107XG5jb25zdCBoYXNCbG9iID0gdHlwZW9mIEJsb2IgIT09ICd1bmRlZmluZWQnO1xuXG5pZiAoaGFzQmxvYikgQklOQVJZX1RZUEVTLnB1c2goJ2Jsb2InKTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIEJJTkFSWV9UWVBFUyxcbiAgRU1QVFlfQlVGRkVSOiBCdWZmZXIuYWxsb2MoMCksXG4gIEdVSUQ6ICcyNThFQUZBNS1FOTE0LTQ3REEtOTVDQS1DNUFCMERDODVCMTEnLFxuICBoYXNCbG9iLFxuICBrRm9yT25FdmVudEF0dHJpYnV0ZTogU3ltYm9sKCdrSXNGb3JPbkV2ZW50QXR0cmlidXRlJyksXG4gIGtMaXN0ZW5lcjogU3ltYm9sKCdrTGlzdGVuZXInKSxcbiAga1N0YXR1c0NvZGU6IFN5bWJvbCgnc3RhdHVzLWNvZGUnKSxcbiAga1dlYlNvY2tldDogU3ltYm9sKCd3ZWJzb2NrZXQnKSxcbiAgTk9PUDogKCkgPT4ge31cbn07XG4iXSwibmFtZXMiOlsiQklOQVJZX1RZUEVTIiwiaGFzQmxvYiIsIkJsb2IiLCJwdXNoIiwibW9kdWxlIiwiZXhwb3J0cyIsIkVNUFRZX0JVRkZFUiIsIkJ1ZmZlciIsImFsbG9jIiwiR1VJRCIsImtGb3JPbkV2ZW50QXR0cmlidXRlIiwiU3ltYm9sIiwia0xpc3RlbmVyIiwia1N0YXR1c0NvZGUiLCJrV2ViU29ja2V0IiwiTk9PUCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/event-target.js":
/*!*********************************************!*\
  !*** ./node_modules/ws/lib/event-target.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { kForOnEventAttribute, kListener } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst kCode = Symbol(\"kCode\");\nconst kData = Symbol(\"kData\");\nconst kError = Symbol(\"kError\");\nconst kMessage = Symbol(\"kMessage\");\nconst kReason = Symbol(\"kReason\");\nconst kTarget = Symbol(\"kTarget\");\nconst kType = Symbol(\"kType\");\nconst kWasClean = Symbol(\"kWasClean\");\n/**\n * Class representing an event.\n */ class Event {\n    /**\n   * Create a new `Event`.\n   *\n   * @param {String} type The name of the event\n   * @throws {TypeError} If the `type` argument is not specified\n   */ constructor(type){\n        this[kTarget] = null;\n        this[kType] = type;\n    }\n    /**\n   * @type {*}\n   */ get target() {\n        return this[kTarget];\n    }\n    /**\n   * @type {String}\n   */ get type() {\n        return this[kType];\n    }\n}\nObject.defineProperty(Event.prototype, \"target\", {\n    enumerable: true\n});\nObject.defineProperty(Event.prototype, \"type\", {\n    enumerable: true\n});\n/**\n * Class representing a close event.\n *\n * @extends Event\n */ class CloseEvent extends Event {\n    /**\n   * Create a new `CloseEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {Number} [options.code=0] The status code explaining why the\n   *     connection was closed\n   * @param {String} [options.reason=''] A human-readable string explaining why\n   *     the connection was closed\n   * @param {Boolean} [options.wasClean=false] Indicates whether or not the\n   *     connection was cleanly closed\n   */ constructor(type, options = {}){\n        super(type);\n        this[kCode] = options.code === undefined ? 0 : options.code;\n        this[kReason] = options.reason === undefined ? \"\" : options.reason;\n        this[kWasClean] = options.wasClean === undefined ? false : options.wasClean;\n    }\n    /**\n   * @type {Number}\n   */ get code() {\n        return this[kCode];\n    }\n    /**\n   * @type {String}\n   */ get reason() {\n        return this[kReason];\n    }\n    /**\n   * @type {Boolean}\n   */ get wasClean() {\n        return this[kWasClean];\n    }\n}\nObject.defineProperty(CloseEvent.prototype, \"code\", {\n    enumerable: true\n});\nObject.defineProperty(CloseEvent.prototype, \"reason\", {\n    enumerable: true\n});\nObject.defineProperty(CloseEvent.prototype, \"wasClean\", {\n    enumerable: true\n});\n/**\n * Class representing an error event.\n *\n * @extends Event\n */ class ErrorEvent extends Event {\n    /**\n   * Create a new `ErrorEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.error=null] The error that generated this event\n   * @param {String} [options.message=''] The error message\n   */ constructor(type, options = {}){\n        super(type);\n        this[kError] = options.error === undefined ? null : options.error;\n        this[kMessage] = options.message === undefined ? \"\" : options.message;\n    }\n    /**\n   * @type {*}\n   */ get error() {\n        return this[kError];\n    }\n    /**\n   * @type {String}\n   */ get message() {\n        return this[kMessage];\n    }\n}\nObject.defineProperty(ErrorEvent.prototype, \"error\", {\n    enumerable: true\n});\nObject.defineProperty(ErrorEvent.prototype, \"message\", {\n    enumerable: true\n});\n/**\n * Class representing a message event.\n *\n * @extends Event\n */ class MessageEvent extends Event {\n    /**\n   * Create a new `MessageEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.data=null] The message content\n   */ constructor(type, options = {}){\n        super(type);\n        this[kData] = options.data === undefined ? null : options.data;\n    }\n    /**\n   * @type {*}\n   */ get data() {\n        return this[kData];\n    }\n}\nObject.defineProperty(MessageEvent.prototype, \"data\", {\n    enumerable: true\n});\n/**\n * This provides methods for emulating the `EventTarget` interface. It's not\n * meant to be used directly.\n *\n * @mixin\n */ const EventTarget = {\n    /**\n   * Register an event listener.\n   *\n   * @param {String} type A string representing the event type to listen for\n   * @param {(Function|Object)} handler The listener to add\n   * @param {Object} [options] An options object specifies characteristics about\n   *     the event listener\n   * @param {Boolean} [options.once=false] A `Boolean` indicating that the\n   *     listener should be invoked at most once after being added. If `true`,\n   *     the listener would be automatically removed when invoked.\n   * @public\n   */ addEventListener (type, handler, options = {}) {\n        for (const listener of this.listeners(type)){\n            if (!options[kForOnEventAttribute] && listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n                return;\n            }\n        }\n        let wrapper;\n        if (type === \"message\") {\n            wrapper = function onMessage(data, isBinary) {\n                const event = new MessageEvent(\"message\", {\n                    data: isBinary ? data : data.toString()\n                });\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else if (type === \"close\") {\n            wrapper = function onClose(code, message) {\n                const event = new CloseEvent(\"close\", {\n                    code,\n                    reason: message.toString(),\n                    wasClean: this._closeFrameReceived && this._closeFrameSent\n                });\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else if (type === \"error\") {\n            wrapper = function onError(error) {\n                const event = new ErrorEvent(\"error\", {\n                    error,\n                    message: error.message\n                });\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else if (type === \"open\") {\n            wrapper = function onOpen() {\n                const event = new Event(\"open\");\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else {\n            return;\n        }\n        wrapper[kForOnEventAttribute] = !!options[kForOnEventAttribute];\n        wrapper[kListener] = handler;\n        if (options.once) {\n            this.once(type, wrapper);\n        } else {\n            this.on(type, wrapper);\n        }\n    },\n    /**\n   * Remove an event listener.\n   *\n   * @param {String} type A string representing the event type to remove\n   * @param {(Function|Object)} handler The listener to remove\n   * @public\n   */ removeEventListener (type, handler) {\n        for (const listener of this.listeners(type)){\n            if (listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n                this.removeListener(type, listener);\n                break;\n            }\n        }\n    }\n};\nmodule.exports = {\n    CloseEvent,\n    ErrorEvent,\n    Event,\n    EventTarget,\n    MessageEvent\n};\n/**\n * Call an event listener\n *\n * @param {(Function|Object)} listener The listener to call\n * @param {*} thisArg The value to use as `this`` when calling the listener\n * @param {Event} event The event to pass to the listener\n * @private\n */ function callListener(listener, thisArg, event) {\n    if (typeof listener === \"object\" && listener.handleEvent) {\n        listener.handleEvent.call(listener, event);\n    } else {\n        listener.call(thisArg, event);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvbGliL2V2ZW50LXRhcmdldC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLE1BQU0sRUFBRUEsb0JBQW9CLEVBQUVDLFNBQVMsRUFBRSxHQUFHQyxtQkFBT0EsQ0FBQztBQUVwRCxNQUFNQyxRQUFRQyxPQUFPO0FBQ3JCLE1BQU1DLFFBQVFELE9BQU87QUFDckIsTUFBTUUsU0FBU0YsT0FBTztBQUN0QixNQUFNRyxXQUFXSCxPQUFPO0FBQ3hCLE1BQU1JLFVBQVVKLE9BQU87QUFDdkIsTUFBTUssVUFBVUwsT0FBTztBQUN2QixNQUFNTSxRQUFRTixPQUFPO0FBQ3JCLE1BQU1PLFlBQVlQLE9BQU87QUFFekI7O0NBRUMsR0FDRCxNQUFNUTtJQUNKOzs7OztHQUtDLEdBQ0RDLFlBQVlDLElBQUksQ0FBRTtRQUNoQixJQUFJLENBQUNMLFFBQVEsR0FBRztRQUNoQixJQUFJLENBQUNDLE1BQU0sR0FBR0k7SUFDaEI7SUFFQTs7R0FFQyxHQUNELElBQUlDLFNBQVM7UUFDWCxPQUFPLElBQUksQ0FBQ04sUUFBUTtJQUN0QjtJQUVBOztHQUVDLEdBQ0QsSUFBSUssT0FBTztRQUNULE9BQU8sSUFBSSxDQUFDSixNQUFNO0lBQ3BCO0FBQ0Y7QUFFQU0sT0FBT0MsY0FBYyxDQUFDTCxNQUFNTSxTQUFTLEVBQUUsVUFBVTtJQUFFQyxZQUFZO0FBQUs7QUFDcEVILE9BQU9DLGNBQWMsQ0FBQ0wsTUFBTU0sU0FBUyxFQUFFLFFBQVE7SUFBRUMsWUFBWTtBQUFLO0FBRWxFOzs7O0NBSUMsR0FDRCxNQUFNQyxtQkFBbUJSO0lBQ3ZCOzs7Ozs7Ozs7Ozs7R0FZQyxHQUNEQyxZQUFZQyxJQUFJLEVBQUVPLFVBQVUsQ0FBQyxDQUFDLENBQUU7UUFDOUIsS0FBSyxDQUFDUDtRQUVOLElBQUksQ0FBQ1gsTUFBTSxHQUFHa0IsUUFBUUMsSUFBSSxLQUFLQyxZQUFZLElBQUlGLFFBQVFDLElBQUk7UUFDM0QsSUFBSSxDQUFDZCxRQUFRLEdBQUdhLFFBQVFHLE1BQU0sS0FBS0QsWUFBWSxLQUFLRixRQUFRRyxNQUFNO1FBQ2xFLElBQUksQ0FBQ2IsVUFBVSxHQUFHVSxRQUFRSSxRQUFRLEtBQUtGLFlBQVksUUFBUUYsUUFBUUksUUFBUTtJQUM3RTtJQUVBOztHQUVDLEdBQ0QsSUFBSUgsT0FBTztRQUNULE9BQU8sSUFBSSxDQUFDbkIsTUFBTTtJQUNwQjtJQUVBOztHQUVDLEdBQ0QsSUFBSXFCLFNBQVM7UUFDWCxPQUFPLElBQUksQ0FBQ2hCLFFBQVE7SUFDdEI7SUFFQTs7R0FFQyxHQUNELElBQUlpQixXQUFXO1FBQ2IsT0FBTyxJQUFJLENBQUNkLFVBQVU7SUFDeEI7QUFDRjtBQUVBSyxPQUFPQyxjQUFjLENBQUNHLFdBQVdGLFNBQVMsRUFBRSxRQUFRO0lBQUVDLFlBQVk7QUFBSztBQUN2RUgsT0FBT0MsY0FBYyxDQUFDRyxXQUFXRixTQUFTLEVBQUUsVUFBVTtJQUFFQyxZQUFZO0FBQUs7QUFDekVILE9BQU9DLGNBQWMsQ0FBQ0csV0FBV0YsU0FBUyxFQUFFLFlBQVk7SUFBRUMsWUFBWTtBQUFLO0FBRTNFOzs7O0NBSUMsR0FDRCxNQUFNTyxtQkFBbUJkO0lBQ3ZCOzs7Ozs7OztHQVFDLEdBQ0RDLFlBQVlDLElBQUksRUFBRU8sVUFBVSxDQUFDLENBQUMsQ0FBRTtRQUM5QixLQUFLLENBQUNQO1FBRU4sSUFBSSxDQUFDUixPQUFPLEdBQUdlLFFBQVFNLEtBQUssS0FBS0osWUFBWSxPQUFPRixRQUFRTSxLQUFLO1FBQ2pFLElBQUksQ0FBQ3BCLFNBQVMsR0FBR2MsUUFBUU8sT0FBTyxLQUFLTCxZQUFZLEtBQUtGLFFBQVFPLE9BQU87SUFDdkU7SUFFQTs7R0FFQyxHQUNELElBQUlELFFBQVE7UUFDVixPQUFPLElBQUksQ0FBQ3JCLE9BQU87SUFDckI7SUFFQTs7R0FFQyxHQUNELElBQUlzQixVQUFVO1FBQ1osT0FBTyxJQUFJLENBQUNyQixTQUFTO0lBQ3ZCO0FBQ0Y7QUFFQVMsT0FBT0MsY0FBYyxDQUFDUyxXQUFXUixTQUFTLEVBQUUsU0FBUztJQUFFQyxZQUFZO0FBQUs7QUFDeEVILE9BQU9DLGNBQWMsQ0FBQ1MsV0FBV1IsU0FBUyxFQUFFLFdBQVc7SUFBRUMsWUFBWTtBQUFLO0FBRTFFOzs7O0NBSUMsR0FDRCxNQUFNVSxxQkFBcUJqQjtJQUN6Qjs7Ozs7OztHQU9DLEdBQ0RDLFlBQVlDLElBQUksRUFBRU8sVUFBVSxDQUFDLENBQUMsQ0FBRTtRQUM5QixLQUFLLENBQUNQO1FBRU4sSUFBSSxDQUFDVCxNQUFNLEdBQUdnQixRQUFRUyxJQUFJLEtBQUtQLFlBQVksT0FBT0YsUUFBUVMsSUFBSTtJQUNoRTtJQUVBOztHQUVDLEdBQ0QsSUFBSUEsT0FBTztRQUNULE9BQU8sSUFBSSxDQUFDekIsTUFBTTtJQUNwQjtBQUNGO0FBRUFXLE9BQU9DLGNBQWMsQ0FBQ1ksYUFBYVgsU0FBUyxFQUFFLFFBQVE7SUFBRUMsWUFBWTtBQUFLO0FBRXpFOzs7OztDQUtDLEdBQ0QsTUFBTVksY0FBYztJQUNsQjs7Ozs7Ozs7Ozs7R0FXQyxHQUNEQyxrQkFBaUJsQixJQUFJLEVBQUVtQixPQUFPLEVBQUVaLFVBQVUsQ0FBQyxDQUFDO1FBQzFDLEtBQUssTUFBTWEsWUFBWSxJQUFJLENBQUNDLFNBQVMsQ0FBQ3JCLE1BQU87WUFDM0MsSUFDRSxDQUFDTyxPQUFPLENBQUNyQixxQkFBcUIsSUFDOUJrQyxRQUFRLENBQUNqQyxVQUFVLEtBQUtnQyxXQUN4QixDQUFDQyxRQUFRLENBQUNsQyxxQkFBcUIsRUFDL0I7Z0JBQ0E7WUFDRjtRQUNGO1FBRUEsSUFBSW9DO1FBRUosSUFBSXRCLFNBQVMsV0FBVztZQUN0QnNCLFVBQVUsU0FBU0MsVUFBVVAsSUFBSSxFQUFFUSxRQUFRO2dCQUN6QyxNQUFNQyxRQUFRLElBQUlWLGFBQWEsV0FBVztvQkFDeENDLE1BQU1RLFdBQVdSLE9BQU9BLEtBQUtVLFFBQVE7Z0JBQ3ZDO2dCQUVBRCxLQUFLLENBQUM5QixRQUFRLEdBQUcsSUFBSTtnQkFDckJnQyxhQUFhUixTQUFTLElBQUksRUFBRU07WUFDOUI7UUFDRixPQUFPLElBQUl6QixTQUFTLFNBQVM7WUFDM0JzQixVQUFVLFNBQVNNLFFBQVFwQixJQUFJLEVBQUVNLE9BQU87Z0JBQ3RDLE1BQU1XLFFBQVEsSUFBSW5CLFdBQVcsU0FBUztvQkFDcENFO29CQUNBRSxRQUFRSSxRQUFRWSxRQUFRO29CQUN4QmYsVUFBVSxJQUFJLENBQUNrQixtQkFBbUIsSUFBSSxJQUFJLENBQUNDLGVBQWU7Z0JBQzVEO2dCQUVBTCxLQUFLLENBQUM5QixRQUFRLEdBQUcsSUFBSTtnQkFDckJnQyxhQUFhUixTQUFTLElBQUksRUFBRU07WUFDOUI7UUFDRixPQUFPLElBQUl6QixTQUFTLFNBQVM7WUFDM0JzQixVQUFVLFNBQVNTLFFBQVFsQixLQUFLO2dCQUM5QixNQUFNWSxRQUFRLElBQUliLFdBQVcsU0FBUztvQkFDcENDO29CQUNBQyxTQUFTRCxNQUFNQyxPQUFPO2dCQUN4QjtnQkFFQVcsS0FBSyxDQUFDOUIsUUFBUSxHQUFHLElBQUk7Z0JBQ3JCZ0MsYUFBYVIsU0FBUyxJQUFJLEVBQUVNO1lBQzlCO1FBQ0YsT0FBTyxJQUFJekIsU0FBUyxRQUFRO1lBQzFCc0IsVUFBVSxTQUFTVTtnQkFDakIsTUFBTVAsUUFBUSxJQUFJM0IsTUFBTTtnQkFFeEIyQixLQUFLLENBQUM5QixRQUFRLEdBQUcsSUFBSTtnQkFDckJnQyxhQUFhUixTQUFTLElBQUksRUFBRU07WUFDOUI7UUFDRixPQUFPO1lBQ0w7UUFDRjtRQUVBSCxPQUFPLENBQUNwQyxxQkFBcUIsR0FBRyxDQUFDLENBQUNxQixPQUFPLENBQUNyQixxQkFBcUI7UUFDL0RvQyxPQUFPLENBQUNuQyxVQUFVLEdBQUdnQztRQUVyQixJQUFJWixRQUFRMEIsSUFBSSxFQUFFO1lBQ2hCLElBQUksQ0FBQ0EsSUFBSSxDQUFDakMsTUFBTXNCO1FBQ2xCLE9BQU87WUFDTCxJQUFJLENBQUNZLEVBQUUsQ0FBQ2xDLE1BQU1zQjtRQUNoQjtJQUNGO0lBRUE7Ozs7OztHQU1DLEdBQ0RhLHFCQUFvQm5DLElBQUksRUFBRW1CLE9BQU87UUFDL0IsS0FBSyxNQUFNQyxZQUFZLElBQUksQ0FBQ0MsU0FBUyxDQUFDckIsTUFBTztZQUMzQyxJQUFJb0IsUUFBUSxDQUFDakMsVUFBVSxLQUFLZ0MsV0FBVyxDQUFDQyxRQUFRLENBQUNsQyxxQkFBcUIsRUFBRTtnQkFDdEUsSUFBSSxDQUFDa0QsY0FBYyxDQUFDcEMsTUFBTW9CO2dCQUMxQjtZQUNGO1FBQ0Y7SUFDRjtBQUNGO0FBRUFpQixPQUFPQyxPQUFPLEdBQUc7SUFDZmhDO0lBQ0FNO0lBQ0FkO0lBQ0FtQjtJQUNBRjtBQUNGO0FBRUE7Ozs7Ozs7Q0FPQyxHQUNELFNBQVNZLGFBQWFQLFFBQVEsRUFBRW1CLE9BQU8sRUFBRWQsS0FBSztJQUM1QyxJQUFJLE9BQU9MLGFBQWEsWUFBWUEsU0FBU29CLFdBQVcsRUFBRTtRQUN4RHBCLFNBQVNvQixXQUFXLENBQUNDLElBQUksQ0FBQ3JCLFVBQVVLO0lBQ3RDLE9BQU87UUFDTEwsU0FBU3FCLElBQUksQ0FBQ0YsU0FBU2Q7SUFDekI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1ibG9nLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL3dzL2xpYi9ldmVudC10YXJnZXQuanM/MzM0NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNvbnN0IHsga0Zvck9uRXZlbnRBdHRyaWJ1dGUsIGtMaXN0ZW5lciB9ID0gcmVxdWlyZSgnLi9jb25zdGFudHMnKTtcblxuY29uc3Qga0NvZGUgPSBTeW1ib2woJ2tDb2RlJyk7XG5jb25zdCBrRGF0YSA9IFN5bWJvbCgna0RhdGEnKTtcbmNvbnN0IGtFcnJvciA9IFN5bWJvbCgna0Vycm9yJyk7XG5jb25zdCBrTWVzc2FnZSA9IFN5bWJvbCgna01lc3NhZ2UnKTtcbmNvbnN0IGtSZWFzb24gPSBTeW1ib2woJ2tSZWFzb24nKTtcbmNvbnN0IGtUYXJnZXQgPSBTeW1ib2woJ2tUYXJnZXQnKTtcbmNvbnN0IGtUeXBlID0gU3ltYm9sKCdrVHlwZScpO1xuY29uc3Qga1dhc0NsZWFuID0gU3ltYm9sKCdrV2FzQ2xlYW4nKTtcblxuLyoqXG4gKiBDbGFzcyByZXByZXNlbnRpbmcgYW4gZXZlbnQuXG4gKi9cbmNsYXNzIEV2ZW50IHtcbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyBgRXZlbnRgLlxuICAgKlxuICAgKiBAcGFyYW0ge1N0cmluZ30gdHlwZSBUaGUgbmFtZSBvZiB0aGUgZXZlbnRcbiAgICogQHRocm93cyB7VHlwZUVycm9yfSBJZiB0aGUgYHR5cGVgIGFyZ3VtZW50IGlzIG5vdCBzcGVjaWZpZWRcbiAgICovXG4gIGNvbnN0cnVjdG9yKHR5cGUpIHtcbiAgICB0aGlzW2tUYXJnZXRdID0gbnVsbDtcbiAgICB0aGlzW2tUeXBlXSA9IHR5cGU7XG4gIH1cblxuICAvKipcbiAgICogQHR5cGUgeyp9XG4gICAqL1xuICBnZXQgdGFyZ2V0KCkge1xuICAgIHJldHVybiB0aGlzW2tUYXJnZXRdO1xuICB9XG5cbiAgLyoqXG4gICAqIEB0eXBlIHtTdHJpbmd9XG4gICAqL1xuICBnZXQgdHlwZSgpIHtcbiAgICByZXR1cm4gdGhpc1trVHlwZV07XG4gIH1cbn1cblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KEV2ZW50LnByb3RvdHlwZSwgJ3RhcmdldCcsIHsgZW51bWVyYWJsZTogdHJ1ZSB9KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShFdmVudC5wcm90b3R5cGUsICd0eXBlJywgeyBlbnVtZXJhYmxlOiB0cnVlIH0pO1xuXG4vKipcbiAqIENsYXNzIHJlcHJlc2VudGluZyBhIGNsb3NlIGV2ZW50LlxuICpcbiAqIEBleHRlbmRzIEV2ZW50XG4gKi9cbmNsYXNzIENsb3NlRXZlbnQgZXh0ZW5kcyBFdmVudCB7XG4gIC8qKlxuICAgKiBDcmVhdGUgYSBuZXcgYENsb3NlRXZlbnRgLlxuICAgKlxuICAgKiBAcGFyYW0ge1N0cmluZ30gdHlwZSBUaGUgbmFtZSBvZiB0aGUgZXZlbnRcbiAgICogQHBhcmFtIHtPYmplY3R9IFtvcHRpb25zXSBBIGRpY3Rpb25hcnkgb2JqZWN0IHRoYXQgYWxsb3dzIGZvciBzZXR0aW5nXG4gICAqICAgICBhdHRyaWJ1dGVzIHZpYSBvYmplY3QgbWVtYmVycyBvZiB0aGUgc2FtZSBuYW1lXG4gICAqIEBwYXJhbSB7TnVtYmVyfSBbb3B0aW9ucy5jb2RlPTBdIFRoZSBzdGF0dXMgY29kZSBleHBsYWluaW5nIHdoeSB0aGVcbiAgICogICAgIGNvbm5lY3Rpb24gd2FzIGNsb3NlZFxuICAgKiBAcGFyYW0ge1N0cmluZ30gW29wdGlvbnMucmVhc29uPScnXSBBIGh1bWFuLXJlYWRhYmxlIHN0cmluZyBleHBsYWluaW5nIHdoeVxuICAgKiAgICAgdGhlIGNvbm5lY3Rpb24gd2FzIGNsb3NlZFxuICAgKiBAcGFyYW0ge0Jvb2xlYW59IFtvcHRpb25zLndhc0NsZWFuPWZhbHNlXSBJbmRpY2F0ZXMgd2hldGhlciBvciBub3QgdGhlXG4gICAqICAgICBjb25uZWN0aW9uIHdhcyBjbGVhbmx5IGNsb3NlZFxuICAgKi9cbiAgY29uc3RydWN0b3IodHlwZSwgb3B0aW9ucyA9IHt9KSB7XG4gICAgc3VwZXIodHlwZSk7XG5cbiAgICB0aGlzW2tDb2RlXSA9IG9wdGlvbnMuY29kZSA9PT0gdW5kZWZpbmVkID8gMCA6IG9wdGlvbnMuY29kZTtcbiAgICB0aGlzW2tSZWFzb25dID0gb3B0aW9ucy5yZWFzb24gPT09IHVuZGVmaW5lZCA/ICcnIDogb3B0aW9ucy5yZWFzb247XG4gICAgdGhpc1trV2FzQ2xlYW5dID0gb3B0aW9ucy53YXNDbGVhbiA9PT0gdW5kZWZpbmVkID8gZmFsc2UgOiBvcHRpb25zLndhc0NsZWFuO1xuICB9XG5cbiAgLyoqXG4gICAqIEB0eXBlIHtOdW1iZXJ9XG4gICAqL1xuICBnZXQgY29kZSgpIHtcbiAgICByZXR1cm4gdGhpc1trQ29kZV07XG4gIH1cblxuICAvKipcbiAgICogQHR5cGUge1N0cmluZ31cbiAgICovXG4gIGdldCByZWFzb24oKSB7XG4gICAgcmV0dXJuIHRoaXNba1JlYXNvbl07XG4gIH1cblxuICAvKipcbiAgICogQHR5cGUge0Jvb2xlYW59XG4gICAqL1xuICBnZXQgd2FzQ2xlYW4oKSB7XG4gICAgcmV0dXJuIHRoaXNba1dhc0NsZWFuXTtcbiAgfVxufVxuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoQ2xvc2VFdmVudC5wcm90b3R5cGUsICdjb2RlJywgeyBlbnVtZXJhYmxlOiB0cnVlIH0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KENsb3NlRXZlbnQucHJvdG90eXBlLCAncmVhc29uJywgeyBlbnVtZXJhYmxlOiB0cnVlIH0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KENsb3NlRXZlbnQucHJvdG90eXBlLCAnd2FzQ2xlYW4nLCB7IGVudW1lcmFibGU6IHRydWUgfSk7XG5cbi8qKlxuICogQ2xhc3MgcmVwcmVzZW50aW5nIGFuIGVycm9yIGV2ZW50LlxuICpcbiAqIEBleHRlbmRzIEV2ZW50XG4gKi9cbmNsYXNzIEVycm9yRXZlbnQgZXh0ZW5kcyBFdmVudCB7XG4gIC8qKlxuICAgKiBDcmVhdGUgYSBuZXcgYEVycm9yRXZlbnRgLlxuICAgKlxuICAgKiBAcGFyYW0ge1N0cmluZ30gdHlwZSBUaGUgbmFtZSBvZiB0aGUgZXZlbnRcbiAgICogQHBhcmFtIHtPYmplY3R9IFtvcHRpb25zXSBBIGRpY3Rpb25hcnkgb2JqZWN0IHRoYXQgYWxsb3dzIGZvciBzZXR0aW5nXG4gICAqICAgICBhdHRyaWJ1dGVzIHZpYSBvYmplY3QgbWVtYmVycyBvZiB0aGUgc2FtZSBuYW1lXG4gICAqIEBwYXJhbSB7Kn0gW29wdGlvbnMuZXJyb3I9bnVsbF0gVGhlIGVycm9yIHRoYXQgZ2VuZXJhdGVkIHRoaXMgZXZlbnRcbiAgICogQHBhcmFtIHtTdHJpbmd9IFtvcHRpb25zLm1lc3NhZ2U9JyddIFRoZSBlcnJvciBtZXNzYWdlXG4gICAqL1xuICBjb25zdHJ1Y3Rvcih0eXBlLCBvcHRpb25zID0ge30pIHtcbiAgICBzdXBlcih0eXBlKTtcblxuICAgIHRoaXNba0Vycm9yXSA9IG9wdGlvbnMuZXJyb3IgPT09IHVuZGVmaW5lZCA/IG51bGwgOiBvcHRpb25zLmVycm9yO1xuICAgIHRoaXNba01lc3NhZ2VdID0gb3B0aW9ucy5tZXNzYWdlID09PSB1bmRlZmluZWQgPyAnJyA6IG9wdGlvbnMubWVzc2FnZTtcbiAgfVxuXG4gIC8qKlxuICAgKiBAdHlwZSB7Kn1cbiAgICovXG4gIGdldCBlcnJvcigpIHtcbiAgICByZXR1cm4gdGhpc1trRXJyb3JdO1xuICB9XG5cbiAgLyoqXG4gICAqIEB0eXBlIHtTdHJpbmd9XG4gICAqL1xuICBnZXQgbWVzc2FnZSgpIHtcbiAgICByZXR1cm4gdGhpc1trTWVzc2FnZV07XG4gIH1cbn1cblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KEVycm9yRXZlbnQucHJvdG90eXBlLCAnZXJyb3InLCB7IGVudW1lcmFibGU6IHRydWUgfSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoRXJyb3JFdmVudC5wcm90b3R5cGUsICdtZXNzYWdlJywgeyBlbnVtZXJhYmxlOiB0cnVlIH0pO1xuXG4vKipcbiAqIENsYXNzIHJlcHJlc2VudGluZyBhIG1lc3NhZ2UgZXZlbnQuXG4gKlxuICogQGV4dGVuZHMgRXZlbnRcbiAqL1xuY2xhc3MgTWVzc2FnZUV2ZW50IGV4dGVuZHMgRXZlbnQge1xuICAvKipcbiAgICogQ3JlYXRlIGEgbmV3IGBNZXNzYWdlRXZlbnRgLlxuICAgKlxuICAgKiBAcGFyYW0ge1N0cmluZ30gdHlwZSBUaGUgbmFtZSBvZiB0aGUgZXZlbnRcbiAgICogQHBhcmFtIHtPYmplY3R9IFtvcHRpb25zXSBBIGRpY3Rpb25hcnkgb2JqZWN0IHRoYXQgYWxsb3dzIGZvciBzZXR0aW5nXG4gICAqICAgICBhdHRyaWJ1dGVzIHZpYSBvYmplY3QgbWVtYmVycyBvZiB0aGUgc2FtZSBuYW1lXG4gICAqIEBwYXJhbSB7Kn0gW29wdGlvbnMuZGF0YT1udWxsXSBUaGUgbWVzc2FnZSBjb250ZW50XG4gICAqL1xuICBjb25zdHJ1Y3Rvcih0eXBlLCBvcHRpb25zID0ge30pIHtcbiAgICBzdXBlcih0eXBlKTtcblxuICAgIHRoaXNba0RhdGFdID0gb3B0aW9ucy5kYXRhID09PSB1bmRlZmluZWQgPyBudWxsIDogb3B0aW9ucy5kYXRhO1xuICB9XG5cbiAgLyoqXG4gICAqIEB0eXBlIHsqfVxuICAgKi9cbiAgZ2V0IGRhdGEoKSB7XG4gICAgcmV0dXJuIHRoaXNba0RhdGFdO1xuICB9XG59XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShNZXNzYWdlRXZlbnQucHJvdG90eXBlLCAnZGF0YScsIHsgZW51bWVyYWJsZTogdHJ1ZSB9KTtcblxuLyoqXG4gKiBUaGlzIHByb3ZpZGVzIG1ldGhvZHMgZm9yIGVtdWxhdGluZyB0aGUgYEV2ZW50VGFyZ2V0YCBpbnRlcmZhY2UuIEl0J3Mgbm90XG4gKiBtZWFudCB0byBiZSB1c2VkIGRpcmVjdGx5LlxuICpcbiAqIEBtaXhpblxuICovXG5jb25zdCBFdmVudFRhcmdldCA9IHtcbiAgLyoqXG4gICAqIFJlZ2lzdGVyIGFuIGV2ZW50IGxpc3RlbmVyLlxuICAgKlxuICAgKiBAcGFyYW0ge1N0cmluZ30gdHlwZSBBIHN0cmluZyByZXByZXNlbnRpbmcgdGhlIGV2ZW50IHR5cGUgdG8gbGlzdGVuIGZvclxuICAgKiBAcGFyYW0geyhGdW5jdGlvbnxPYmplY3QpfSBoYW5kbGVyIFRoZSBsaXN0ZW5lciB0byBhZGRcbiAgICogQHBhcmFtIHtPYmplY3R9IFtvcHRpb25zXSBBbiBvcHRpb25zIG9iamVjdCBzcGVjaWZpZXMgY2hhcmFjdGVyaXN0aWNzIGFib3V0XG4gICAqICAgICB0aGUgZXZlbnQgbGlzdGVuZXJcbiAgICogQHBhcmFtIHtCb29sZWFufSBbb3B0aW9ucy5vbmNlPWZhbHNlXSBBIGBCb29sZWFuYCBpbmRpY2F0aW5nIHRoYXQgdGhlXG4gICAqICAgICBsaXN0ZW5lciBzaG91bGQgYmUgaW52b2tlZCBhdCBtb3N0IG9uY2UgYWZ0ZXIgYmVpbmcgYWRkZWQuIElmIGB0cnVlYCxcbiAgICogICAgIHRoZSBsaXN0ZW5lciB3b3VsZCBiZSBhdXRvbWF0aWNhbGx5IHJlbW92ZWQgd2hlbiBpbnZva2VkLlxuICAgKiBAcHVibGljXG4gICAqL1xuICBhZGRFdmVudExpc3RlbmVyKHR5cGUsIGhhbmRsZXIsIG9wdGlvbnMgPSB7fSkge1xuICAgIGZvciAoY29uc3QgbGlzdGVuZXIgb2YgdGhpcy5saXN0ZW5lcnModHlwZSkpIHtcbiAgICAgIGlmIChcbiAgICAgICAgIW9wdGlvbnNba0Zvck9uRXZlbnRBdHRyaWJ1dGVdICYmXG4gICAgICAgIGxpc3RlbmVyW2tMaXN0ZW5lcl0gPT09IGhhbmRsZXIgJiZcbiAgICAgICAgIWxpc3RlbmVyW2tGb3JPbkV2ZW50QXR0cmlidXRlXVxuICAgICAgKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBsZXQgd3JhcHBlcjtcblxuICAgIGlmICh0eXBlID09PSAnbWVzc2FnZScpIHtcbiAgICAgIHdyYXBwZXIgPSBmdW5jdGlvbiBvbk1lc3NhZ2UoZGF0YSwgaXNCaW5hcnkpIHtcbiAgICAgICAgY29uc3QgZXZlbnQgPSBuZXcgTWVzc2FnZUV2ZW50KCdtZXNzYWdlJywge1xuICAgICAgICAgIGRhdGE6IGlzQmluYXJ5ID8gZGF0YSA6IGRhdGEudG9TdHJpbmcoKVxuICAgICAgICB9KTtcblxuICAgICAgICBldmVudFtrVGFyZ2V0XSA9IHRoaXM7XG4gICAgICAgIGNhbGxMaXN0ZW5lcihoYW5kbGVyLCB0aGlzLCBldmVudCk7XG4gICAgICB9O1xuICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gJ2Nsb3NlJykge1xuICAgICAgd3JhcHBlciA9IGZ1bmN0aW9uIG9uQ2xvc2UoY29kZSwgbWVzc2FnZSkge1xuICAgICAgICBjb25zdCBldmVudCA9IG5ldyBDbG9zZUV2ZW50KCdjbG9zZScsIHtcbiAgICAgICAgICBjb2RlLFxuICAgICAgICAgIHJlYXNvbjogbWVzc2FnZS50b1N0cmluZygpLFxuICAgICAgICAgIHdhc0NsZWFuOiB0aGlzLl9jbG9zZUZyYW1lUmVjZWl2ZWQgJiYgdGhpcy5fY2xvc2VGcmFtZVNlbnRcbiAgICAgICAgfSk7XG5cbiAgICAgICAgZXZlbnRba1RhcmdldF0gPSB0aGlzO1xuICAgICAgICBjYWxsTGlzdGVuZXIoaGFuZGxlciwgdGhpcywgZXZlbnQpO1xuICAgICAgfTtcbiAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICdlcnJvcicpIHtcbiAgICAgIHdyYXBwZXIgPSBmdW5jdGlvbiBvbkVycm9yKGVycm9yKSB7XG4gICAgICAgIGNvbnN0IGV2ZW50ID0gbmV3IEVycm9yRXZlbnQoJ2Vycm9yJywge1xuICAgICAgICAgIGVycm9yLFxuICAgICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2VcbiAgICAgICAgfSk7XG5cbiAgICAgICAgZXZlbnRba1RhcmdldF0gPSB0aGlzO1xuICAgICAgICBjYWxsTGlzdGVuZXIoaGFuZGxlciwgdGhpcywgZXZlbnQpO1xuICAgICAgfTtcbiAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICdvcGVuJykge1xuICAgICAgd3JhcHBlciA9IGZ1bmN0aW9uIG9uT3BlbigpIHtcbiAgICAgICAgY29uc3QgZXZlbnQgPSBuZXcgRXZlbnQoJ29wZW4nKTtcblxuICAgICAgICBldmVudFtrVGFyZ2V0XSA9IHRoaXM7XG4gICAgICAgIGNhbGxMaXN0ZW5lcihoYW5kbGVyLCB0aGlzLCBldmVudCk7XG4gICAgICB9O1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgd3JhcHBlcltrRm9yT25FdmVudEF0dHJpYnV0ZV0gPSAhIW9wdGlvbnNba0Zvck9uRXZlbnRBdHRyaWJ1dGVdO1xuICAgIHdyYXBwZXJba0xpc3RlbmVyXSA9IGhhbmRsZXI7XG5cbiAgICBpZiAob3B0aW9ucy5vbmNlKSB7XG4gICAgICB0aGlzLm9uY2UodHlwZSwgd3JhcHBlcik7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRoaXMub24odHlwZSwgd3JhcHBlcik7XG4gICAgfVxuICB9LFxuXG4gIC8qKlxuICAgKiBSZW1vdmUgYW4gZXZlbnQgbGlzdGVuZXIuXG4gICAqXG4gICAqIEBwYXJhbSB7U3RyaW5nfSB0eXBlIEEgc3RyaW5nIHJlcHJlc2VudGluZyB0aGUgZXZlbnQgdHlwZSB0byByZW1vdmVcbiAgICogQHBhcmFtIHsoRnVuY3Rpb258T2JqZWN0KX0gaGFuZGxlciBUaGUgbGlzdGVuZXIgdG8gcmVtb3ZlXG4gICAqIEBwdWJsaWNcbiAgICovXG4gIHJlbW92ZUV2ZW50TGlzdGVuZXIodHlwZSwgaGFuZGxlcikge1xuICAgIGZvciAoY29uc3QgbGlzdGVuZXIgb2YgdGhpcy5saXN0ZW5lcnModHlwZSkpIHtcbiAgICAgIGlmIChsaXN0ZW5lcltrTGlzdGVuZXJdID09PSBoYW5kbGVyICYmICFsaXN0ZW5lcltrRm9yT25FdmVudEF0dHJpYnV0ZV0pIHtcbiAgICAgICAgdGhpcy5yZW1vdmVMaXN0ZW5lcih0eXBlLCBsaXN0ZW5lcik7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH1cbiAgfVxufTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIENsb3NlRXZlbnQsXG4gIEVycm9yRXZlbnQsXG4gIEV2ZW50LFxuICBFdmVudFRhcmdldCxcbiAgTWVzc2FnZUV2ZW50XG59O1xuXG4vKipcbiAqIENhbGwgYW4gZXZlbnQgbGlzdGVuZXJcbiAqXG4gKiBAcGFyYW0geyhGdW5jdGlvbnxPYmplY3QpfSBsaXN0ZW5lciBUaGUgbGlzdGVuZXIgdG8gY2FsbFxuICogQHBhcmFtIHsqfSB0aGlzQXJnIFRoZSB2YWx1ZSB0byB1c2UgYXMgYHRoaXNgYCB3aGVuIGNhbGxpbmcgdGhlIGxpc3RlbmVyXG4gKiBAcGFyYW0ge0V2ZW50fSBldmVudCBUaGUgZXZlbnQgdG8gcGFzcyB0byB0aGUgbGlzdGVuZXJcbiAqIEBwcml2YXRlXG4gKi9cbmZ1bmN0aW9uIGNhbGxMaXN0ZW5lcihsaXN0ZW5lciwgdGhpc0FyZywgZXZlbnQpIHtcbiAgaWYgKHR5cGVvZiBsaXN0ZW5lciA9PT0gJ29iamVjdCcgJiYgbGlzdGVuZXIuaGFuZGxlRXZlbnQpIHtcbiAgICBsaXN0ZW5lci5oYW5kbGVFdmVudC5jYWxsKGxpc3RlbmVyLCBldmVudCk7XG4gIH0gZWxzZSB7XG4gICAgbGlzdGVuZXIuY2FsbCh0aGlzQXJnLCBldmVudCk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJrRm9yT25FdmVudEF0dHJpYnV0ZSIsImtMaXN0ZW5lciIsInJlcXVpcmUiLCJrQ29kZSIsIlN5bWJvbCIsImtEYXRhIiwia0Vycm9yIiwia01lc3NhZ2UiLCJrUmVhc29uIiwia1RhcmdldCIsImtUeXBlIiwia1dhc0NsZWFuIiwiRXZlbnQiLCJjb25zdHJ1Y3RvciIsInR5cGUiLCJ0YXJnZXQiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsInByb3RvdHlwZSIsImVudW1lcmFibGUiLCJDbG9zZUV2ZW50Iiwib3B0aW9ucyIsImNvZGUiLCJ1bmRlZmluZWQiLCJyZWFzb24iLCJ3YXNDbGVhbiIsIkVycm9yRXZlbnQiLCJlcnJvciIsIm1lc3NhZ2UiLCJNZXNzYWdlRXZlbnQiLCJkYXRhIiwiRXZlbnRUYXJnZXQiLCJhZGRFdmVudExpc3RlbmVyIiwiaGFuZGxlciIsImxpc3RlbmVyIiwibGlzdGVuZXJzIiwid3JhcHBlciIsIm9uTWVzc2FnZSIsImlzQmluYXJ5IiwiZXZlbnQiLCJ0b1N0cmluZyIsImNhbGxMaXN0ZW5lciIsIm9uQ2xvc2UiLCJfY2xvc2VGcmFtZVJlY2VpdmVkIiwiX2Nsb3NlRnJhbWVTZW50Iiwib25FcnJvciIsIm9uT3BlbiIsIm9uY2UiLCJvbiIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJyZW1vdmVMaXN0ZW5lciIsIm1vZHVsZSIsImV4cG9ydHMiLCJ0aGlzQXJnIiwiaGFuZGxlRXZlbnQiLCJjYWxsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/event-target.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/extension.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/extension.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { tokenChars } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n/**\n * Adds an offer to the map of extension offers or a parameter to the map of\n * parameters.\n *\n * @param {Object} dest The map of extension offers or parameters\n * @param {String} name The extension or parameter name\n * @param {(Object|Boolean|String)} elem The extension parameters or the\n *     parameter value\n * @private\n */ function push(dest, name, elem) {\n    if (dest[name] === undefined) dest[name] = [\n        elem\n    ];\n    else dest[name].push(elem);\n}\n/**\n * Parses the `Sec-WebSocket-Extensions` header into an object.\n *\n * @param {String} header The field value of the header\n * @return {Object} The parsed object\n * @public\n */ function parse(header) {\n    const offers = Object.create(null);\n    let params = Object.create(null);\n    let mustUnescape = false;\n    let isEscaping = false;\n    let inQuotes = false;\n    let extensionName;\n    let paramName;\n    let start = -1;\n    let code = -1;\n    let end = -1;\n    let i = 0;\n    for(; i < header.length; i++){\n        code = header.charCodeAt(i);\n        if (extensionName === undefined) {\n            if (end === -1 && tokenChars[code] === 1) {\n                if (start === -1) start = i;\n            } else if (i !== 0 && (code === 0x20 /* ' ' */  || code === 0x09)) {\n                if (end === -1 && start !== -1) end = i;\n            } else if (code === 0x3b /* ';' */  || code === 0x2c /* ',' */ ) {\n                if (start === -1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (end === -1) end = i;\n                const name = header.slice(start, end);\n                if (code === 0x2c) {\n                    push(offers, name, params);\n                    params = Object.create(null);\n                } else {\n                    extensionName = name;\n                }\n                start = end = -1;\n            } else {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n        } else if (paramName === undefined) {\n            if (end === -1 && tokenChars[code] === 1) {\n                if (start === -1) start = i;\n            } else if (code === 0x20 || code === 0x09) {\n                if (end === -1 && start !== -1) end = i;\n            } else if (code === 0x3b || code === 0x2c) {\n                if (start === -1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (end === -1) end = i;\n                push(params, header.slice(start, end), true);\n                if (code === 0x2c) {\n                    push(offers, extensionName, params);\n                    params = Object.create(null);\n                    extensionName = undefined;\n                }\n                start = end = -1;\n            } else if (code === 0x3d /* '=' */  && start !== -1 && end === -1) {\n                paramName = header.slice(start, i);\n                start = end = -1;\n            } else {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n        } else {\n            //\n            // The value of a quoted-string after unescaping must conform to the\n            // token ABNF, so only token characters are valid.\n            // Ref: https://tools.ietf.org/html/rfc6455#section-9.1\n            //\n            if (isEscaping) {\n                if (tokenChars[code] !== 1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (start === -1) start = i;\n                else if (!mustUnescape) mustUnescape = true;\n                isEscaping = false;\n            } else if (inQuotes) {\n                if (tokenChars[code] === 1) {\n                    if (start === -1) start = i;\n                } else if (code === 0x22 /* '\"' */  && start !== -1) {\n                    inQuotes = false;\n                    end = i;\n                } else if (code === 0x5c /* '\\' */ ) {\n                    isEscaping = true;\n                } else {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n            } else if (code === 0x22 && header.charCodeAt(i - 1) === 0x3d) {\n                inQuotes = true;\n            } else if (end === -1 && tokenChars[code] === 1) {\n                if (start === -1) start = i;\n            } else if (start !== -1 && (code === 0x20 || code === 0x09)) {\n                if (end === -1) end = i;\n            } else if (code === 0x3b || code === 0x2c) {\n                if (start === -1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (end === -1) end = i;\n                let value = header.slice(start, end);\n                if (mustUnescape) {\n                    value = value.replace(/\\\\/g, \"\");\n                    mustUnescape = false;\n                }\n                push(params, paramName, value);\n                if (code === 0x2c) {\n                    push(offers, extensionName, params);\n                    params = Object.create(null);\n                    extensionName = undefined;\n                }\n                paramName = undefined;\n                start = end = -1;\n            } else {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n        }\n    }\n    if (start === -1 || inQuotes || code === 0x20 || code === 0x09) {\n        throw new SyntaxError(\"Unexpected end of input\");\n    }\n    if (end === -1) end = i;\n    const token = header.slice(start, end);\n    if (extensionName === undefined) {\n        push(offers, token, params);\n    } else {\n        if (paramName === undefined) {\n            push(params, token, true);\n        } else if (mustUnescape) {\n            push(params, paramName, token.replace(/\\\\/g, \"\"));\n        } else {\n            push(params, paramName, token);\n        }\n        push(offers, extensionName, params);\n    }\n    return offers;\n}\n/**\n * Builds the `Sec-WebSocket-Extensions` header field value.\n *\n * @param {Object} extensions The map of extensions and parameters to format\n * @return {String} A string representing the given object\n * @public\n */ function format(extensions) {\n    return Object.keys(extensions).map((extension)=>{\n        let configurations = extensions[extension];\n        if (!Array.isArray(configurations)) configurations = [\n            configurations\n        ];\n        return configurations.map((params)=>{\n            return [\n                extension\n            ].concat(Object.keys(params).map((k)=>{\n                let values = params[k];\n                if (!Array.isArray(values)) values = [\n                    values\n                ];\n                return values.map((v)=>v === true ? k : `${k}=${v}`).join(\"; \");\n            })).join(\"; \");\n        }).join(\", \");\n    }).join(\", \");\n}\nmodule.exports = {\n    format,\n    parse\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/extension.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/limiter.js":
/*!****************************************!*\
  !*** ./node_modules/ws/lib/limiter.js ***!
  \****************************************/
/***/ ((module) => {

eval("\nconst kDone = Symbol(\"kDone\");\nconst kRun = Symbol(\"kRun\");\n/**\n * A very simple job queue with adjustable concurrency. Adapted from\n * https://github.com/STRML/async-limiter\n */ class Limiter {\n    /**\n   * Creates a new `Limiter`.\n   *\n   * @param {Number} [concurrency=Infinity] The maximum number of jobs allowed\n   *     to run concurrently\n   */ constructor(concurrency){\n        this[kDone] = ()=>{\n            this.pending--;\n            this[kRun]();\n        };\n        this.concurrency = concurrency || Infinity;\n        this.jobs = [];\n        this.pending = 0;\n    }\n    /**\n   * Adds a job to the queue.\n   *\n   * @param {Function} job The job to run\n   * @public\n   */ add(job) {\n        this.jobs.push(job);\n        this[kRun]();\n    }\n    /**\n   * Removes a job from the queue and runs it if possible.\n   *\n   * @private\n   */ [kRun]() {\n        if (this.pending === this.concurrency) return;\n        if (this.jobs.length) {\n            const job = this.jobs.shift();\n            this.pending++;\n            job(this[kDone]);\n        }\n    }\n}\nmodule.exports = Limiter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/limiter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/permessage-deflate.js":
/*!***************************************************!*\
  !*** ./node_modules/ws/lib/permessage-deflate.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst bufferUtil = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst Limiter = __webpack_require__(/*! ./limiter */ \"(ssr)/./node_modules/ws/lib/limiter.js\");\nconst { kStatusCode } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst FastBuffer = Buffer[Symbol.species];\nconst TRAILER = Buffer.from([\n    0x00,\n    0x00,\n    0xff,\n    0xff\n]);\nconst kPerMessageDeflate = Symbol(\"permessage-deflate\");\nconst kTotalLength = Symbol(\"total-length\");\nconst kCallback = Symbol(\"callback\");\nconst kBuffers = Symbol(\"buffers\");\nconst kError = Symbol(\"error\");\n//\n// We limit zlib concurrency, which prevents severe memory fragmentation\n// as documented in https://github.com/nodejs/node/issues/8871#issuecomment-250915913\n// and https://github.com/websockets/ws/issues/1202\n//\n// Intentionally global; it's the global thread pool that's an issue.\n//\nlet zlibLimiter;\n/**\n * permessage-deflate implementation.\n */ class PerMessageDeflate {\n    /**\n   * Creates a PerMessageDeflate instance.\n   *\n   * @param {Object} [options] Configuration options\n   * @param {(Boolean|Number)} [options.clientMaxWindowBits] Advertise support\n   *     for, or request, a custom client window size\n   * @param {Boolean} [options.clientNoContextTakeover=false] Advertise/\n   *     acknowledge disabling of client context takeover\n   * @param {Number} [options.concurrencyLimit=10] The number of concurrent\n   *     calls to zlib\n   * @param {(Boolean|Number)} [options.serverMaxWindowBits] Request/confirm the\n   *     use of a custom server window size\n   * @param {Boolean} [options.serverNoContextTakeover=false] Request/accept\n   *     disabling of server context takeover\n   * @param {Number} [options.threshold=1024] Size (in bytes) below which\n   *     messages should not be compressed if context takeover is disabled\n   * @param {Object} [options.zlibDeflateOptions] Options to pass to zlib on\n   *     deflate\n   * @param {Object} [options.zlibInflateOptions] Options to pass to zlib on\n   *     inflate\n   * @param {Boolean} [isServer=false] Create the instance in either server or\n   *     client mode\n   * @param {Number} [maxPayload=0] The maximum allowed message length\n   */ constructor(options, isServer, maxPayload){\n        this._maxPayload = maxPayload | 0;\n        this._options = options || {};\n        this._threshold = this._options.threshold !== undefined ? this._options.threshold : 1024;\n        this._isServer = !!isServer;\n        this._deflate = null;\n        this._inflate = null;\n        this.params = null;\n        if (!zlibLimiter) {\n            const concurrency = this._options.concurrencyLimit !== undefined ? this._options.concurrencyLimit : 10;\n            zlibLimiter = new Limiter(concurrency);\n        }\n    }\n    /**\n   * @type {String}\n   */ static get extensionName() {\n        return \"permessage-deflate\";\n    }\n    /**\n   * Create an extension negotiation offer.\n   *\n   * @return {Object} Extension parameters\n   * @public\n   */ offer() {\n        const params = {};\n        if (this._options.serverNoContextTakeover) {\n            params.server_no_context_takeover = true;\n        }\n        if (this._options.clientNoContextTakeover) {\n            params.client_no_context_takeover = true;\n        }\n        if (this._options.serverMaxWindowBits) {\n            params.server_max_window_bits = this._options.serverMaxWindowBits;\n        }\n        if (this._options.clientMaxWindowBits) {\n            params.client_max_window_bits = this._options.clientMaxWindowBits;\n        } else if (this._options.clientMaxWindowBits == null) {\n            params.client_max_window_bits = true;\n        }\n        return params;\n    }\n    /**\n   * Accept an extension negotiation offer/response.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Object} Accepted configuration\n   * @public\n   */ accept(configurations) {\n        configurations = this.normalizeParams(configurations);\n        this.params = this._isServer ? this.acceptAsServer(configurations) : this.acceptAsClient(configurations);\n        return this.params;\n    }\n    /**\n   * Releases all resources used by the extension.\n   *\n   * @public\n   */ cleanup() {\n        if (this._inflate) {\n            this._inflate.close();\n            this._inflate = null;\n        }\n        if (this._deflate) {\n            const callback = this._deflate[kCallback];\n            this._deflate.close();\n            this._deflate = null;\n            if (callback) {\n                callback(new Error(\"The deflate stream was closed while data was being processed\"));\n            }\n        }\n    }\n    /**\n   *  Accept an extension negotiation offer.\n   *\n   * @param {Array} offers The extension negotiation offers\n   * @return {Object} Accepted configuration\n   * @private\n   */ acceptAsServer(offers) {\n        const opts = this._options;\n        const accepted = offers.find((params)=>{\n            if (opts.serverNoContextTakeover === false && params.server_no_context_takeover || params.server_max_window_bits && (opts.serverMaxWindowBits === false || typeof opts.serverMaxWindowBits === \"number\" && opts.serverMaxWindowBits > params.server_max_window_bits) || typeof opts.clientMaxWindowBits === \"number\" && !params.client_max_window_bits) {\n                return false;\n            }\n            return true;\n        });\n        if (!accepted) {\n            throw new Error(\"None of the extension offers can be accepted\");\n        }\n        if (opts.serverNoContextTakeover) {\n            accepted.server_no_context_takeover = true;\n        }\n        if (opts.clientNoContextTakeover) {\n            accepted.client_no_context_takeover = true;\n        }\n        if (typeof opts.serverMaxWindowBits === \"number\") {\n            accepted.server_max_window_bits = opts.serverMaxWindowBits;\n        }\n        if (typeof opts.clientMaxWindowBits === \"number\") {\n            accepted.client_max_window_bits = opts.clientMaxWindowBits;\n        } else if (accepted.client_max_window_bits === true || opts.clientMaxWindowBits === false) {\n            delete accepted.client_max_window_bits;\n        }\n        return accepted;\n    }\n    /**\n   * Accept the extension negotiation response.\n   *\n   * @param {Array} response The extension negotiation response\n   * @return {Object} Accepted configuration\n   * @private\n   */ acceptAsClient(response) {\n        const params = response[0];\n        if (this._options.clientNoContextTakeover === false && params.client_no_context_takeover) {\n            throw new Error('Unexpected parameter \"client_no_context_takeover\"');\n        }\n        if (!params.client_max_window_bits) {\n            if (typeof this._options.clientMaxWindowBits === \"number\") {\n                params.client_max_window_bits = this._options.clientMaxWindowBits;\n            }\n        } else if (this._options.clientMaxWindowBits === false || typeof this._options.clientMaxWindowBits === \"number\" && params.client_max_window_bits > this._options.clientMaxWindowBits) {\n            throw new Error('Unexpected or invalid parameter \"client_max_window_bits\"');\n        }\n        return params;\n    }\n    /**\n   * Normalize parameters.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Array} The offers/response with normalized parameters\n   * @private\n   */ normalizeParams(configurations) {\n        configurations.forEach((params)=>{\n            Object.keys(params).forEach((key)=>{\n                let value = params[key];\n                if (value.length > 1) {\n                    throw new Error(`Parameter \"${key}\" must have only a single value`);\n                }\n                value = value[0];\n                if (key === \"client_max_window_bits\") {\n                    if (value !== true) {\n                        const num = +value;\n                        if (!Number.isInteger(num) || num < 8 || num > 15) {\n                            throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                        }\n                        value = num;\n                    } else if (!this._isServer) {\n                        throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                    }\n                } else if (key === \"server_max_window_bits\") {\n                    const num = +value;\n                    if (!Number.isInteger(num) || num < 8 || num > 15) {\n                        throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                    }\n                    value = num;\n                } else if (key === \"client_no_context_takeover\" || key === \"server_no_context_takeover\") {\n                    if (value !== true) {\n                        throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                    }\n                } else {\n                    throw new Error(`Unknown parameter \"${key}\"`);\n                }\n                params[key] = value;\n            });\n        });\n        return configurations;\n    }\n    /**\n   * Decompress data. Concurrency limited.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */ decompress(data, fin, callback) {\n        zlibLimiter.add((done)=>{\n            this._decompress(data, fin, (err, result)=>{\n                done();\n                callback(err, result);\n            });\n        });\n    }\n    /**\n   * Compress data. Concurrency limited.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */ compress(data, fin, callback) {\n        zlibLimiter.add((done)=>{\n            this._compress(data, fin, (err, result)=>{\n                done();\n                callback(err, result);\n            });\n        });\n    }\n    /**\n   * Decompress data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */ _decompress(data, fin, callback) {\n        const endpoint = this._isServer ? \"client\" : \"server\";\n        if (!this._inflate) {\n            const key = `${endpoint}_max_window_bits`;\n            const windowBits = typeof this.params[key] !== \"number\" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n            this._inflate = zlib.createInflateRaw({\n                ...this._options.zlibInflateOptions,\n                windowBits\n            });\n            this._inflate[kPerMessageDeflate] = this;\n            this._inflate[kTotalLength] = 0;\n            this._inflate[kBuffers] = [];\n            this._inflate.on(\"error\", inflateOnError);\n            this._inflate.on(\"data\", inflateOnData);\n        }\n        this._inflate[kCallback] = callback;\n        this._inflate.write(data);\n        if (fin) this._inflate.write(TRAILER);\n        this._inflate.flush(()=>{\n            const err = this._inflate[kError];\n            if (err) {\n                this._inflate.close();\n                this._inflate = null;\n                callback(err);\n                return;\n            }\n            const data = bufferUtil.concat(this._inflate[kBuffers], this._inflate[kTotalLength]);\n            if (this._inflate._readableState.endEmitted) {\n                this._inflate.close();\n                this._inflate = null;\n            } else {\n                this._inflate[kTotalLength] = 0;\n                this._inflate[kBuffers] = [];\n                if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n                    this._inflate.reset();\n                }\n            }\n            callback(null, data);\n        });\n    }\n    /**\n   * Compress data.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */ _compress(data, fin, callback) {\n        const endpoint = this._isServer ? \"server\" : \"client\";\n        if (!this._deflate) {\n            const key = `${endpoint}_max_window_bits`;\n            const windowBits = typeof this.params[key] !== \"number\" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n            this._deflate = zlib.createDeflateRaw({\n                ...this._options.zlibDeflateOptions,\n                windowBits\n            });\n            this._deflate[kTotalLength] = 0;\n            this._deflate[kBuffers] = [];\n            this._deflate.on(\"data\", deflateOnData);\n        }\n        this._deflate[kCallback] = callback;\n        this._deflate.write(data);\n        this._deflate.flush(zlib.Z_SYNC_FLUSH, ()=>{\n            if (!this._deflate) {\n                //\n                // The deflate stream was closed while data was being processed.\n                //\n                return;\n            }\n            let data = bufferUtil.concat(this._deflate[kBuffers], this._deflate[kTotalLength]);\n            if (fin) {\n                data = new FastBuffer(data.buffer, data.byteOffset, data.length - 4);\n            }\n            //\n            // Ensure that the callback will not be called again in\n            // `PerMessageDeflate#cleanup()`.\n            //\n            this._deflate[kCallback] = null;\n            this._deflate[kTotalLength] = 0;\n            this._deflate[kBuffers] = [];\n            if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n                this._deflate.reset();\n            }\n            callback(null, data);\n        });\n    }\n}\nmodule.exports = PerMessageDeflate;\n/**\n * The listener of the `zlib.DeflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */ function deflateOnData(chunk) {\n    this[kBuffers].push(chunk);\n    this[kTotalLength] += chunk.length;\n}\n/**\n * The listener of the `zlib.InflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */ function inflateOnData(chunk) {\n    this[kTotalLength] += chunk.length;\n    if (this[kPerMessageDeflate]._maxPayload < 1 || this[kTotalLength] <= this[kPerMessageDeflate]._maxPayload) {\n        this[kBuffers].push(chunk);\n        return;\n    }\n    this[kError] = new RangeError(\"Max payload size exceeded\");\n    this[kError].code = \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\";\n    this[kError][kStatusCode] = 1009;\n    this.removeListener(\"data\", inflateOnData);\n    //\n    // The choice to employ `zlib.reset()` over `zlib.close()` is dictated by the\n    // fact that in Node.js versions prior to 13.10.0, the callback for\n    // `zlib.flush()` is not called if `zlib.close()` is used. Utilizing\n    // `zlib.reset()` ensures that either the callback is invoked or an error is\n    // emitted.\n    //\n    this.reset();\n}\n/**\n * The listener of the `zlib.InflateRaw` stream `'error'` event.\n *\n * @param {Error} err The emitted error\n * @private\n */ function inflateOnError(err) {\n    //\n    // There is no need to call `Zlib#close()` as the handle is automatically\n    // closed when an error is emitted.\n    //\n    this[kPerMessageDeflate]._inflate = null;\n    if (this[kError]) {\n        this[kCallback](this[kError]);\n        return;\n    }\n    err[kStatusCode] = 1007;\n    this[kCallback](err);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/permessage-deflate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/receiver.js":
/*!*****************************************!*\
  !*** ./node_modules/ws/lib/receiver.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { Writable } = __webpack_require__(/*! stream */ \"stream\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst { BINARY_TYPES, EMPTY_BUFFER, kStatusCode, kWebSocket } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { concat, toArrayBuffer, unmask } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst { isValidStatusCode, isValidUTF8 } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst FastBuffer = Buffer[Symbol.species];\nconst GET_INFO = 0;\nconst GET_PAYLOAD_LENGTH_16 = 1;\nconst GET_PAYLOAD_LENGTH_64 = 2;\nconst GET_MASK = 3;\nconst GET_DATA = 4;\nconst INFLATING = 5;\nconst DEFER_EVENT = 6;\n/**\n * HyBi Receiver implementation.\n *\n * @extends Writable\n */ class Receiver extends Writable {\n    /**\n   * Creates a Receiver instance.\n   *\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {String} [options.binaryType=nodebuffer] The type for binary data\n   * @param {Object} [options.extensions] An object containing the negotiated\n   *     extensions\n   * @param {Boolean} [options.isServer=false] Specifies whether to operate in\n   *     client or server mode\n   * @param {Number} [options.maxPayload=0] The maximum allowed message length\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   */ constructor(options = {}){\n        super();\n        this._allowSynchronousEvents = options.allowSynchronousEvents !== undefined ? options.allowSynchronousEvents : true;\n        this._binaryType = options.binaryType || BINARY_TYPES[0];\n        this._extensions = options.extensions || {};\n        this._isServer = !!options.isServer;\n        this._maxPayload = options.maxPayload | 0;\n        this._skipUTF8Validation = !!options.skipUTF8Validation;\n        this[kWebSocket] = undefined;\n        this._bufferedBytes = 0;\n        this._buffers = [];\n        this._compressed = false;\n        this._payloadLength = 0;\n        this._mask = undefined;\n        this._fragmented = 0;\n        this._masked = false;\n        this._fin = false;\n        this._opcode = 0;\n        this._totalPayloadLength = 0;\n        this._messageLength = 0;\n        this._fragments = [];\n        this._errored = false;\n        this._loop = false;\n        this._state = GET_INFO;\n    }\n    /**\n   * Implements `Writable.prototype._write()`.\n   *\n   * @param {Buffer} chunk The chunk of data to write\n   * @param {String} encoding The character encoding of `chunk`\n   * @param {Function} cb Callback\n   * @private\n   */ _write(chunk, encoding, cb) {\n        if (this._opcode === 0x08 && this._state == GET_INFO) return cb();\n        this._bufferedBytes += chunk.length;\n        this._buffers.push(chunk);\n        this.startLoop(cb);\n    }\n    /**\n   * Consumes `n` bytes from the buffered data.\n   *\n   * @param {Number} n The number of bytes to consume\n   * @return {Buffer} The consumed bytes\n   * @private\n   */ consume(n) {\n        this._bufferedBytes -= n;\n        if (n === this._buffers[0].length) return this._buffers.shift();\n        if (n < this._buffers[0].length) {\n            const buf = this._buffers[0];\n            this._buffers[0] = new FastBuffer(buf.buffer, buf.byteOffset + n, buf.length - n);\n            return new FastBuffer(buf.buffer, buf.byteOffset, n);\n        }\n        const dst = Buffer.allocUnsafe(n);\n        do {\n            const buf = this._buffers[0];\n            const offset = dst.length - n;\n            if (n >= buf.length) {\n                dst.set(this._buffers.shift(), offset);\n            } else {\n                dst.set(new Uint8Array(buf.buffer, buf.byteOffset, n), offset);\n                this._buffers[0] = new FastBuffer(buf.buffer, buf.byteOffset + n, buf.length - n);\n            }\n            n -= buf.length;\n        }while (n > 0);\n        return dst;\n    }\n    /**\n   * Starts the parsing loop.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ startLoop(cb) {\n        this._loop = true;\n        do {\n            switch(this._state){\n                case GET_INFO:\n                    this.getInfo(cb);\n                    break;\n                case GET_PAYLOAD_LENGTH_16:\n                    this.getPayloadLength16(cb);\n                    break;\n                case GET_PAYLOAD_LENGTH_64:\n                    this.getPayloadLength64(cb);\n                    break;\n                case GET_MASK:\n                    this.getMask();\n                    break;\n                case GET_DATA:\n                    this.getData(cb);\n                    break;\n                case INFLATING:\n                case DEFER_EVENT:\n                    this._loop = false;\n                    return;\n            }\n        }while (this._loop);\n        if (!this._errored) cb();\n    }\n    /**\n   * Reads the first two bytes of a frame.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getInfo(cb) {\n        if (this._bufferedBytes < 2) {\n            this._loop = false;\n            return;\n        }\n        const buf = this.consume(2);\n        if ((buf[0] & 0x30) !== 0x00) {\n            const error = this.createError(RangeError, \"RSV2 and RSV3 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_2_3\");\n            cb(error);\n            return;\n        }\n        const compressed = (buf[0] & 0x40) === 0x40;\n        if (compressed && !this._extensions[PerMessageDeflate.extensionName]) {\n            const error = this.createError(RangeError, \"RSV1 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_1\");\n            cb(error);\n            return;\n        }\n        this._fin = (buf[0] & 0x80) === 0x80;\n        this._opcode = buf[0] & 0x0f;\n        this._payloadLength = buf[1] & 0x7f;\n        if (this._opcode === 0x00) {\n            if (compressed) {\n                const error = this.createError(RangeError, \"RSV1 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_1\");\n                cb(error);\n                return;\n            }\n            if (!this._fragmented) {\n                const error = this.createError(RangeError, \"invalid opcode 0\", true, 1002, \"WS_ERR_INVALID_OPCODE\");\n                cb(error);\n                return;\n            }\n            this._opcode = this._fragmented;\n        } else if (this._opcode === 0x01 || this._opcode === 0x02) {\n            if (this._fragmented) {\n                const error = this.createError(RangeError, `invalid opcode ${this._opcode}`, true, 1002, \"WS_ERR_INVALID_OPCODE\");\n                cb(error);\n                return;\n            }\n            this._compressed = compressed;\n        } else if (this._opcode > 0x07 && this._opcode < 0x0b) {\n            if (!this._fin) {\n                const error = this.createError(RangeError, \"FIN must be set\", true, 1002, \"WS_ERR_EXPECTED_FIN\");\n                cb(error);\n                return;\n            }\n            if (compressed) {\n                const error = this.createError(RangeError, \"RSV1 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_1\");\n                cb(error);\n                return;\n            }\n            if (this._payloadLength > 0x7d || this._opcode === 0x08 && this._payloadLength === 1) {\n                const error = this.createError(RangeError, `invalid payload length ${this._payloadLength}`, true, 1002, \"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH\");\n                cb(error);\n                return;\n            }\n        } else {\n            const error = this.createError(RangeError, `invalid opcode ${this._opcode}`, true, 1002, \"WS_ERR_INVALID_OPCODE\");\n            cb(error);\n            return;\n        }\n        if (!this._fin && !this._fragmented) this._fragmented = this._opcode;\n        this._masked = (buf[1] & 0x80) === 0x80;\n        if (this._isServer) {\n            if (!this._masked) {\n                const error = this.createError(RangeError, \"MASK must be set\", true, 1002, \"WS_ERR_EXPECTED_MASK\");\n                cb(error);\n                return;\n            }\n        } else if (this._masked) {\n            const error = this.createError(RangeError, \"MASK must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_MASK\");\n            cb(error);\n            return;\n        }\n        if (this._payloadLength === 126) this._state = GET_PAYLOAD_LENGTH_16;\n        else if (this._payloadLength === 127) this._state = GET_PAYLOAD_LENGTH_64;\n        else this.haveLength(cb);\n    }\n    /**\n   * Gets extended payload length (7+16).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getPayloadLength16(cb) {\n        if (this._bufferedBytes < 2) {\n            this._loop = false;\n            return;\n        }\n        this._payloadLength = this.consume(2).readUInt16BE(0);\n        this.haveLength(cb);\n    }\n    /**\n   * Gets extended payload length (7+64).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getPayloadLength64(cb) {\n        if (this._bufferedBytes < 8) {\n            this._loop = false;\n            return;\n        }\n        const buf = this.consume(8);\n        const num = buf.readUInt32BE(0);\n        //\n        // The maximum safe integer in JavaScript is 2^53 - 1. An error is returned\n        // if payload length is greater than this number.\n        //\n        if (num > Math.pow(2, 53 - 32) - 1) {\n            const error = this.createError(RangeError, \"Unsupported WebSocket frame: payload length > 2^53 - 1\", false, 1009, \"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH\");\n            cb(error);\n            return;\n        }\n        this._payloadLength = num * Math.pow(2, 32) + buf.readUInt32BE(4);\n        this.haveLength(cb);\n    }\n    /**\n   * Payload length has been read.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ haveLength(cb) {\n        if (this._payloadLength && this._opcode < 0x08) {\n            this._totalPayloadLength += this._payloadLength;\n            if (this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {\n                const error = this.createError(RangeError, \"Max payload size exceeded\", false, 1009, \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\");\n                cb(error);\n                return;\n            }\n        }\n        if (this._masked) this._state = GET_MASK;\n        else this._state = GET_DATA;\n    }\n    /**\n   * Reads mask bytes.\n   *\n   * @private\n   */ getMask() {\n        if (this._bufferedBytes < 4) {\n            this._loop = false;\n            return;\n        }\n        this._mask = this.consume(4);\n        this._state = GET_DATA;\n    }\n    /**\n   * Reads data bytes.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getData(cb) {\n        let data = EMPTY_BUFFER;\n        if (this._payloadLength) {\n            if (this._bufferedBytes < this._payloadLength) {\n                this._loop = false;\n                return;\n            }\n            data = this.consume(this._payloadLength);\n            if (this._masked && (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0) {\n                unmask(data, this._mask);\n            }\n        }\n        if (this._opcode > 0x07) {\n            this.controlMessage(data, cb);\n            return;\n        }\n        if (this._compressed) {\n            this._state = INFLATING;\n            this.decompress(data, cb);\n            return;\n        }\n        if (data.length) {\n            //\n            // This message is not compressed so its length is the sum of the payload\n            // length of all fragments.\n            //\n            this._messageLength = this._totalPayloadLength;\n            this._fragments.push(data);\n        }\n        this.dataMessage(cb);\n    }\n    /**\n   * Decompresses data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Function} cb Callback\n   * @private\n   */ decompress(data, cb) {\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        perMessageDeflate.decompress(data, this._fin, (err, buf)=>{\n            if (err) return cb(err);\n            if (buf.length) {\n                this._messageLength += buf.length;\n                if (this._messageLength > this._maxPayload && this._maxPayload > 0) {\n                    const error = this.createError(RangeError, \"Max payload size exceeded\", false, 1009, \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\");\n                    cb(error);\n                    return;\n                }\n                this._fragments.push(buf);\n            }\n            this.dataMessage(cb);\n            if (this._state === GET_INFO) this.startLoop(cb);\n        });\n    }\n    /**\n   * Handles a data message.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ dataMessage(cb) {\n        if (!this._fin) {\n            this._state = GET_INFO;\n            return;\n        }\n        const messageLength = this._messageLength;\n        const fragments = this._fragments;\n        this._totalPayloadLength = 0;\n        this._messageLength = 0;\n        this._fragmented = 0;\n        this._fragments = [];\n        if (this._opcode === 2) {\n            let data;\n            if (this._binaryType === \"nodebuffer\") {\n                data = concat(fragments, messageLength);\n            } else if (this._binaryType === \"arraybuffer\") {\n                data = toArrayBuffer(concat(fragments, messageLength));\n            } else if (this._binaryType === \"blob\") {\n                data = new Blob(fragments);\n            } else {\n                data = fragments;\n            }\n            if (this._allowSynchronousEvents) {\n                this.emit(\"message\", data, true);\n                this._state = GET_INFO;\n            } else {\n                this._state = DEFER_EVENT;\n                setImmediate(()=>{\n                    this.emit(\"message\", data, true);\n                    this._state = GET_INFO;\n                    this.startLoop(cb);\n                });\n            }\n        } else {\n            const buf = concat(fragments, messageLength);\n            if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n                const error = this.createError(Error, \"invalid UTF-8 sequence\", true, 1007, \"WS_ERR_INVALID_UTF8\");\n                cb(error);\n                return;\n            }\n            if (this._state === INFLATING || this._allowSynchronousEvents) {\n                this.emit(\"message\", buf, false);\n                this._state = GET_INFO;\n            } else {\n                this._state = DEFER_EVENT;\n                setImmediate(()=>{\n                    this.emit(\"message\", buf, false);\n                    this._state = GET_INFO;\n                    this.startLoop(cb);\n                });\n            }\n        }\n    }\n    /**\n   * Handles a control message.\n   *\n   * @param {Buffer} data Data to handle\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */ controlMessage(data, cb) {\n        if (this._opcode === 0x08) {\n            if (data.length === 0) {\n                this._loop = false;\n                this.emit(\"conclude\", 1005, EMPTY_BUFFER);\n                this.end();\n            } else {\n                const code = data.readUInt16BE(0);\n                if (!isValidStatusCode(code)) {\n                    const error = this.createError(RangeError, `invalid status code ${code}`, true, 1002, \"WS_ERR_INVALID_CLOSE_CODE\");\n                    cb(error);\n                    return;\n                }\n                const buf = new FastBuffer(data.buffer, data.byteOffset + 2, data.length - 2);\n                if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n                    const error = this.createError(Error, \"invalid UTF-8 sequence\", true, 1007, \"WS_ERR_INVALID_UTF8\");\n                    cb(error);\n                    return;\n                }\n                this._loop = false;\n                this.emit(\"conclude\", code, buf);\n                this.end();\n            }\n            this._state = GET_INFO;\n            return;\n        }\n        if (this._allowSynchronousEvents) {\n            this.emit(this._opcode === 0x09 ? \"ping\" : \"pong\", data);\n            this._state = GET_INFO;\n        } else {\n            this._state = DEFER_EVENT;\n            setImmediate(()=>{\n                this.emit(this._opcode === 0x09 ? \"ping\" : \"pong\", data);\n                this._state = GET_INFO;\n                this.startLoop(cb);\n            });\n        }\n    }\n    /**\n   * Builds an error object.\n   *\n   * @param {function(new:Error|RangeError)} ErrorCtor The error constructor\n   * @param {String} message The error message\n   * @param {Boolean} prefix Specifies whether or not to add a default prefix to\n   *     `message`\n   * @param {Number} statusCode The status code\n   * @param {String} errorCode The exposed error code\n   * @return {(Error|RangeError)} The error\n   * @private\n   */ createError(ErrorCtor, message, prefix, statusCode, errorCode) {\n        this._loop = false;\n        this._errored = true;\n        const err = new ErrorCtor(prefix ? `Invalid WebSocket frame: ${message}` : message);\n        Error.captureStackTrace(err, this.createError);\n        err.code = errorCode;\n        err[kStatusCode] = statusCode;\n        return err;\n    }\n}\nmodule.exports = Receiver;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/receiver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/sender.js":
/*!***************************************!*\
  !*** ./node_modules/ws/lib/sender.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex\" }] */ \nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\nconst { randomFillSync } = __webpack_require__(/*! crypto */ \"crypto\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst { EMPTY_BUFFER, kWebSocket, NOOP } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { isBlob, isValidStatusCode } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst { mask: applyMask, toBuffer } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst kByteLength = Symbol(\"kByteLength\");\nconst maskBuffer = Buffer.alloc(4);\nconst RANDOM_POOL_SIZE = 8 * 1024;\nlet randomPool;\nlet randomPoolPointer = RANDOM_POOL_SIZE;\nconst DEFAULT = 0;\nconst DEFLATING = 1;\nconst GET_BLOB_DATA = 2;\n/**\n * HyBi Sender implementation.\n */ class Sender {\n    /**\n   * Creates a Sender instance.\n   *\n   * @param {Duplex} socket The connection socket\n   * @param {Object} [extensions] An object containing the negotiated extensions\n   * @param {Function} [generateMask] The function used to generate the masking\n   *     key\n   */ constructor(socket, extensions, generateMask){\n        this._extensions = extensions || {};\n        if (generateMask) {\n            this._generateMask = generateMask;\n            this._maskBuffer = Buffer.alloc(4);\n        }\n        this._socket = socket;\n        this._firstFragment = true;\n        this._compress = false;\n        this._bufferedBytes = 0;\n        this._queue = [];\n        this._state = DEFAULT;\n        this.onerror = NOOP;\n        this[kWebSocket] = undefined;\n    }\n    /**\n   * Frames a piece of data according to the HyBi WebSocket protocol.\n   *\n   * @param {(Buffer|String)} data The data to frame\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @return {(Buffer|String)[]} The framed data\n   * @public\n   */ static frame(data, options) {\n        let mask;\n        let merge = false;\n        let offset = 2;\n        let skipMasking = false;\n        if (options.mask) {\n            mask = options.maskBuffer || maskBuffer;\n            if (options.generateMask) {\n                options.generateMask(mask);\n            } else {\n                if (randomPoolPointer === RANDOM_POOL_SIZE) {\n                    /* istanbul ignore else  */ if (randomPool === undefined) {\n                        //\n                        // This is lazily initialized because server-sent frames must not\n                        // be masked so it may never be used.\n                        //\n                        randomPool = Buffer.alloc(RANDOM_POOL_SIZE);\n                    }\n                    randomFillSync(randomPool, 0, RANDOM_POOL_SIZE);\n                    randomPoolPointer = 0;\n                }\n                mask[0] = randomPool[randomPoolPointer++];\n                mask[1] = randomPool[randomPoolPointer++];\n                mask[2] = randomPool[randomPoolPointer++];\n                mask[3] = randomPool[randomPoolPointer++];\n            }\n            skipMasking = (mask[0] | mask[1] | mask[2] | mask[3]) === 0;\n            offset = 6;\n        }\n        let dataLength;\n        if (typeof data === \"string\") {\n            if ((!options.mask || skipMasking) && options[kByteLength] !== undefined) {\n                dataLength = options[kByteLength];\n            } else {\n                data = Buffer.from(data);\n                dataLength = data.length;\n            }\n        } else {\n            dataLength = data.length;\n            merge = options.mask && options.readOnly && !skipMasking;\n        }\n        let payloadLength = dataLength;\n        if (dataLength >= 65536) {\n            offset += 8;\n            payloadLength = 127;\n        } else if (dataLength > 125) {\n            offset += 2;\n            payloadLength = 126;\n        }\n        const target = Buffer.allocUnsafe(merge ? dataLength + offset : offset);\n        target[0] = options.fin ? options.opcode | 0x80 : options.opcode;\n        if (options.rsv1) target[0] |= 0x40;\n        target[1] = payloadLength;\n        if (payloadLength === 126) {\n            target.writeUInt16BE(dataLength, 2);\n        } else if (payloadLength === 127) {\n            target[2] = target[3] = 0;\n            target.writeUIntBE(dataLength, 4, 6);\n        }\n        if (!options.mask) return [\n            target,\n            data\n        ];\n        target[1] |= 0x80;\n        target[offset - 4] = mask[0];\n        target[offset - 3] = mask[1];\n        target[offset - 2] = mask[2];\n        target[offset - 1] = mask[3];\n        if (skipMasking) return [\n            target,\n            data\n        ];\n        if (merge) {\n            applyMask(data, mask, target, offset, dataLength);\n            return [\n                target\n            ];\n        }\n        applyMask(data, mask, data, 0, dataLength);\n        return [\n            target,\n            data\n        ];\n    }\n    /**\n   * Sends a close message to the other peer.\n   *\n   * @param {Number} [code] The status code component of the body\n   * @param {(String|Buffer)} [data] The message component of the body\n   * @param {Boolean} [mask=false] Specifies whether or not to mask the message\n   * @param {Function} [cb] Callback\n   * @public\n   */ close(code, data, mask, cb) {\n        let buf;\n        if (code === undefined) {\n            buf = EMPTY_BUFFER;\n        } else if (typeof code !== \"number\" || !isValidStatusCode(code)) {\n            throw new TypeError(\"First argument must be a valid error code number\");\n        } else if (data === undefined || !data.length) {\n            buf = Buffer.allocUnsafe(2);\n            buf.writeUInt16BE(code, 0);\n        } else {\n            const length = Buffer.byteLength(data);\n            if (length > 123) {\n                throw new RangeError(\"The message must not be greater than 123 bytes\");\n            }\n            buf = Buffer.allocUnsafe(2 + length);\n            buf.writeUInt16BE(code, 0);\n            if (typeof data === \"string\") {\n                buf.write(data, 2);\n            } else {\n                buf.set(data, 2);\n            }\n        }\n        const options = {\n            [kByteLength]: buf.length,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x08,\n            readOnly: false,\n            rsv1: false\n        };\n        if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                buf,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(buf, options), cb);\n        }\n    }\n    /**\n   * Sends a ping message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ ping(data, mask, cb) {\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (byteLength > 125) {\n            throw new RangeError(\"The data size must not be greater than 125 bytes\");\n        }\n        const options = {\n            [kByteLength]: byteLength,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x09,\n            readOnly,\n            rsv1: false\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    false,\n                    options,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, false, options, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(data, options), cb);\n        }\n    }\n    /**\n   * Sends a pong message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ pong(data, mask, cb) {\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (byteLength > 125) {\n            throw new RangeError(\"The data size must not be greater than 125 bytes\");\n        }\n        const options = {\n            [kByteLength]: byteLength,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x0a,\n            readOnly,\n            rsv1: false\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    false,\n                    options,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, false, options, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(data, options), cb);\n        }\n    }\n    /**\n   * Sends a data message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Object} options Options object\n   * @param {Boolean} [options.binary=false] Specifies whether `data` is binary\n   *     or text\n   * @param {Boolean} [options.compress=false] Specifies whether or not to\n   *     compress `data`\n   * @param {Boolean} [options.fin=false] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ send(data, options, cb) {\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        let opcode = options.binary ? 2 : 1;\n        let rsv1 = options.compress;\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (this._firstFragment) {\n            this._firstFragment = false;\n            if (rsv1 && perMessageDeflate && perMessageDeflate.params[perMessageDeflate._isServer ? \"server_no_context_takeover\" : \"client_no_context_takeover\"]) {\n                rsv1 = byteLength >= perMessageDeflate._threshold;\n            }\n            this._compress = rsv1;\n        } else {\n            rsv1 = false;\n            opcode = 0;\n        }\n        if (options.fin) this._firstFragment = true;\n        const opts = {\n            [kByteLength]: byteLength,\n            fin: options.fin,\n            generateMask: this._generateMask,\n            mask: options.mask,\n            maskBuffer: this._maskBuffer,\n            opcode,\n            readOnly,\n            rsv1\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    this._compress,\n                    opts,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, this._compress, opts, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                this._compress,\n                opts,\n                cb\n            ]);\n        } else {\n            this.dispatch(data, this._compress, opts, cb);\n        }\n    }\n    /**\n   * Gets the contents of a blob as binary data.\n   *\n   * @param {Blob} blob The blob\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     the data\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */ getBlobData(blob, compress, options, cb) {\n        this._bufferedBytes += options[kByteLength];\n        this._state = GET_BLOB_DATA;\n        blob.arrayBuffer().then((arrayBuffer)=>{\n            if (this._socket.destroyed) {\n                const err = new Error(\"The socket was closed while the blob was being read\");\n                //\n                // `callCallbacks` is called in the next tick to ensure that errors\n                // that might be thrown in the callbacks behave like errors thrown\n                // outside the promise chain.\n                //\n                process.nextTick(callCallbacks, this, err, cb);\n                return;\n            }\n            this._bufferedBytes -= options[kByteLength];\n            const data = toBuffer(arrayBuffer);\n            if (!compress) {\n                this._state = DEFAULT;\n                this.sendFrame(Sender.frame(data, options), cb);\n                this.dequeue();\n            } else {\n                this.dispatch(data, compress, options, cb);\n            }\n        }).catch((err)=>{\n            //\n            // `onError` is called in the next tick for the same reason that\n            // `callCallbacks` above is.\n            //\n            process.nextTick(onError, this, err, cb);\n        });\n    }\n    /**\n   * Dispatches a message.\n   *\n   * @param {(Buffer|String)} data The message to send\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     `data`\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */ dispatch(data, compress, options, cb) {\n        if (!compress) {\n            this.sendFrame(Sender.frame(data, options), cb);\n            return;\n        }\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        this._bufferedBytes += options[kByteLength];\n        this._state = DEFLATING;\n        perMessageDeflate.compress(data, options.fin, (_, buf)=>{\n            if (this._socket.destroyed) {\n                const err = new Error(\"The socket was closed while data was being compressed\");\n                callCallbacks(this, err, cb);\n                return;\n            }\n            this._bufferedBytes -= options[kByteLength];\n            this._state = DEFAULT;\n            options.readOnly = false;\n            this.sendFrame(Sender.frame(buf, options), cb);\n            this.dequeue();\n        });\n    }\n    /**\n   * Executes queued send operations.\n   *\n   * @private\n   */ dequeue() {\n        while(this._state === DEFAULT && this._queue.length){\n            const params = this._queue.shift();\n            this._bufferedBytes -= params[3][kByteLength];\n            Reflect.apply(params[0], this, params.slice(1));\n        }\n    }\n    /**\n   * Enqueues a send operation.\n   *\n   * @param {Array} params Send operation parameters.\n   * @private\n   */ enqueue(params) {\n        this._bufferedBytes += params[3][kByteLength];\n        this._queue.push(params);\n    }\n    /**\n   * Sends a frame.\n   *\n   * @param {(Buffer | String)[]} list The frame to send\n   * @param {Function} [cb] Callback\n   * @private\n   */ sendFrame(list, cb) {\n        if (list.length === 2) {\n            this._socket.cork();\n            this._socket.write(list[0]);\n            this._socket.write(list[1], cb);\n            this._socket.uncork();\n        } else {\n            this._socket.write(list[0], cb);\n        }\n    }\n}\nmodule.exports = Sender;\n/**\n * Calls queued callbacks with an error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error to call the callbacks with\n * @param {Function} [cb] The first callback\n * @private\n */ function callCallbacks(sender, err, cb) {\n    if (typeof cb === \"function\") cb(err);\n    for(let i = 0; i < sender._queue.length; i++){\n        const params = sender._queue[i];\n        const callback = params[params.length - 1];\n        if (typeof callback === \"function\") callback(err);\n    }\n}\n/**\n * Handles a `Sender` error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error\n * @param {Function} [cb] The first pending callback\n * @private\n */ function onError(sender, err, cb) {\n    callCallbacks(sender, err, cb);\n    sender.onerror(err);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/sender.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/stream.js":
/*!***************************************!*\
  !*** ./node_modules/ws/lib/stream.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^WebSocket$\" }] */ \nconst WebSocket = __webpack_require__(/*! ./websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\n/**\n * Emits the `'close'` event on a stream.\n *\n * @param {Duplex} stream The stream.\n * @private\n */ function emitClose(stream) {\n    stream.emit(\"close\");\n}\n/**\n * The listener of the `'end'` event.\n *\n * @private\n */ function duplexOnEnd() {\n    if (!this.destroyed && this._writableState.finished) {\n        this.destroy();\n    }\n}\n/**\n * The listener of the `'error'` event.\n *\n * @param {Error} err The error\n * @private\n */ function duplexOnError(err) {\n    this.removeListener(\"error\", duplexOnError);\n    this.destroy();\n    if (this.listenerCount(\"error\") === 0) {\n        // Do not suppress the throwing behavior.\n        this.emit(\"error\", err);\n    }\n}\n/**\n * Wraps a `WebSocket` in a duplex stream.\n *\n * @param {WebSocket} ws The `WebSocket` to wrap\n * @param {Object} [options] The options for the `Duplex` constructor\n * @return {Duplex} The duplex stream\n * @public\n */ function createWebSocketStream(ws, options) {\n    let terminateOnDestroy = true;\n    const duplex = new Duplex({\n        ...options,\n        autoDestroy: false,\n        emitClose: false,\n        objectMode: false,\n        writableObjectMode: false\n    });\n    ws.on(\"message\", function message(msg, isBinary) {\n        const data = !isBinary && duplex._readableState.objectMode ? msg.toString() : msg;\n        if (!duplex.push(data)) ws.pause();\n    });\n    ws.once(\"error\", function error(err) {\n        if (duplex.destroyed) return;\n        // Prevent `ws.terminate()` from being called by `duplex._destroy()`.\n        //\n        // - If the `'error'` event is emitted before the `'open'` event, then\n        //   `ws.terminate()` is a noop as no socket is assigned.\n        // - Otherwise, the error is re-emitted by the listener of the `'error'`\n        //   event of the `Receiver` object. The listener already closes the\n        //   connection by calling `ws.close()`. This allows a close frame to be\n        //   sent to the other peer. If `ws.terminate()` is called right after this,\n        //   then the close frame might not be sent.\n        terminateOnDestroy = false;\n        duplex.destroy(err);\n    });\n    ws.once(\"close\", function close() {\n        if (duplex.destroyed) return;\n        duplex.push(null);\n    });\n    duplex._destroy = function(err, callback) {\n        if (ws.readyState === ws.CLOSED) {\n            callback(err);\n            process.nextTick(emitClose, duplex);\n            return;\n        }\n        let called = false;\n        ws.once(\"error\", function error(err) {\n            called = true;\n            callback(err);\n        });\n        ws.once(\"close\", function close() {\n            if (!called) callback(err);\n            process.nextTick(emitClose, duplex);\n        });\n        if (terminateOnDestroy) ws.terminate();\n    };\n    duplex._final = function(callback) {\n        if (ws.readyState === ws.CONNECTING) {\n            ws.once(\"open\", function open() {\n                duplex._final(callback);\n            });\n            return;\n        }\n        // If the value of the `_socket` property is `null` it means that `ws` is a\n        // client websocket and the handshake failed. In fact, when this happens, a\n        // socket is never assigned to the websocket. Wait for the `'error'` event\n        // that will be emitted by the websocket.\n        if (ws._socket === null) return;\n        if (ws._socket._writableState.finished) {\n            callback();\n            if (duplex._readableState.endEmitted) duplex.destroy();\n        } else {\n            ws._socket.once(\"finish\", function finish() {\n                // `duplex` is not destroyed here because the `'end'` event will be\n                // emitted on `duplex` after this `'finish'` event. The EOF signaling\n                // `null` chunk is, in fact, pushed when the websocket emits `'close'`.\n                callback();\n            });\n            ws.close();\n        }\n    };\n    duplex._read = function() {\n        if (ws.isPaused) ws.resume();\n    };\n    duplex._write = function(chunk, encoding, callback) {\n        if (ws.readyState === ws.CONNECTING) {\n            ws.once(\"open\", function open() {\n                duplex._write(chunk, encoding, callback);\n            });\n            return;\n        }\n        ws.send(chunk, callback);\n    };\n    duplex.on(\"end\", duplexOnEnd);\n    duplex.on(\"error\", duplexOnError);\n    return duplex;\n}\nmodule.exports = createWebSocketStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/subprotocol.js":
/*!********************************************!*\
  !*** ./node_modules/ws/lib/subprotocol.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { tokenChars } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n/**\n * Parses the `Sec-WebSocket-Protocol` header into a set of subprotocol names.\n *\n * @param {String} header The field value of the header\n * @return {Set} The subprotocol names\n * @public\n */ function parse(header) {\n    const protocols = new Set();\n    let start = -1;\n    let end = -1;\n    let i = 0;\n    for(i; i < header.length; i++){\n        const code = header.charCodeAt(i);\n        if (end === -1 && tokenChars[code] === 1) {\n            if (start === -1) start = i;\n        } else if (i !== 0 && (code === 0x20 /* ' ' */  || code === 0x09)) {\n            if (end === -1 && start !== -1) end = i;\n        } else if (code === 0x2c /* ',' */ ) {\n            if (start === -1) {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n            if (end === -1) end = i;\n            const protocol = header.slice(start, end);\n            if (protocols.has(protocol)) {\n                throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n            }\n            protocols.add(protocol);\n            start = end = -1;\n        } else {\n            throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n    }\n    if (start === -1 || end !== -1) {\n        throw new SyntaxError(\"Unexpected end of input\");\n    }\n    const protocol = header.slice(start, i);\n    if (protocols.has(protocol)) {\n        throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n    }\n    protocols.add(protocol);\n    return protocols;\n}\nmodule.exports = {\n    parse\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/subprotocol.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/validation.js":
/*!*******************************************!*\
  !*** ./node_modules/ws/lib/validation.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { isUtf8 } = __webpack_require__(/*! buffer */ \"buffer\");\nconst { hasBlob } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\n//\n// Allowed token characters:\n//\n// '!', '#', '$', '%', '&', ''', '*', '+', '-',\n// '.', 0-9, A-Z, '^', '_', '`', a-z, '|', '~'\n//\n// tokenChars[32] === 0 // ' '\n// tokenChars[33] === 1 // '!'\n// tokenChars[34] === 0 // '\"'\n// ...\n//\n// prettier-ignore\nconst tokenChars = [\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    1,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    0,\n    1,\n    1,\n    0,\n    1,\n    1,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    0,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    1,\n    0,\n    1,\n    0 // 112 - 127\n];\n/**\n * Checks if a status code is allowed in a close frame.\n *\n * @param {Number} code The status code\n * @return {Boolean} `true` if the status code is valid, else `false`\n * @public\n */ function isValidStatusCode(code) {\n    return code >= 1000 && code <= 1014 && code !== 1004 && code !== 1005 && code !== 1006 || code >= 3000 && code <= 4999;\n}\n/**\n * Checks if a given buffer contains only correct UTF-8.\n * Ported from https://www.cl.cam.ac.uk/%7Emgk25/ucs/utf8_check.c by\n * Markus Kuhn.\n *\n * @param {Buffer} buf The buffer to check\n * @return {Boolean} `true` if `buf` contains only correct UTF-8, else `false`\n * @public\n */ function _isValidUTF8(buf) {\n    const len = buf.length;\n    let i = 0;\n    while(i < len){\n        if ((buf[i] & 0x80) === 0) {\n            // 0xxxxxxx\n            i++;\n        } else if ((buf[i] & 0xe0) === 0xc0) {\n            // 110xxxxx 10xxxxxx\n            if (i + 1 === len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i] & 0xfe) === 0xc0 // Overlong\n            ) {\n                return false;\n            }\n            i += 2;\n        } else if ((buf[i] & 0xf0) === 0xe0) {\n            // 1110xxxx 10xxxxxx 10xxxxxx\n            if (i + 2 >= len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i + 2] & 0xc0) !== 0x80 || buf[i] === 0xe0 && (buf[i + 1] & 0xe0) === 0x80 || // Overlong\n            buf[i] === 0xed && (buf[i + 1] & 0xe0) === 0xa0 // Surrogate (U+D800 - U+DFFF)\n            ) {\n                return false;\n            }\n            i += 3;\n        } else if ((buf[i] & 0xf8) === 0xf0) {\n            // 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx\n            if (i + 3 >= len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i + 2] & 0xc0) !== 0x80 || (buf[i + 3] & 0xc0) !== 0x80 || buf[i] === 0xf0 && (buf[i + 1] & 0xf0) === 0x80 || // Overlong\n            buf[i] === 0xf4 && buf[i + 1] > 0x8f || buf[i] > 0xf4 // > U+10FFFF\n            ) {\n                return false;\n            }\n            i += 4;\n        } else {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Determines whether a value is a `Blob`.\n *\n * @param {*} value The value to be tested\n * @return {Boolean} `true` if `value` is a `Blob`, else `false`\n * @private\n */ function isBlob(value) {\n    return hasBlob && typeof value === \"object\" && typeof value.arrayBuffer === \"function\" && typeof value.type === \"string\" && typeof value.stream === \"function\" && (value[Symbol.toStringTag] === \"Blob\" || value[Symbol.toStringTag] === \"File\");\n}\nmodule.exports = {\n    isBlob,\n    isValidStatusCode,\n    isValidUTF8: _isValidUTF8,\n    tokenChars\n};\nif (isUtf8) {\n    module.exports.isValidUTF8 = function(buf) {\n        return buf.length < 24 ? _isValidUTF8(buf) : isUtf8(buf);\n    };\n} else if (!process.env.WS_NO_UTF_8_VALIDATE) {\n    try {\n        const isValidUTF8 = __webpack_require__(/*! utf-8-validate */ \"?66e9\");\n        module.exports.isValidUTF8 = function(buf) {\n            return buf.length < 32 ? _isValidUTF8(buf) : isValidUTF8(buf);\n        };\n    } catch (e) {\n    // Continue regardless of the error.\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/validation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/websocket-server.js":
/*!*************************************************!*\
  !*** ./node_modules/ws/lib/websocket-server.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex$\", \"caughtErrors\": \"none\" }] */ \nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\nconst { createHash } = __webpack_require__(/*! crypto */ \"crypto\");\nconst extension = __webpack_require__(/*! ./extension */ \"(ssr)/./node_modules/ws/lib/extension.js\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst subprotocol = __webpack_require__(/*! ./subprotocol */ \"(ssr)/./node_modules/ws/lib/subprotocol.js\");\nconst WebSocket = __webpack_require__(/*! ./websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\nconst { GUID, kWebSocket } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst keyRegex = /^[+/0-9A-Za-z]{22}==$/;\nconst RUNNING = 0;\nconst CLOSING = 1;\nconst CLOSED = 2;\n/**\n * Class representing a WebSocket server.\n *\n * @extends EventEmitter\n */ class WebSocketServer extends EventEmitter {\n    /**\n   * Create a `WebSocketServer` instance.\n   *\n   * @param {Object} options Configuration options\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n   *     automatically send a pong in response to a ping\n   * @param {Number} [options.backlog=511] The maximum length of the queue of\n   *     pending connections\n   * @param {Boolean} [options.clientTracking=true] Specifies whether or not to\n   *     track clients\n   * @param {Function} [options.handleProtocols] A hook to handle protocols\n   * @param {String} [options.host] The hostname where to bind the server\n   * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n   *     size\n   * @param {Boolean} [options.noServer=false] Enable no server mode\n   * @param {String} [options.path] Accept only connections matching this path\n   * @param {(Boolean|Object)} [options.perMessageDeflate=false] Enable/disable\n   *     permessage-deflate\n   * @param {Number} [options.port] The port where to bind the server\n   * @param {(http.Server|https.Server)} [options.server] A pre-created HTTP/S\n   *     server to use\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @param {Function} [options.verifyClient] A hook to reject connections\n   * @param {Function} [options.WebSocket=WebSocket] Specifies the `WebSocket`\n   *     class to use. It must be the `WebSocket` class or class that extends it\n   * @param {Function} [callback] A listener for the `listening` event\n   */ constructor(options, callback){\n        super();\n        options = {\n            allowSynchronousEvents: true,\n            autoPong: true,\n            maxPayload: 100 * 1024 * 1024,\n            skipUTF8Validation: false,\n            perMessageDeflate: false,\n            handleProtocols: null,\n            clientTracking: true,\n            verifyClient: null,\n            noServer: false,\n            backlog: null,\n            server: null,\n            host: null,\n            path: null,\n            port: null,\n            WebSocket,\n            ...options\n        };\n        if (options.port == null && !options.server && !options.noServer || options.port != null && (options.server || options.noServer) || options.server && options.noServer) {\n            throw new TypeError('One and only one of the \"port\", \"server\", or \"noServer\" options ' + \"must be specified\");\n        }\n        if (options.port != null) {\n            this._server = http.createServer((req, res)=>{\n                const body = http.STATUS_CODES[426];\n                res.writeHead(426, {\n                    \"Content-Length\": body.length,\n                    \"Content-Type\": \"text/plain\"\n                });\n                res.end(body);\n            });\n            this._server.listen(options.port, options.host, options.backlog, callback);\n        } else if (options.server) {\n            this._server = options.server;\n        }\n        if (this._server) {\n            const emitConnection = this.emit.bind(this, \"connection\");\n            this._removeListeners = addListeners(this._server, {\n                listening: this.emit.bind(this, \"listening\"),\n                error: this.emit.bind(this, \"error\"),\n                upgrade: (req, socket, head)=>{\n                    this.handleUpgrade(req, socket, head, emitConnection);\n                }\n            });\n        }\n        if (options.perMessageDeflate === true) options.perMessageDeflate = {};\n        if (options.clientTracking) {\n            this.clients = new Set();\n            this._shouldEmitClose = false;\n        }\n        this.options = options;\n        this._state = RUNNING;\n    }\n    /**\n   * Returns the bound address, the address family name, and port of the server\n   * as reported by the operating system if listening on an IP socket.\n   * If the server is listening on a pipe or UNIX domain socket, the name is\n   * returned as a string.\n   *\n   * @return {(Object|String|null)} The address of the server\n   * @public\n   */ address() {\n        if (this.options.noServer) {\n            throw new Error('The server is operating in \"noServer\" mode');\n        }\n        if (!this._server) return null;\n        return this._server.address();\n    }\n    /**\n   * Stop the server from accepting new connections and emit the `'close'` event\n   * when all existing connections are closed.\n   *\n   * @param {Function} [cb] A one-time listener for the `'close'` event\n   * @public\n   */ close(cb) {\n        if (this._state === CLOSED) {\n            if (cb) {\n                this.once(\"close\", ()=>{\n                    cb(new Error(\"The server is not running\"));\n                });\n            }\n            process.nextTick(emitClose, this);\n            return;\n        }\n        if (cb) this.once(\"close\", cb);\n        if (this._state === CLOSING) return;\n        this._state = CLOSING;\n        if (this.options.noServer || this.options.server) {\n            if (this._server) {\n                this._removeListeners();\n                this._removeListeners = this._server = null;\n            }\n            if (this.clients) {\n                if (!this.clients.size) {\n                    process.nextTick(emitClose, this);\n                } else {\n                    this._shouldEmitClose = true;\n                }\n            } else {\n                process.nextTick(emitClose, this);\n            }\n        } else {\n            const server = this._server;\n            this._removeListeners();\n            this._removeListeners = this._server = null;\n            //\n            // The HTTP/S server was created internally. Close it, and rely on its\n            // `'close'` event.\n            //\n            server.close(()=>{\n                emitClose(this);\n            });\n        }\n    }\n    /**\n   * See if a given request should be handled by this server instance.\n   *\n   * @param {http.IncomingMessage} req Request object to inspect\n   * @return {Boolean} `true` if the request is valid, else `false`\n   * @public\n   */ shouldHandle(req) {\n        if (this.options.path) {\n            const index = req.url.indexOf(\"?\");\n            const pathname = index !== -1 ? req.url.slice(0, index) : req.url;\n            if (pathname !== this.options.path) return false;\n        }\n        return true;\n    }\n    /**\n   * Handle a HTTP Upgrade request.\n   *\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @public\n   */ handleUpgrade(req, socket, head, cb) {\n        socket.on(\"error\", socketOnError);\n        const key = req.headers[\"sec-websocket-key\"];\n        const upgrade = req.headers.upgrade;\n        const version = +req.headers[\"sec-websocket-version\"];\n        if (req.method !== \"GET\") {\n            const message = \"Invalid HTTP method\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 405, message);\n            return;\n        }\n        if (upgrade === undefined || upgrade.toLowerCase() !== \"websocket\") {\n            const message = \"Invalid Upgrade header\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n            return;\n        }\n        if (key === undefined || !keyRegex.test(key)) {\n            const message = \"Missing or invalid Sec-WebSocket-Key header\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n            return;\n        }\n        if (version !== 13 && version !== 8) {\n            const message = \"Missing or invalid Sec-WebSocket-Version header\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 400, message, {\n                \"Sec-WebSocket-Version\": \"13, 8\"\n            });\n            return;\n        }\n        if (!this.shouldHandle(req)) {\n            abortHandshake(socket, 400);\n            return;\n        }\n        const secWebSocketProtocol = req.headers[\"sec-websocket-protocol\"];\n        let protocols = new Set();\n        if (secWebSocketProtocol !== undefined) {\n            try {\n                protocols = subprotocol.parse(secWebSocketProtocol);\n            } catch (err) {\n                const message = \"Invalid Sec-WebSocket-Protocol header\";\n                abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n                return;\n            }\n        }\n        const secWebSocketExtensions = req.headers[\"sec-websocket-extensions\"];\n        const extensions = {};\n        if (this.options.perMessageDeflate && secWebSocketExtensions !== undefined) {\n            const perMessageDeflate = new PerMessageDeflate(this.options.perMessageDeflate, true, this.options.maxPayload);\n            try {\n                const offers = extension.parse(secWebSocketExtensions);\n                if (offers[PerMessageDeflate.extensionName]) {\n                    perMessageDeflate.accept(offers[PerMessageDeflate.extensionName]);\n                    extensions[PerMessageDeflate.extensionName] = perMessageDeflate;\n                }\n            } catch (err) {\n                const message = \"Invalid or unacceptable Sec-WebSocket-Extensions header\";\n                abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n                return;\n            }\n        }\n        //\n        // Optionally call external client verification handler.\n        //\n        if (this.options.verifyClient) {\n            const info = {\n                origin: req.headers[`${version === 8 ? \"sec-websocket-origin\" : \"origin\"}`],\n                secure: !!(req.socket.authorized || req.socket.encrypted),\n                req\n            };\n            if (this.options.verifyClient.length === 2) {\n                this.options.verifyClient(info, (verified, code, message, headers)=>{\n                    if (!verified) {\n                        return abortHandshake(socket, code || 401, message, headers);\n                    }\n                    this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n                });\n                return;\n            }\n            if (!this.options.verifyClient(info)) return abortHandshake(socket, 401);\n        }\n        this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n    }\n    /**\n   * Upgrade the connection to WebSocket.\n   *\n   * @param {Object} extensions The accepted extensions\n   * @param {String} key The value of the `Sec-WebSocket-Key` header\n   * @param {Set} protocols The subprotocols\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @throws {Error} If called more than once with the same socket\n   * @private\n   */ completeUpgrade(extensions, key, protocols, req, socket, head, cb) {\n        //\n        // Destroy the socket if the client has already sent a FIN packet.\n        //\n        if (!socket.readable || !socket.writable) return socket.destroy();\n        if (socket[kWebSocket]) {\n            throw new Error(\"server.handleUpgrade() was called more than once with the same \" + \"socket, possibly due to a misconfiguration\");\n        }\n        if (this._state > RUNNING) return abortHandshake(socket, 503);\n        const digest = createHash(\"sha1\").update(key + GUID).digest(\"base64\");\n        const headers = [\n            \"HTTP/1.1 101 Switching Protocols\",\n            \"Upgrade: websocket\",\n            \"Connection: Upgrade\",\n            `Sec-WebSocket-Accept: ${digest}`\n        ];\n        const ws = new this.options.WebSocket(null, undefined, this.options);\n        if (protocols.size) {\n            //\n            // Optionally call external protocol selection handler.\n            //\n            const protocol = this.options.handleProtocols ? this.options.handleProtocols(protocols, req) : protocols.values().next().value;\n            if (protocol) {\n                headers.push(`Sec-WebSocket-Protocol: ${protocol}`);\n                ws._protocol = protocol;\n            }\n        }\n        if (extensions[PerMessageDeflate.extensionName]) {\n            const params = extensions[PerMessageDeflate.extensionName].params;\n            const value = extension.format({\n                [PerMessageDeflate.extensionName]: [\n                    params\n                ]\n            });\n            headers.push(`Sec-WebSocket-Extensions: ${value}`);\n            ws._extensions = extensions;\n        }\n        //\n        // Allow external modification/inspection of handshake headers.\n        //\n        this.emit(\"headers\", headers, req);\n        socket.write(headers.concat(\"\\r\\n\").join(\"\\r\\n\"));\n        socket.removeListener(\"error\", socketOnError);\n        ws.setSocket(socket, head, {\n            allowSynchronousEvents: this.options.allowSynchronousEvents,\n            maxPayload: this.options.maxPayload,\n            skipUTF8Validation: this.options.skipUTF8Validation\n        });\n        if (this.clients) {\n            this.clients.add(ws);\n            ws.on(\"close\", ()=>{\n                this.clients.delete(ws);\n                if (this._shouldEmitClose && !this.clients.size) {\n                    process.nextTick(emitClose, this);\n                }\n            });\n        }\n        cb(ws, req);\n    }\n}\nmodule.exports = WebSocketServer;\n/**\n * Add event listeners on an `EventEmitter` using a map of <event, listener>\n * pairs.\n *\n * @param {EventEmitter} server The event emitter\n * @param {Object.<String, Function>} map The listeners to add\n * @return {Function} A function that will remove the added listeners when\n *     called\n * @private\n */ function addListeners(server, map) {\n    for (const event of Object.keys(map))server.on(event, map[event]);\n    return function removeListeners() {\n        for (const event of Object.keys(map)){\n            server.removeListener(event, map[event]);\n        }\n    };\n}\n/**\n * Emit a `'close'` event on an `EventEmitter`.\n *\n * @param {EventEmitter} server The event emitter\n * @private\n */ function emitClose(server) {\n    server._state = CLOSED;\n    server.emit(\"close\");\n}\n/**\n * Handle socket errors.\n *\n * @private\n */ function socketOnError() {\n    this.destroy();\n}\n/**\n * Close the connection when preconditions are not fulfilled.\n *\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} [message] The HTTP response body\n * @param {Object} [headers] Additional HTTP response headers\n * @private\n */ function abortHandshake(socket, code, message, headers) {\n    //\n    // The socket is writable unless the user destroyed or ended it before calling\n    // `server.handleUpgrade()` or in the `verifyClient` function, which is a user\n    // error. Handling this does not make much sense as the worst that can happen\n    // is that some of the data written by the user might be discarded due to the\n    // call to `socket.end()` below, which triggers an `'error'` event that in\n    // turn causes the socket to be destroyed.\n    //\n    message = message || http.STATUS_CODES[code];\n    headers = {\n        Connection: \"close\",\n        \"Content-Type\": \"text/html\",\n        \"Content-Length\": Buffer.byteLength(message),\n        ...headers\n    };\n    socket.once(\"finish\", socket.destroy);\n    socket.end(`HTTP/1.1 ${code} ${http.STATUS_CODES[code]}\\r\\n` + Object.keys(headers).map((h)=>`${h}: ${headers[h]}`).join(\"\\r\\n\") + \"\\r\\n\\r\\n\" + message);\n}\n/**\n * Emit a `'wsClientError'` event on a `WebSocketServer` if there is at least\n * one listener for it, otherwise call `abortHandshake()`.\n *\n * @param {WebSocketServer} server The WebSocket server\n * @param {http.IncomingMessage} req The request object\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} message The HTTP response body\n * @param {Object} [headers] The HTTP response headers\n * @private\n */ function abortHandshakeOrEmitwsClientError(server, req, socket, code, message, headers) {\n    if (server.listenerCount(\"wsClientError\")) {\n        const err = new Error(message);\n        Error.captureStackTrace(err, abortHandshakeOrEmitwsClientError);\n        server.emit(\"wsClientError\", err, socket, req);\n    } else {\n        abortHandshake(socket, code, message, headers);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/websocket-server.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/websocket.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/websocket.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex|Readable$\", \"caughtErrors\": \"none\" }] */ \nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst net = __webpack_require__(/*! net */ \"net\");\nconst tls = __webpack_require__(/*! tls */ \"tls\");\nconst { randomBytes, createHash } = __webpack_require__(/*! crypto */ \"crypto\");\nconst { Duplex, Readable } = __webpack_require__(/*! stream */ \"stream\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst Receiver = __webpack_require__(/*! ./receiver */ \"(ssr)/./node_modules/ws/lib/receiver.js\");\nconst Sender = __webpack_require__(/*! ./sender */ \"(ssr)/./node_modules/ws/lib/sender.js\");\nconst { isBlob } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst { BINARY_TYPES, EMPTY_BUFFER, GUID, kForOnEventAttribute, kListener, kStatusCode, kWebSocket, NOOP } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { EventTarget: { addEventListener, removeEventListener } } = __webpack_require__(/*! ./event-target */ \"(ssr)/./node_modules/ws/lib/event-target.js\");\nconst { format, parse } = __webpack_require__(/*! ./extension */ \"(ssr)/./node_modules/ws/lib/extension.js\");\nconst { toBuffer } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst closeTimeout = 30 * 1000;\nconst kAborted = Symbol(\"kAborted\");\nconst protocolVersions = [\n    8,\n    13\n];\nconst readyStates = [\n    \"CONNECTING\",\n    \"OPEN\",\n    \"CLOSING\",\n    \"CLOSED\"\n];\nconst subprotocolRegex = /^[!#$%&'*+\\-.0-9A-Z^_`|a-z~]+$/;\n/**\n * Class representing a WebSocket.\n *\n * @extends EventEmitter\n */ class WebSocket extends EventEmitter {\n    /**\n   * Create a new `WebSocket`.\n   *\n   * @param {(String|URL)} address The URL to which to connect\n   * @param {(String|String[])} [protocols] The subprotocols\n   * @param {Object} [options] Connection options\n   */ constructor(address, protocols, options){\n        super();\n        this._binaryType = BINARY_TYPES[0];\n        this._closeCode = 1006;\n        this._closeFrameReceived = false;\n        this._closeFrameSent = false;\n        this._closeMessage = EMPTY_BUFFER;\n        this._closeTimer = null;\n        this._errorEmitted = false;\n        this._extensions = {};\n        this._paused = false;\n        this._protocol = \"\";\n        this._readyState = WebSocket.CONNECTING;\n        this._receiver = null;\n        this._sender = null;\n        this._socket = null;\n        if (address !== null) {\n            this._bufferedAmount = 0;\n            this._isServer = false;\n            this._redirects = 0;\n            if (protocols === undefined) {\n                protocols = [];\n            } else if (!Array.isArray(protocols)) {\n                if (typeof protocols === \"object\" && protocols !== null) {\n                    options = protocols;\n                    protocols = [];\n                } else {\n                    protocols = [\n                        protocols\n                    ];\n                }\n            }\n            initAsClient(this, address, protocols, options);\n        } else {\n            this._autoPong = options.autoPong;\n            this._isServer = true;\n        }\n    }\n    /**\n   * For historical reasons, the custom \"nodebuffer\" type is used by the default\n   * instead of \"blob\".\n   *\n   * @type {String}\n   */ get binaryType() {\n        return this._binaryType;\n    }\n    set binaryType(type) {\n        if (!BINARY_TYPES.includes(type)) return;\n        this._binaryType = type;\n        //\n        // Allow to change `binaryType` on the fly.\n        //\n        if (this._receiver) this._receiver._binaryType = type;\n    }\n    /**\n   * @type {Number}\n   */ get bufferedAmount() {\n        if (!this._socket) return this._bufferedAmount;\n        return this._socket._writableState.length + this._sender._bufferedBytes;\n    }\n    /**\n   * @type {String}\n   */ get extensions() {\n        return Object.keys(this._extensions).join();\n    }\n    /**\n   * @type {Boolean}\n   */ get isPaused() {\n        return this._paused;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onclose() {\n        return null;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onerror() {\n        return null;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onopen() {\n        return null;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onmessage() {\n        return null;\n    }\n    /**\n   * @type {String}\n   */ get protocol() {\n        return this._protocol;\n    }\n    /**\n   * @type {Number}\n   */ get readyState() {\n        return this._readyState;\n    }\n    /**\n   * @type {String}\n   */ get url() {\n        return this._url;\n    }\n    /**\n   * Set up the socket and the internal resources.\n   *\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Object} options Options object\n   * @param {Boolean} [options.allowSynchronousEvents=false] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Number} [options.maxPayload=0] The maximum allowed message size\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @private\n   */ setSocket(socket, head, options) {\n        const receiver = new Receiver({\n            allowSynchronousEvents: options.allowSynchronousEvents,\n            binaryType: this.binaryType,\n            extensions: this._extensions,\n            isServer: this._isServer,\n            maxPayload: options.maxPayload,\n            skipUTF8Validation: options.skipUTF8Validation\n        });\n        const sender = new Sender(socket, this._extensions, options.generateMask);\n        this._receiver = receiver;\n        this._sender = sender;\n        this._socket = socket;\n        receiver[kWebSocket] = this;\n        sender[kWebSocket] = this;\n        socket[kWebSocket] = this;\n        receiver.on(\"conclude\", receiverOnConclude);\n        receiver.on(\"drain\", receiverOnDrain);\n        receiver.on(\"error\", receiverOnError);\n        receiver.on(\"message\", receiverOnMessage);\n        receiver.on(\"ping\", receiverOnPing);\n        receiver.on(\"pong\", receiverOnPong);\n        sender.onerror = senderOnError;\n        //\n        // These methods may not be available if `socket` is just a `Duplex`.\n        //\n        if (socket.setTimeout) socket.setTimeout(0);\n        if (socket.setNoDelay) socket.setNoDelay();\n        if (head.length > 0) socket.unshift(head);\n        socket.on(\"close\", socketOnClose);\n        socket.on(\"data\", socketOnData);\n        socket.on(\"end\", socketOnEnd);\n        socket.on(\"error\", socketOnError);\n        this._readyState = WebSocket.OPEN;\n        this.emit(\"open\");\n    }\n    /**\n   * Emit the `'close'` event.\n   *\n   * @private\n   */ emitClose() {\n        if (!this._socket) {\n            this._readyState = WebSocket.CLOSED;\n            this.emit(\"close\", this._closeCode, this._closeMessage);\n            return;\n        }\n        if (this._extensions[PerMessageDeflate.extensionName]) {\n            this._extensions[PerMessageDeflate.extensionName].cleanup();\n        }\n        this._receiver.removeAllListeners();\n        this._readyState = WebSocket.CLOSED;\n        this.emit(\"close\", this._closeCode, this._closeMessage);\n    }\n    /**\n   * Start a closing handshake.\n   *\n   *          +----------+   +-----------+   +----------+\n   *     - - -|ws.close()|-->|close frame|-->|ws.close()|- - -\n   *    |     +----------+   +-----------+   +----------+     |\n   *          +----------+   +-----------+         |\n   * CLOSING  |ws.close()|<--|close frame|<--+-----+       CLOSING\n   *          +----------+   +-----------+   |\n   *    |           |                        |   +---+        |\n   *                +------------------------+-->|fin| - - - -\n   *    |         +---+                      |   +---+\n   *     - - - - -|fin|<---------------------+\n   *              +---+\n   *\n   * @param {Number} [code] Status code explaining why the connection is closing\n   * @param {(String|Buffer)} [data] The reason why the connection is\n   *     closing\n   * @public\n   */ close(code, data) {\n        if (this.readyState === WebSocket.CLOSED) return;\n        if (this.readyState === WebSocket.CONNECTING) {\n            const msg = \"WebSocket was closed before the connection was established\";\n            abortHandshake(this, this._req, msg);\n            return;\n        }\n        if (this.readyState === WebSocket.CLOSING) {\n            if (this._closeFrameSent && (this._closeFrameReceived || this._receiver._writableState.errorEmitted)) {\n                this._socket.end();\n            }\n            return;\n        }\n        this._readyState = WebSocket.CLOSING;\n        this._sender.close(code, data, !this._isServer, (err)=>{\n            //\n            // This error is handled by the `'error'` listener on the socket. We only\n            // want to know if the close frame has been sent here.\n            //\n            if (err) return;\n            this._closeFrameSent = true;\n            if (this._closeFrameReceived || this._receiver._writableState.errorEmitted) {\n                this._socket.end();\n            }\n        });\n        setCloseTimer(this);\n    }\n    /**\n   * Pause the socket.\n   *\n   * @public\n   */ pause() {\n        if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n            return;\n        }\n        this._paused = true;\n        this._socket.pause();\n    }\n    /**\n   * Send a ping.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the ping is sent\n   * @public\n   */ ping(data, mask, cb) {\n        if (this.readyState === WebSocket.CONNECTING) {\n            throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n        }\n        if (typeof data === \"function\") {\n            cb = data;\n            data = mask = undefined;\n        } else if (typeof mask === \"function\") {\n            cb = mask;\n            mask = undefined;\n        }\n        if (typeof data === \"number\") data = data.toString();\n        if (this.readyState !== WebSocket.OPEN) {\n            sendAfterClose(this, data, cb);\n            return;\n        }\n        if (mask === undefined) mask = !this._isServer;\n        this._sender.ping(data || EMPTY_BUFFER, mask, cb);\n    }\n    /**\n   * Send a pong.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the pong is sent\n   * @public\n   */ pong(data, mask, cb) {\n        if (this.readyState === WebSocket.CONNECTING) {\n            throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n        }\n        if (typeof data === \"function\") {\n            cb = data;\n            data = mask = undefined;\n        } else if (typeof mask === \"function\") {\n            cb = mask;\n            mask = undefined;\n        }\n        if (typeof data === \"number\") data = data.toString();\n        if (this.readyState !== WebSocket.OPEN) {\n            sendAfterClose(this, data, cb);\n            return;\n        }\n        if (mask === undefined) mask = !this._isServer;\n        this._sender.pong(data || EMPTY_BUFFER, mask, cb);\n    }\n    /**\n   * Resume the socket.\n   *\n   * @public\n   */ resume() {\n        if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n            return;\n        }\n        this._paused = false;\n        if (!this._receiver._writableState.needDrain) this._socket.resume();\n    }\n    /**\n   * Send a data message.\n   *\n   * @param {*} data The message to send\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.binary] Specifies whether `data` is binary or\n   *     text\n   * @param {Boolean} [options.compress] Specifies whether or not to compress\n   *     `data`\n   * @param {Boolean} [options.fin=true] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when data is written out\n   * @public\n   */ send(data, options, cb) {\n        if (this.readyState === WebSocket.CONNECTING) {\n            throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n        }\n        if (typeof options === \"function\") {\n            cb = options;\n            options = {};\n        }\n        if (typeof data === \"number\") data = data.toString();\n        if (this.readyState !== WebSocket.OPEN) {\n            sendAfterClose(this, data, cb);\n            return;\n        }\n        const opts = {\n            binary: typeof data !== \"string\",\n            mask: !this._isServer,\n            compress: true,\n            fin: true,\n            ...options\n        };\n        if (!this._extensions[PerMessageDeflate.extensionName]) {\n            opts.compress = false;\n        }\n        this._sender.send(data || EMPTY_BUFFER, opts, cb);\n    }\n    /**\n   * Forcibly close the connection.\n   *\n   * @public\n   */ terminate() {\n        if (this.readyState === WebSocket.CLOSED) return;\n        if (this.readyState === WebSocket.CONNECTING) {\n            const msg = \"WebSocket was closed before the connection was established\";\n            abortHandshake(this, this._req, msg);\n            return;\n        }\n        if (this._socket) {\n            this._readyState = WebSocket.CLOSING;\n            this._socket.destroy();\n        }\n    }\n}\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"CONNECTING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CONNECTING\")\n});\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"CONNECTING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CONNECTING\")\n});\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"OPEN\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"OPEN\")\n});\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"OPEN\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"OPEN\")\n});\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"CLOSING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSING\")\n});\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"CLOSING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSING\")\n});\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"CLOSED\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSED\")\n});\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"CLOSED\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSED\")\n});\n[\n    \"binaryType\",\n    \"bufferedAmount\",\n    \"extensions\",\n    \"isPaused\",\n    \"protocol\",\n    \"readyState\",\n    \"url\"\n].forEach((property)=>{\n    Object.defineProperty(WebSocket.prototype, property, {\n        enumerable: true\n    });\n});\n//\n// Add the `onopen`, `onerror`, `onclose`, and `onmessage` attributes.\n// See https://html.spec.whatwg.org/multipage/comms.html#the-websocket-interface\n//\n[\n    \"open\",\n    \"error\",\n    \"close\",\n    \"message\"\n].forEach((method)=>{\n    Object.defineProperty(WebSocket.prototype, `on${method}`, {\n        enumerable: true,\n        get () {\n            for (const listener of this.listeners(method)){\n                if (listener[kForOnEventAttribute]) return listener[kListener];\n            }\n            return null;\n        },\n        set (handler) {\n            for (const listener of this.listeners(method)){\n                if (listener[kForOnEventAttribute]) {\n                    this.removeListener(method, listener);\n                    break;\n                }\n            }\n            if (typeof handler !== \"function\") return;\n            this.addEventListener(method, handler, {\n                [kForOnEventAttribute]: true\n            });\n        }\n    });\n});\nWebSocket.prototype.addEventListener = addEventListener;\nWebSocket.prototype.removeEventListener = removeEventListener;\nmodule.exports = WebSocket;\n/**\n * Initialize a WebSocket client.\n *\n * @param {WebSocket} websocket The client to initialize\n * @param {(String|URL)} address The URL to which to connect\n * @param {Array} protocols The subprotocols\n * @param {Object} [options] Connection options\n * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether any\n *     of the `'message'`, `'ping'`, and `'pong'` events can be emitted multiple\n *     times in the same tick\n * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n *     automatically send a pong in response to a ping\n * @param {Function} [options.finishRequest] A function which can be used to\n *     customize the headers of each http request before it is sent\n * @param {Boolean} [options.followRedirects=false] Whether or not to follow\n *     redirects\n * @param {Function} [options.generateMask] The function used to generate the\n *     masking key\n * @param {Number} [options.handshakeTimeout] Timeout in milliseconds for the\n *     handshake request\n * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n *     size\n * @param {Number} [options.maxRedirects=10] The maximum number of redirects\n *     allowed\n * @param {String} [options.origin] Value of the `Origin` or\n *     `Sec-WebSocket-Origin` header\n * @param {(Boolean|Object)} [options.perMessageDeflate=true] Enable/disable\n *     permessage-deflate\n * @param {Number} [options.protocolVersion=13] Value of the\n *     `Sec-WebSocket-Version` header\n * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n *     not to skip UTF-8 validation for text and close messages\n * @private\n */ function initAsClient(websocket, address, protocols, options) {\n    const opts = {\n        allowSynchronousEvents: true,\n        autoPong: true,\n        protocolVersion: protocolVersions[1],\n        maxPayload: 100 * 1024 * 1024,\n        skipUTF8Validation: false,\n        perMessageDeflate: true,\n        followRedirects: false,\n        maxRedirects: 10,\n        ...options,\n        socketPath: undefined,\n        hostname: undefined,\n        protocol: undefined,\n        timeout: undefined,\n        method: \"GET\",\n        host: undefined,\n        path: undefined,\n        port: undefined\n    };\n    websocket._autoPong = opts.autoPong;\n    if (!protocolVersions.includes(opts.protocolVersion)) {\n        throw new RangeError(`Unsupported protocol version: ${opts.protocolVersion} ` + `(supported versions: ${protocolVersions.join(\", \")})`);\n    }\n    let parsedUrl;\n    if (address instanceof URL) {\n        parsedUrl = address;\n    } else {\n        try {\n            parsedUrl = new URL(address);\n        } catch (e) {\n            throw new SyntaxError(`Invalid URL: ${address}`);\n        }\n    }\n    if (parsedUrl.protocol === \"http:\") {\n        parsedUrl.protocol = \"ws:\";\n    } else if (parsedUrl.protocol === \"https:\") {\n        parsedUrl.protocol = \"wss:\";\n    }\n    websocket._url = parsedUrl.href;\n    const isSecure = parsedUrl.protocol === \"wss:\";\n    const isIpcUrl = parsedUrl.protocol === \"ws+unix:\";\n    let invalidUrlMessage;\n    if (parsedUrl.protocol !== \"ws:\" && !isSecure && !isIpcUrl) {\n        invalidUrlMessage = 'The URL\\'s protocol must be one of \"ws:\", \"wss:\", ' + '\"http:\", \"https:\", or \"ws+unix:\"';\n    } else if (isIpcUrl && !parsedUrl.pathname) {\n        invalidUrlMessage = \"The URL's pathname is empty\";\n    } else if (parsedUrl.hash) {\n        invalidUrlMessage = \"The URL contains a fragment identifier\";\n    }\n    if (invalidUrlMessage) {\n        const err = new SyntaxError(invalidUrlMessage);\n        if (websocket._redirects === 0) {\n            throw err;\n        } else {\n            emitErrorAndClose(websocket, err);\n            return;\n        }\n    }\n    const defaultPort = isSecure ? 443 : 80;\n    const key = randomBytes(16).toString(\"base64\");\n    const request = isSecure ? https.request : http.request;\n    const protocolSet = new Set();\n    let perMessageDeflate;\n    opts.createConnection = opts.createConnection || (isSecure ? tlsConnect : netConnect);\n    opts.defaultPort = opts.defaultPort || defaultPort;\n    opts.port = parsedUrl.port || defaultPort;\n    opts.host = parsedUrl.hostname.startsWith(\"[\") ? parsedUrl.hostname.slice(1, -1) : parsedUrl.hostname;\n    opts.headers = {\n        ...opts.headers,\n        \"Sec-WebSocket-Version\": opts.protocolVersion,\n        \"Sec-WebSocket-Key\": key,\n        Connection: \"Upgrade\",\n        Upgrade: \"websocket\"\n    };\n    opts.path = parsedUrl.pathname + parsedUrl.search;\n    opts.timeout = opts.handshakeTimeout;\n    if (opts.perMessageDeflate) {\n        perMessageDeflate = new PerMessageDeflate(opts.perMessageDeflate !== true ? opts.perMessageDeflate : {}, false, opts.maxPayload);\n        opts.headers[\"Sec-WebSocket-Extensions\"] = format({\n            [PerMessageDeflate.extensionName]: perMessageDeflate.offer()\n        });\n    }\n    if (protocols.length) {\n        for (const protocol of protocols){\n            if (typeof protocol !== \"string\" || !subprotocolRegex.test(protocol) || protocolSet.has(protocol)) {\n                throw new SyntaxError(\"An invalid or duplicated subprotocol was specified\");\n            }\n            protocolSet.add(protocol);\n        }\n        opts.headers[\"Sec-WebSocket-Protocol\"] = protocols.join(\",\");\n    }\n    if (opts.origin) {\n        if (opts.protocolVersion < 13) {\n            opts.headers[\"Sec-WebSocket-Origin\"] = opts.origin;\n        } else {\n            opts.headers.Origin = opts.origin;\n        }\n    }\n    if (parsedUrl.username || parsedUrl.password) {\n        opts.auth = `${parsedUrl.username}:${parsedUrl.password}`;\n    }\n    if (isIpcUrl) {\n        const parts = opts.path.split(\":\");\n        opts.socketPath = parts[0];\n        opts.path = parts[1];\n    }\n    let req;\n    if (opts.followRedirects) {\n        if (websocket._redirects === 0) {\n            websocket._originalIpc = isIpcUrl;\n            websocket._originalSecure = isSecure;\n            websocket._originalHostOrSocketPath = isIpcUrl ? opts.socketPath : parsedUrl.host;\n            const headers = options && options.headers;\n            //\n            // Shallow copy the user provided options so that headers can be changed\n            // without mutating the original object.\n            //\n            options = {\n                ...options,\n                headers: {}\n            };\n            if (headers) {\n                for (const [key, value] of Object.entries(headers)){\n                    options.headers[key.toLowerCase()] = value;\n                }\n            }\n        } else if (websocket.listenerCount(\"redirect\") === 0) {\n            const isSameHost = isIpcUrl ? websocket._originalIpc ? opts.socketPath === websocket._originalHostOrSocketPath : false : websocket._originalIpc ? false : parsedUrl.host === websocket._originalHostOrSocketPath;\n            if (!isSameHost || websocket._originalSecure && !isSecure) {\n                //\n                // Match curl 7.77.0 behavior and drop the following headers. These\n                // headers are also dropped when following a redirect to a subdomain.\n                //\n                delete opts.headers.authorization;\n                delete opts.headers.cookie;\n                if (!isSameHost) delete opts.headers.host;\n                opts.auth = undefined;\n            }\n        }\n        //\n        // Match curl 7.77.0 behavior and make the first `Authorization` header win.\n        // If the `Authorization` header is set, then there is nothing to do as it\n        // will take precedence.\n        //\n        if (opts.auth && !options.headers.authorization) {\n            options.headers.authorization = \"Basic \" + Buffer.from(opts.auth).toString(\"base64\");\n        }\n        req = websocket._req = request(opts);\n        if (websocket._redirects) {\n            //\n            // Unlike what is done for the `'upgrade'` event, no early exit is\n            // triggered here if the user calls `websocket.close()` or\n            // `websocket.terminate()` from a listener of the `'redirect'` event. This\n            // is because the user can also call `request.destroy()` with an error\n            // before calling `websocket.close()` or `websocket.terminate()` and this\n            // would result in an error being emitted on the `request` object with no\n            // `'error'` event listeners attached.\n            //\n            websocket.emit(\"redirect\", websocket.url, req);\n        }\n    } else {\n        req = websocket._req = request(opts);\n    }\n    if (opts.timeout) {\n        req.on(\"timeout\", ()=>{\n            abortHandshake(websocket, req, \"Opening handshake has timed out\");\n        });\n    }\n    req.on(\"error\", (err)=>{\n        if (req === null || req[kAborted]) return;\n        req = websocket._req = null;\n        emitErrorAndClose(websocket, err);\n    });\n    req.on(\"response\", (res)=>{\n        const location = res.headers.location;\n        const statusCode = res.statusCode;\n        if (location && opts.followRedirects && statusCode >= 300 && statusCode < 400) {\n            if (++websocket._redirects > opts.maxRedirects) {\n                abortHandshake(websocket, req, \"Maximum redirects exceeded\");\n                return;\n            }\n            req.abort();\n            let addr;\n            try {\n                addr = new URL(location, address);\n            } catch (e) {\n                const err = new SyntaxError(`Invalid URL: ${location}`);\n                emitErrorAndClose(websocket, err);\n                return;\n            }\n            initAsClient(websocket, addr, protocols, options);\n        } else if (!websocket.emit(\"unexpected-response\", req, res)) {\n            abortHandshake(websocket, req, `Unexpected server response: ${res.statusCode}`);\n        }\n    });\n    req.on(\"upgrade\", (res, socket, head)=>{\n        websocket.emit(\"upgrade\", res);\n        //\n        // The user may have closed the connection from a listener of the\n        // `'upgrade'` event.\n        //\n        if (websocket.readyState !== WebSocket.CONNECTING) return;\n        req = websocket._req = null;\n        const upgrade = res.headers.upgrade;\n        if (upgrade === undefined || upgrade.toLowerCase() !== \"websocket\") {\n            abortHandshake(websocket, socket, \"Invalid Upgrade header\");\n            return;\n        }\n        const digest = createHash(\"sha1\").update(key + GUID).digest(\"base64\");\n        if (res.headers[\"sec-websocket-accept\"] !== digest) {\n            abortHandshake(websocket, socket, \"Invalid Sec-WebSocket-Accept header\");\n            return;\n        }\n        const serverProt = res.headers[\"sec-websocket-protocol\"];\n        let protError;\n        if (serverProt !== undefined) {\n            if (!protocolSet.size) {\n                protError = \"Server sent a subprotocol but none was requested\";\n            } else if (!protocolSet.has(serverProt)) {\n                protError = \"Server sent an invalid subprotocol\";\n            }\n        } else if (protocolSet.size) {\n            protError = \"Server sent no subprotocol\";\n        }\n        if (protError) {\n            abortHandshake(websocket, socket, protError);\n            return;\n        }\n        if (serverProt) websocket._protocol = serverProt;\n        const secWebSocketExtensions = res.headers[\"sec-websocket-extensions\"];\n        if (secWebSocketExtensions !== undefined) {\n            if (!perMessageDeflate) {\n                const message = \"Server sent a Sec-WebSocket-Extensions header but no extension \" + \"was requested\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            let extensions;\n            try {\n                extensions = parse(secWebSocketExtensions);\n            } catch (err) {\n                const message = \"Invalid Sec-WebSocket-Extensions header\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            const extensionNames = Object.keys(extensions);\n            if (extensionNames.length !== 1 || extensionNames[0] !== PerMessageDeflate.extensionName) {\n                const message = \"Server indicated an extension that was not requested\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            try {\n                perMessageDeflate.accept(extensions[PerMessageDeflate.extensionName]);\n            } catch (err) {\n                const message = \"Invalid Sec-WebSocket-Extensions header\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            websocket._extensions[PerMessageDeflate.extensionName] = perMessageDeflate;\n        }\n        websocket.setSocket(socket, head, {\n            allowSynchronousEvents: opts.allowSynchronousEvents,\n            generateMask: opts.generateMask,\n            maxPayload: opts.maxPayload,\n            skipUTF8Validation: opts.skipUTF8Validation\n        });\n    });\n    if (opts.finishRequest) {\n        opts.finishRequest(req, websocket);\n    } else {\n        req.end();\n    }\n}\n/**\n * Emit the `'error'` and `'close'` events.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {Error} The error to emit\n * @private\n */ function emitErrorAndClose(websocket, err) {\n    websocket._readyState = WebSocket.CLOSING;\n    //\n    // The following assignment is practically useless and is done only for\n    // consistency.\n    //\n    websocket._errorEmitted = true;\n    websocket.emit(\"error\", err);\n    websocket.emitClose();\n}\n/**\n * Create a `net.Socket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {net.Socket} The newly created socket used to start the connection\n * @private\n */ function netConnect(options) {\n    options.path = options.socketPath;\n    return net.connect(options);\n}\n/**\n * Create a `tls.TLSSocket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {tls.TLSSocket} The newly created socket used to start the connection\n * @private\n */ function tlsConnect(options) {\n    options.path = undefined;\n    if (!options.servername && options.servername !== \"\") {\n        options.servername = net.isIP(options.host) ? \"\" : options.host;\n    }\n    return tls.connect(options);\n}\n/**\n * Abort the handshake and emit an error.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {(http.ClientRequest|net.Socket|tls.Socket)} stream The request to\n *     abort or the socket to destroy\n * @param {String} message The error message\n * @private\n */ function abortHandshake(websocket, stream, message) {\n    websocket._readyState = WebSocket.CLOSING;\n    const err = new Error(message);\n    Error.captureStackTrace(err, abortHandshake);\n    if (stream.setHeader) {\n        stream[kAborted] = true;\n        stream.abort();\n        if (stream.socket && !stream.socket.destroyed) {\n            //\n            // On Node.js >= 14.3.0 `request.abort()` does not destroy the socket if\n            // called after the request completed. See\n            // https://github.com/websockets/ws/issues/1869.\n            //\n            stream.socket.destroy();\n        }\n        process.nextTick(emitErrorAndClose, websocket, err);\n    } else {\n        stream.destroy(err);\n        stream.once(\"error\", websocket.emit.bind(websocket, \"error\"));\n        stream.once(\"close\", websocket.emitClose.bind(websocket));\n    }\n}\n/**\n * Handle cases where the `ping()`, `pong()`, or `send()` methods are called\n * when the `readyState` attribute is `CLOSING` or `CLOSED`.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {*} [data] The data to send\n * @param {Function} [cb] Callback\n * @private\n */ function sendAfterClose(websocket, data, cb) {\n    if (data) {\n        const length = isBlob(data) ? data.size : toBuffer(data).length;\n        //\n        // The `_bufferedAmount` property is used only when the peer is a client and\n        // the opening handshake fails. Under these circumstances, in fact, the\n        // `setSocket()` method is not called, so the `_socket` and `_sender`\n        // properties are set to `null`.\n        //\n        if (websocket._socket) websocket._sender._bufferedBytes += length;\n        else websocket._bufferedAmount += length;\n    }\n    if (cb) {\n        const err = new Error(`WebSocket is not open: readyState ${websocket.readyState} ` + `(${readyStates[websocket.readyState]})`);\n        process.nextTick(cb, err);\n    }\n}\n/**\n * The listener of the `Receiver` `'conclude'` event.\n *\n * @param {Number} code The status code\n * @param {Buffer} reason The reason for closing\n * @private\n */ function receiverOnConclude(code, reason) {\n    const websocket = this[kWebSocket];\n    websocket._closeFrameReceived = true;\n    websocket._closeMessage = reason;\n    websocket._closeCode = code;\n    if (websocket._socket[kWebSocket] === undefined) return;\n    websocket._socket.removeListener(\"data\", socketOnData);\n    process.nextTick(resume, websocket._socket);\n    if (code === 1005) websocket.close();\n    else websocket.close(code, reason);\n}\n/**\n * The listener of the `Receiver` `'drain'` event.\n *\n * @private\n */ function receiverOnDrain() {\n    const websocket = this[kWebSocket];\n    if (!websocket.isPaused) websocket._socket.resume();\n}\n/**\n * The listener of the `Receiver` `'error'` event.\n *\n * @param {(RangeError|Error)} err The emitted error\n * @private\n */ function receiverOnError(err) {\n    const websocket = this[kWebSocket];\n    if (websocket._socket[kWebSocket] !== undefined) {\n        websocket._socket.removeListener(\"data\", socketOnData);\n        //\n        // On Node.js < 14.0.0 the `'error'` event is emitted synchronously. See\n        // https://github.com/websockets/ws/issues/1940.\n        //\n        process.nextTick(resume, websocket._socket);\n        websocket.close(err[kStatusCode]);\n    }\n    if (!websocket._errorEmitted) {\n        websocket._errorEmitted = true;\n        websocket.emit(\"error\", err);\n    }\n}\n/**\n * The listener of the `Receiver` `'finish'` event.\n *\n * @private\n */ function receiverOnFinish() {\n    this[kWebSocket].emitClose();\n}\n/**\n * The listener of the `Receiver` `'message'` event.\n *\n * @param {Buffer|ArrayBuffer|Buffer[])} data The message\n * @param {Boolean} isBinary Specifies whether the message is binary or not\n * @private\n */ function receiverOnMessage(data, isBinary) {\n    this[kWebSocket].emit(\"message\", data, isBinary);\n}\n/**\n * The listener of the `Receiver` `'ping'` event.\n *\n * @param {Buffer} data The data included in the ping frame\n * @private\n */ function receiverOnPing(data) {\n    const websocket = this[kWebSocket];\n    if (websocket._autoPong) websocket.pong(data, !this._isServer, NOOP);\n    websocket.emit(\"ping\", data);\n}\n/**\n * The listener of the `Receiver` `'pong'` event.\n *\n * @param {Buffer} data The data included in the pong frame\n * @private\n */ function receiverOnPong(data) {\n    this[kWebSocket].emit(\"pong\", data);\n}\n/**\n * Resume a readable stream\n *\n * @param {Readable} stream The readable stream\n * @private\n */ function resume(stream) {\n    stream.resume();\n}\n/**\n * The `Sender` error event handler.\n *\n * @param {Error} The error\n * @private\n */ function senderOnError(err) {\n    const websocket = this[kWebSocket];\n    if (websocket.readyState === WebSocket.CLOSED) return;\n    if (websocket.readyState === WebSocket.OPEN) {\n        websocket._readyState = WebSocket.CLOSING;\n        setCloseTimer(websocket);\n    }\n    //\n    // `socket.end()` is used instead of `socket.destroy()` to allow the other\n    // peer to finish sending queued data. There is no need to set a timer here\n    // because `CLOSING` means that it is already set or not needed.\n    //\n    this._socket.end();\n    if (!websocket._errorEmitted) {\n        websocket._errorEmitted = true;\n        websocket.emit(\"error\", err);\n    }\n}\n/**\n * Set a timer to destroy the underlying raw socket of a WebSocket.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @private\n */ function setCloseTimer(websocket) {\n    websocket._closeTimer = setTimeout(websocket._socket.destroy.bind(websocket._socket), closeTimeout);\n}\n/**\n * The listener of the socket `'close'` event.\n *\n * @private\n */ function socketOnClose() {\n    const websocket = this[kWebSocket];\n    this.removeListener(\"close\", socketOnClose);\n    this.removeListener(\"data\", socketOnData);\n    this.removeListener(\"end\", socketOnEnd);\n    websocket._readyState = WebSocket.CLOSING;\n    let chunk;\n    //\n    // The close frame might not have been received or the `'end'` event emitted,\n    // for example, if the socket was destroyed due to an error. Ensure that the\n    // `receiver` stream is closed after writing any remaining buffered data to\n    // it. If the readable side of the socket is in flowing mode then there is no\n    // buffered data as everything has been already written and `readable.read()`\n    // will return `null`. If instead, the socket is paused, any possible buffered\n    // data will be read as a single chunk.\n    //\n    if (!this._readableState.endEmitted && !websocket._closeFrameReceived && !websocket._receiver._writableState.errorEmitted && (chunk = websocket._socket.read()) !== null) {\n        websocket._receiver.write(chunk);\n    }\n    websocket._receiver.end();\n    this[kWebSocket] = undefined;\n    clearTimeout(websocket._closeTimer);\n    if (websocket._receiver._writableState.finished || websocket._receiver._writableState.errorEmitted) {\n        websocket.emitClose();\n    } else {\n        websocket._receiver.on(\"error\", receiverOnFinish);\n        websocket._receiver.on(\"finish\", receiverOnFinish);\n    }\n}\n/**\n * The listener of the socket `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */ function socketOnData(chunk) {\n    if (!this[kWebSocket]._receiver.write(chunk)) {\n        this.pause();\n    }\n}\n/**\n * The listener of the socket `'end'` event.\n *\n * @private\n */ function socketOnEnd() {\n    const websocket = this[kWebSocket];\n    websocket._readyState = WebSocket.CLOSING;\n    websocket._receiver.end();\n    this.end();\n}\n/**\n * The listener of the socket `'error'` event.\n *\n * @private\n */ function socketOnError() {\n    const websocket = this[kWebSocket];\n    this.removeListener(\"error\", socketOnError);\n    this.on(\"error\", NOOP);\n    if (websocket) {\n        websocket._readyState = WebSocket.CLOSING;\n        this.destroy();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/websocket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/wrapper.mjs":
/*!*************************************!*\
  !*** ./node_modules/ws/wrapper.mjs ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Receiver: () => (/* reexport default export from named module */ _lib_receiver_js__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   Sender: () => (/* reexport default export from named module */ _lib_sender_js__WEBPACK_IMPORTED_MODULE_2__),\n/* harmony export */   WebSocket: () => (/* reexport default export from named module */ _lib_websocket_js__WEBPACK_IMPORTED_MODULE_3__),\n/* harmony export */   WebSocketServer: () => (/* reexport default export from named module */ _lib_websocket_server_js__WEBPACK_IMPORTED_MODULE_4__),\n/* harmony export */   createWebSocketStream: () => (/* reexport default export from named module */ _lib_stream_js__WEBPACK_IMPORTED_MODULE_0__),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_stream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/stream.js */ \"(ssr)/./node_modules/ws/lib/stream.js\");\n/* harmony import */ var _lib_receiver_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/receiver.js */ \"(ssr)/./node_modules/ws/lib/receiver.js\");\n/* harmony import */ var _lib_sender_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/sender.js */ \"(ssr)/./node_modules/ws/lib/sender.js\");\n/* harmony import */ var _lib_websocket_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/websocket.js */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\n/* harmony import */ var _lib_websocket_server_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/websocket-server.js */ \"(ssr)/./node_modules/ws/lib/websocket-server.js\");\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_lib_websocket_js__WEBPACK_IMPORTED_MODULE_3__);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3Mvd3JhcHBlci5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBb0Q7QUFDWDtBQUNKO0FBQ007QUFDYTtBQUV1QjtBQUMvRSxpRUFBZUcsOENBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy93cy93cmFwcGVyLm1qcz9jNWRlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVXZWJTb2NrZXRTdHJlYW0gZnJvbSAnLi9saWIvc3RyZWFtLmpzJztcbmltcG9ydCBSZWNlaXZlciBmcm9tICcuL2xpYi9yZWNlaXZlci5qcyc7XG5pbXBvcnQgU2VuZGVyIGZyb20gJy4vbGliL3NlbmRlci5qcyc7XG5pbXBvcnQgV2ViU29ja2V0IGZyb20gJy4vbGliL3dlYnNvY2tldC5qcyc7XG5pbXBvcnQgV2ViU29ja2V0U2VydmVyIGZyb20gJy4vbGliL3dlYnNvY2tldC1zZXJ2ZXIuanMnO1xuXG5leHBvcnQgeyBjcmVhdGVXZWJTb2NrZXRTdHJlYW0sIFJlY2VpdmVyLCBTZW5kZXIsIFdlYlNvY2tldCwgV2ViU29ja2V0U2VydmVyIH07XG5leHBvcnQgZGVmYXVsdCBXZWJTb2NrZXQ7XG4iXSwibmFtZXMiOlsiY3JlYXRlV2ViU29ja2V0U3RyZWFtIiwiUmVjZWl2ZXIiLCJTZW5kZXIiLCJXZWJTb2NrZXQiLCJXZWJTb2NrZXRTZXJ2ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/wrapper.mjs\n");

/***/ })

};
;