"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-strikethrough";
exports.ids = ["vendor-chunks/micromark-extension-gfm-strikethrough"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-strikethrough/dev/lib/html.js":
/*!****************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-strikethrough/dev/lib/html.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmStrikethroughHtml: () => (/* binding */ gfmStrikethroughHtml)\n/* harmony export */ });\n/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */ /**\n * Create an HTML extension for `micromark` to support GFM strikethrough when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions`, to\n *   support GFM strikethrough when serializing to HTML.\n */ function gfmStrikethroughHtml() {\n    return {\n        enter: {\n            strikethrough () {\n                this.tag(\"<del>\");\n            }\n        },\n        exit: {\n            strikethrough () {\n                this.tag(\"</del>\");\n            }\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tc3RyaWtldGhyb3VnaC9kZXYvbGliL2h0bWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRUQ7Ozs7Ozs7Q0FPQyxHQUNNLFNBQVNBO0lBQ2QsT0FBTztRQUNMQyxPQUFPO1lBQ0xDO2dCQUNFLElBQUksQ0FBQ0MsR0FBRyxDQUFDO1lBQ1g7UUFDRjtRQUNBQyxNQUFNO1lBQ0pGO2dCQUNFLElBQUksQ0FBQ0MsR0FBRyxDQUFDO1lBQ1g7UUFDRjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9taWNyb21hcmstZXh0ZW5zaW9uLWdmbS1zdHJpa2V0aHJvdWdoL2Rldi9saWIvaHRtbC5qcz9kZDYxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SHRtbEV4dGVuc2lvbn0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuLyoqXG4gKiBDcmVhdGUgYW4gSFRNTCBleHRlbnNpb24gZm9yIGBtaWNyb21hcmtgIHRvIHN1cHBvcnQgR0ZNIHN0cmlrZXRocm91Z2ggd2hlblxuICogc2VyaWFsaXppbmcgdG8gSFRNTC5cbiAqXG4gKiBAcmV0dXJucyB7SHRtbEV4dGVuc2lvbn1cbiAqICAgRXh0ZW5zaW9uIGZvciBgbWljcm9tYXJrYCB0aGF0IGNhbiBiZSBwYXNzZWQgaW4gYGh0bWxFeHRlbnNpb25zYCwgdG9cbiAqICAgc3VwcG9ydCBHRk0gc3RyaWtldGhyb3VnaCB3aGVuIHNlcmlhbGl6aW5nIHRvIEhUTUwuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZm1TdHJpa2V0aHJvdWdoSHRtbCgpIHtcbiAgcmV0dXJuIHtcbiAgICBlbnRlcjoge1xuICAgICAgc3RyaWtldGhyb3VnaCgpIHtcbiAgICAgICAgdGhpcy50YWcoJzxkZWw+JylcbiAgICAgIH1cbiAgICB9LFxuICAgIGV4aXQ6IHtcbiAgICAgIHN0cmlrZXRocm91Z2goKSB7XG4gICAgICAgIHRoaXMudGFnKCc8L2RlbD4nKVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbImdmbVN0cmlrZXRocm91Z2hIdG1sIiwiZW50ZXIiLCJzdHJpa2V0aHJvdWdoIiwidGFnIiwiZXhpdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-strikethrough/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-strikethrough/dev/lib/syntax.js":
/*!******************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-strikethrough/dev/lib/syntax.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmStrikethrough: () => (/* binding */ gfmStrikethrough)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-classify-character */ \"(ssr)/./node_modules/micromark-util-classify-character/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(ssr)/./node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Options} from 'micromark-extension-gfm-strikethrough'\n * @import {Event, Extension, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */ \n\n\n\n\n/**\n * Create an extension for `micromark` to enable GFM strikethrough syntax.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration.\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions`, to\n *   enable GFM strikethrough syntax.\n */ function gfmStrikethrough(options) {\n    const options_ = options || {};\n    let single = options_.singleTilde;\n    const tokenizer = {\n        name: \"strikethrough\",\n        tokenize: tokenizeStrikethrough,\n        resolveAll: resolveAllStrikethrough\n    };\n    if (single === null || single === undefined) {\n        single = true;\n    }\n    return {\n        text: {\n            [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde]: tokenizer\n        },\n        insideSpan: {\n            null: [\n                tokenizer\n            ]\n        },\n        attentionMarkers: {\n            null: [\n                micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde\n            ]\n        }\n    };\n    /**\n   * Take events and resolve strikethrough.\n   *\n   * @type {Resolver}\n   */ function resolveAllStrikethrough(events, context) {\n        let index = -1;\n        // Walk through all events.\n        while(++index < events.length){\n            // Find a token that can close.\n            if (events[index][0] === \"enter\" && events[index][1].type === \"strikethroughSequenceTemporary\" && events[index][1]._close) {\n                let open = index;\n                // Now walk back to find an opener.\n                while(open--){\n                    // Find a token that can open the closer.\n                    if (events[open][0] === \"exit\" && events[open][1].type === \"strikethroughSequenceTemporary\" && events[open][1]._open && // If the sizes are the same:\n                    events[index][1].end.offset - events[index][1].start.offset === events[open][1].end.offset - events[open][1].start.offset) {\n                        events[index][1].type = \"strikethroughSequence\";\n                        events[open][1].type = \"strikethroughSequence\";\n                        /** @type {Token} */ const strikethrough = {\n                            type: \"strikethrough\",\n                            start: Object.assign({}, events[open][1].start),\n                            end: Object.assign({}, events[index][1].end)\n                        };\n                        /** @type {Token} */ const text = {\n                            type: \"strikethroughText\",\n                            start: Object.assign({}, events[open][1].end),\n                            end: Object.assign({}, events[index][1].start)\n                        };\n                        // Opening.\n                        /** @type {Array<Event>} */ const nextEvents = [\n                            [\n                                \"enter\",\n                                strikethrough,\n                                context\n                            ],\n                            [\n                                \"enter\",\n                                events[open][1],\n                                context\n                            ],\n                            [\n                                \"exit\",\n                                events[open][1],\n                                context\n                            ],\n                            [\n                                \"enter\",\n                                text,\n                                context\n                            ]\n                        ];\n                        const insideSpan = context.parser.constructs.insideSpan.null;\n                        if (insideSpan) {\n                            // Between.\n                            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.splice)(nextEvents, nextEvents.length, 0, (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_2__.resolveAll)(insideSpan, events.slice(open + 1, index), context));\n                        }\n                        // Closing.\n                        (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.splice)(nextEvents, nextEvents.length, 0, [\n                            [\n                                \"exit\",\n                                text,\n                                context\n                            ],\n                            [\n                                \"enter\",\n                                events[index][1],\n                                context\n                            ],\n                            [\n                                \"exit\",\n                                events[index][1],\n                                context\n                            ],\n                            [\n                                \"exit\",\n                                strikethrough,\n                                context\n                            ]\n                        ]);\n                        (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.splice)(events, open - 1, index - open + 3, nextEvents);\n                        index = open + nextEvents.length - 2;\n                        break;\n                    }\n                }\n            }\n        }\n        index = -1;\n        while(++index < events.length){\n            if (events[index][1].type === \"strikethroughSequenceTemporary\") {\n                events[index][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.data;\n            }\n        }\n        return events;\n    }\n    /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */ function tokenizeStrikethrough(effects, ok, nok) {\n        const previous = this.previous;\n        const events = this.events;\n        let size = 0;\n        return start;\n        /** @type {State} */ function start(code) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde, \"expected `~`\");\n            if (previous === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde && events[events.length - 1][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.characterEscape) {\n                return nok(code);\n            }\n            effects.enter(\"strikethroughSequenceTemporary\");\n            return more(code);\n        }\n        /** @type {State} */ function more(code) {\n            const before = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_5__.classifyCharacter)(previous);\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde) {\n                // If this is the third marker, exit.\n                if (size > 1) return nok(code);\n                effects.consume(code);\n                size++;\n                return more;\n            }\n            if (size < 2 && !single) return nok(code);\n            const token = effects.exit(\"strikethroughSequenceTemporary\");\n            const after = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_5__.classifyCharacter)(code);\n            token._open = !after || after === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.constants.attentionSideAfter && Boolean(before);\n            token._close = !before || before === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.constants.attentionSideAfter && Boolean(after);\n            return ok(code);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-strikethrough/dev/lib/syntax.js\n");

/***/ })

};
;