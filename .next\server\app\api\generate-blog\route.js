"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-blog/route";
exports.ids = ["app/api/generate-blog/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-blog%2Froute&page=%2Fapi%2Fgenerate-blog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-blog%2Froute.ts&appDir=E%3A%5Cauto-blog%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cauto-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-blog%2Froute&page=%2Fapi%2Fgenerate-blog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-blog%2Froute.ts&appDir=E%3A%5Cauto-blog%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cauto-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_auto_blog_app_api_generate_blog_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/generate-blog/route.ts */ \"(rsc)/./app/api/generate-blog/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-blog/route\",\n        pathname: \"/api/generate-blog\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-blog/route\"\n    },\n    resolvedPagePath: \"E:\\\\auto-blog\\\\app\\\\api\\\\generate-blog\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_auto_blog_app_api_generate_blog_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/generate-blog/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-blog%2Froute&page=%2Fapi%2Fgenerate-blog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-blog%2Froute.ts&appDir=E%3A%5Cauto-blog%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cauto-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/generate-blog/route.ts":
/*!****************************************!*\
  !*** ./app/api/generate-blog/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\nasync function POST(request) {\n    try {\n        // 确保请求体正确解析\n        const body = await request.json();\n        const { keywords, title, prompt, language, seriesContext } = body;\n        // 验证必需参数\n        if (!keywords || !prompt) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"缺少必需参数：keywords 和 prompt\"\n            }, {\n                status: 400\n            });\n        }\n        // 检查API密钥\n        const apiKey = process.env.QWEN_API_KEY;\n        if (!apiKey) {\n            console.warn(\"未配置QWEN_API_KEY，使用模拟数据\");\n            return generateMockContent(body);\n        }\n        // 构建发送给Qwen的提示词\n        const systemPrompt = `你是一个专业的博客写手，擅长创作高质量、SEO优化的博文内容。请根据用户提供的信息生成一篇完整的博文。\r\n\r\n要求：\r\n1. 内容要原创、有价值、结构清晰\r\n2. 标题要吸引人且包含关键词\r\n3. 内容要包含适当的标题层级（H1, H2, H3等）\r\n4. 适当使用markdown格式\r\n5. 内容长度适中（1000-2000字）\r\n6. 语言：${language === \"zh\" ? \"中文\" : language === \"en\" ? \"English\" : language}\r\n\r\n${seriesContext ? `系列上下文：\\n${seriesContext}\\n请确保本文与系列其他文章保持连贯性。\\n` : \"\"}\r\n\r\n请直接返回博文内容，不要包含额外的说明。`;\n        const userPrompt = `${prompt}\r\n\r\n关键词：${keywords}\r\n${title ? `建议标题：${title}` : \"\"}\r\n\r\n请生成一篇关于\"${keywords}\"的博文。`;\n        // 调用Qwen API (兼容模式)\n        const requestBody = JSON.stringify({\n            model: \"qwen-turbo\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: userPrompt\n                }\n            ],\n            temperature: 0.7,\n            top_p: 0.8,\n            max_tokens: 2000\n        });\n        const qwenResponse = await fetch(\"https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions\", {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": `Bearer ${apiKey}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: requestBody\n        });\n        if (!qwenResponse.ok) {\n            const errorText = await qwenResponse.text();\n            console.error(\"Qwen API错误:\", errorText);\n            throw new Error(`Qwen API调用失败: ${qwenResponse.status} ${errorText}`);\n        }\n        const qwenData = await qwenResponse.json();\n        if (!qwenData.choices || qwenData.choices.length === 0) {\n            throw new Error(\"Qwen API返回数据格式错误\");\n        }\n        const generatedContent = qwenData.choices[0].message.content;\n        // 从生成的内容中提取标题和正文\n        const { extractedTitle, content } = extractTitleAndContent(generatedContent, keywords, title);\n        // 生成SEO信息\n        const seoTitle = generateSEOTitle(extractedTitle, keywords);\n        const seoDescription = generateSEODescription(content, keywords);\n        const response = {\n            title: extractedTitle,\n            content: content,\n            seoTitle: seoTitle,\n            seoDescription: seoDescription,\n            usage: qwenData.usage,\n            model: \"qwen-turbo\"\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response);\n    } catch (error) {\n        console.error(\"博文生成错误:\", error);\n        // 检查是否是编码相关错误\n        if (error instanceof Error && (error.message.includes(\"ByteString\") || error.message.includes(\"character\") || error.message.includes(\"greater than 255\"))) {\n            console.log(\"检测到编码错误，使用模拟数据\");\n            try {\n                const body = await request.json();\n                return generateMockContent(body);\n            } catch  {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"请求数据格式错误\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // 如果API调用失败，回退到模拟数据\n        if (error instanceof Error && error.message.includes(\"API\")) {\n            console.log(\"API调用失败，使用模拟数据\");\n            try {\n                const body = await request.json();\n                return generateMockContent(body);\n            } catch  {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"请求数据格式错误\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error instanceof Error ? error.message : \"生成博文失败\"\n        }, {\n            status: 500\n        });\n    }\n}\n// 从生成内容中提取标题和正文\nfunction extractTitleAndContent(generatedContent, keywords, suggestedTitle) {\n    const lines = generatedContent.trim().split(\"\\n\");\n    let extractedTitle = suggestedTitle || `关于${keywords}的完整指南`;\n    let content = generatedContent;\n    // 尝试从第一行提取标题\n    if (lines[0] && (lines[0].startsWith(\"#\") || lines[0].includes(keywords))) {\n        extractedTitle = lines[0].replace(/^#+\\s*/, \"\").trim();\n        content = lines.slice(1).join(\"\\n\").trim();\n    }\n    return {\n        extractedTitle,\n        content\n    };\n}\n// 生成SEO标题\nfunction generateSEOTitle(title, keywords) {\n    const year = new Date().getFullYear();\n    if (title.includes(year.toString())) {\n        return title;\n    }\n    return `${title} - 专业指南 | ${year}`;\n}\n// 生成SEO描述\nfunction generateSEODescription(content, keywords) {\n    // 从内容中提取前200个字符作为描述\n    const plainText = content.replace(/#+\\s/g, \"\") // 移除markdown标题标记\n    .replace(/\\*\\*/g, \"\") // 移除加粗标记\n    .replace(/\\n+/g, \" \") // 替换换行为空格\n    .trim();\n    const description = plainText.length > 160 ? plainText.substring(0, 157) + \"...\" : plainText;\n    return description || `详细了解${keywords}的概念、应用和最佳实践。专业的技术指南，帮助您快速掌握相关知识。`;\n}\n// 生成模拟内容（备用方案）\nasync function generateMockContent(body) {\n    const { keywords, title, seriesContext } = body;\n    // 模拟网络延迟\n    await new Promise((resolve)=>setTimeout(resolve, 1000));\n    const generatedTitle = title || `关于${keywords}的完整指南`;\n    const content = `# ${generatedTitle}\r\n\r\n## 简介\r\n\r\n${seriesContext ? \"基于本系列前面的内容，\" : \"\"}本文将深入探讨${keywords}相关的重要概念和实践方法。\r\n\r\n## 主要内容\r\n\r\n### 1. 基础概念\r\n在了解${keywords}之前，我们需要掌握一些基础概念。${keywords}作为当前技术领域的重要组成部分，具有以下特点：\r\n\r\n- 高效性：能够显著提升工作效率\r\n- 可扩展性：支持灵活的扩展和定制\r\n- 易用性：提供友好的用户体验\r\n\r\n### 2. 实践应用\r\n${keywords}在实际应用中有着广泛的用途：\r\n\r\n**应用场景一：**\r\n在企业级应用中，${keywords}可以帮助团队更好地协作和管理项目。\r\n\r\n**应用场景二：**\r\n对于个人用户，${keywords}提供了便捷的解决方案来处理日常任务。\r\n\r\n### 3. 最佳实践\r\n以下是使用${keywords}的一些最佳实践：\r\n\r\n1. **规划先行**：在开始之前，制定详细的实施计划\r\n2. **逐步推进**：采用迭代的方式，逐步完善功能\r\n3. **持续优化**：根据使用反馈，不断改进和优化\r\n4. **团队协作**：确保团队成员都能熟练使用相关工具\r\n5. **文档记录**：维护完善的文档，便于后续维护\r\n\r\n### 4. 注意事项\r\n在使用${keywords}过程中，需要注意以下几点：\r\n\r\n- 数据安全：确保敏感信息得到妥善保护\r\n- 性能优化：定期检查和优化系统性能\r\n- 备份策略：建立完善的数据备份机制\r\n\r\n## 实施建议\r\n\r\n为了更好地应用${keywords}，建议按照以下步骤进行：\r\n\r\n1. **需求分析**：明确具体的业务需求和目标\r\n2. **技术选型**：选择合适的技术栈和工具\r\n3. **原型开发**：快速构建原型验证可行性\r\n4. **正式开发**：基于原型进行正式的开发工作\r\n5. **测试部署**：充分测试后进行生产环境部署\r\n6. **运维监控**：建立完善的运维和监控体系\r\n\r\n## 总结\r\n\r\n通过本文的介绍，我们深入了解了${keywords}的核心概念、实践应用和最佳实践。${keywords}作为一个强大的工具，能够为我们的工作和生活带来显著的便利。\r\n\r\n在实际应用中，建议：\r\n- 根据自身需求选择合适的方案\r\n- 重视团队培训和知识分享\r\n- 保持技术更新和持续学习\r\n\r\n${seriesContext ? \"\\n在下一篇文章中，我们将继续探讨${keywords}的高级应用和未来发展趋势。\" : \"\"}\r\n\r\n希望这些内容对您有所帮助。如果您有任何问题或建议，欢迎随时交流讨论。`;\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        title: generatedTitle,\n        content: content,\n        seoTitle: `${generatedTitle} - 专业指南 | ${new Date().getFullYear()}`,\n        seoDescription: `详细了解${keywords}的概念、应用和最佳实践。专业的技术指南，帮助您快速掌握相关知识。`,\n        usage: {\n            prompt_tokens: 100,\n            completion_tokens: 800,\n            total_tokens: 900\n        },\n        model: \"mock-generator\"\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2dlbmVyYXRlLWJsb2cvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0Q7QUF1QmpELGVBQWVDLEtBQUtDLE9BQW9CO0lBQzdDLElBQUk7UUFDRixZQUFZO1FBQ1osTUFBTUMsT0FBd0IsTUFBTUQsUUFBUUUsSUFBSTtRQUNoRCxNQUFNLEVBQUVDLFFBQVEsRUFBRUMsS0FBSyxFQUFFQyxNQUFNLEVBQUVDLFFBQVEsRUFBRUMsYUFBYSxFQUFFLEdBQUdOO1FBRTdELFNBQVM7UUFDVCxJQUFJLENBQUNFLFlBQVksQ0FBQ0UsUUFBUTtZQUN4QixPQUFPUCxrRkFBWUEsQ0FBQ0ksSUFBSSxDQUN0QjtnQkFBRU0sT0FBTztZQUEyQixHQUNwQztnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsVUFBVTtRQUNWLE1BQU1DLFNBQVNDLFFBQVFDLEdBQUcsQ0FBQ0MsWUFBWTtRQUN2QyxJQUFJLENBQUNILFFBQVE7WUFDWEksUUFBUUMsSUFBSSxDQUFDO1lBQ2IsT0FBT0Msb0JBQW9CZjtRQUM3QjtRQUVBLGdCQUFnQjtRQUNoQixNQUFNZ0IsZUFBZSxDQUFDOzs7Ozs7OztNQVFwQixFQUFFWCxhQUFhLE9BQU8sT0FBT0EsYUFBYSxPQUFPLFlBQVlBLFNBQVM7O0FBRTVFLEVBQUVDLGdCQUFnQixDQUFDLFFBQVEsRUFBRUEsY0FBYyxzQkFBc0IsQ0FBQyxHQUFHLEdBQUc7O29CQUVwRCxDQUFDO1FBRWpCLE1BQU1XLGFBQWEsQ0FBQyxFQUFFYixPQUFPOztJQUU3QixFQUFFRixTQUFTO0FBQ2YsRUFBRUMsUUFBUSxDQUFDLEtBQUssRUFBRUEsTUFBTSxDQUFDLEdBQUcsR0FBRzs7UUFFdkIsRUFBRUQsU0FBUyxLQUFLLENBQUM7UUFFckIsb0JBQW9CO1FBQ3BCLE1BQU1nQixjQUFjQyxLQUFLQyxTQUFTLENBQUM7WUFDakNDLE9BQU87WUFDUEMsVUFBVTtnQkFDUjtvQkFDRUMsTUFBTTtvQkFDTkMsU0FBU1I7Z0JBQ1g7Z0JBQ0E7b0JBQ0VPLE1BQU07b0JBQ05DLFNBQVNQO2dCQUNYO2FBQ0Q7WUFDRFEsYUFBYTtZQUNiQyxPQUFPO1lBQ1BDLFlBQVk7UUFDZDtRQUVBLE1BQU1DLGVBQWUsTUFBTUMsTUFBTSxzRUFBc0U7WUFDckdDLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUV0QixPQUFPLENBQUM7Z0JBQ25DLGdCQUFnQjtZQUNsQjtZQUNBVCxNQUFNa0I7UUFDUjtRQUVBLElBQUksQ0FBQ1UsYUFBYUksRUFBRSxFQUFFO1lBQ3BCLE1BQU1DLFlBQVksTUFBTUwsYUFBYU0sSUFBSTtZQUN6Q3JCLFFBQVFOLEtBQUssQ0FBQyxlQUFlMEI7WUFDN0IsTUFBTSxJQUFJRSxNQUFNLENBQUMsY0FBYyxFQUFFUCxhQUFhcEIsTUFBTSxDQUFDLENBQUMsRUFBRXlCLFVBQVUsQ0FBQztRQUNyRTtRQUVBLE1BQU1HLFdBQXlCLE1BQU1SLGFBQWEzQixJQUFJO1FBRXRELElBQUksQ0FBQ21DLFNBQVNDLE9BQU8sSUFBSUQsU0FBU0MsT0FBTyxDQUFDQyxNQUFNLEtBQUssR0FBRztZQUN0RCxNQUFNLElBQUlILE1BQU07UUFDbEI7UUFFQSxNQUFNSSxtQkFBbUJILFNBQVNDLE9BQU8sQ0FBQyxFQUFFLENBQUNHLE9BQU8sQ0FBQ2hCLE9BQU87UUFFNUQsaUJBQWlCO1FBQ2pCLE1BQU0sRUFBRWlCLGNBQWMsRUFBRWpCLE9BQU8sRUFBRSxHQUFHa0IsdUJBQXVCSCxrQkFBa0JyQyxVQUFVQztRQUV2RixVQUFVO1FBQ1YsTUFBTXdDLFdBQVdDLGlCQUFpQkgsZ0JBQWdCdkM7UUFDbEQsTUFBTTJDLGlCQUFpQkMsdUJBQXVCdEIsU0FBU3RCO1FBRXZELE1BQU02QyxXQUFXO1lBQ2Y1QyxPQUFPc0M7WUFDUGpCLFNBQVNBO1lBQ1RtQixVQUFVQTtZQUNWRSxnQkFBZ0JBO1lBQ2hCRyxPQUFPWixTQUFTWSxLQUFLO1lBQ3JCM0IsT0FBTztRQUNUO1FBRUEsT0FBT3hCLGtGQUFZQSxDQUFDSSxJQUFJLENBQUM4QztJQUUzQixFQUFFLE9BQU94QyxPQUFPO1FBQ2RNLFFBQVFOLEtBQUssQ0FBQyxXQUFXQTtRQUV6QixjQUFjO1FBQ2QsSUFBSUEsaUJBQWlCNEIsU0FDbkI1QixDQUFBQSxNQUFNaUMsT0FBTyxDQUFDUyxRQUFRLENBQUMsaUJBQ3ZCMUMsTUFBTWlDLE9BQU8sQ0FBQ1MsUUFBUSxDQUFDLGdCQUN2QjFDLE1BQU1pQyxPQUFPLENBQUNTLFFBQVEsQ0FBQyxtQkFBa0IsR0FDeEM7WUFDRHBDLFFBQVFxQyxHQUFHLENBQUM7WUFDWixJQUFJO2dCQUNGLE1BQU1sRCxPQUF3QixNQUFNRCxRQUFRRSxJQUFJO2dCQUNoRCxPQUFPYyxvQkFBb0JmO1lBQzdCLEVBQUUsT0FBTTtnQkFDTixPQUFPSCxrRkFBWUEsQ0FBQ0ksSUFBSSxDQUN0QjtvQkFBRU0sT0FBTztnQkFBVyxHQUNwQjtvQkFBRUMsUUFBUTtnQkFBSTtZQUVsQjtRQUNGO1FBRUEsb0JBQW9CO1FBQ3BCLElBQUlELGlCQUFpQjRCLFNBQVM1QixNQUFNaUMsT0FBTyxDQUFDUyxRQUFRLENBQUMsUUFBUTtZQUMzRHBDLFFBQVFxQyxHQUFHLENBQUM7WUFDWixJQUFJO2dCQUNGLE1BQU1sRCxPQUF3QixNQUFNRCxRQUFRRSxJQUFJO2dCQUNoRCxPQUFPYyxvQkFBb0JmO1lBQzdCLEVBQUUsT0FBTTtnQkFDTixPQUFPSCxrRkFBWUEsQ0FBQ0ksSUFBSSxDQUN0QjtvQkFBRU0sT0FBTztnQkFBVyxHQUNwQjtvQkFBRUMsUUFBUTtnQkFBSTtZQUVsQjtRQUNGO1FBRUEsT0FBT1gsa0ZBQVlBLENBQUNJLElBQUksQ0FDdEI7WUFBRU0sT0FBT0EsaUJBQWlCNEIsUUFBUTVCLE1BQU1pQyxPQUFPLEdBQUc7UUFBUyxHQUMzRDtZQUFFaEMsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSxnQkFBZ0I7QUFDaEIsU0FBU2tDLHVCQUF1QkgsZ0JBQXdCLEVBQUVyQyxRQUFnQixFQUFFaUQsY0FBdUI7SUFDakcsTUFBTUMsUUFBUWIsaUJBQWlCYyxJQUFJLEdBQUdDLEtBQUssQ0FBQztJQUM1QyxJQUFJYixpQkFBaUJVLGtCQUFrQixDQUFDLEVBQUUsRUFBRWpELFNBQVMsS0FBSyxDQUFDO0lBQzNELElBQUlzQixVQUFVZTtJQUVkLGFBQWE7SUFDYixJQUFJYSxLQUFLLENBQUMsRUFBRSxJQUFLQSxDQUFBQSxLQUFLLENBQUMsRUFBRSxDQUFDRyxVQUFVLENBQUMsUUFBUUgsS0FBSyxDQUFDLEVBQUUsQ0FBQ0gsUUFBUSxDQUFDL0MsU0FBUSxHQUFJO1FBQ3pFdUMsaUJBQWlCVyxLQUFLLENBQUMsRUFBRSxDQUFDSSxPQUFPLENBQUMsVUFBVSxJQUFJSCxJQUFJO1FBQ3BEN0IsVUFBVTRCLE1BQU1LLEtBQUssQ0FBQyxHQUFHQyxJQUFJLENBQUMsTUFBTUwsSUFBSTtJQUMxQztJQUVBLE9BQU87UUFBRVo7UUFBZ0JqQjtJQUFRO0FBQ25DO0FBRUEsVUFBVTtBQUNWLFNBQVNvQixpQkFBaUJ6QyxLQUFhLEVBQUVELFFBQWdCO0lBQ3ZELE1BQU15RCxPQUFPLElBQUlDLE9BQU9DLFdBQVc7SUFDbkMsSUFBSTFELE1BQU04QyxRQUFRLENBQUNVLEtBQUtHLFFBQVEsS0FBSztRQUNuQyxPQUFPM0Q7SUFDVDtJQUNBLE9BQU8sQ0FBQyxFQUFFQSxNQUFNLFVBQVUsRUFBRXdELEtBQUssQ0FBQztBQUNwQztBQUVBLFVBQVU7QUFDVixTQUFTYix1QkFBdUJ0QixPQUFlLEVBQUV0QixRQUFnQjtJQUMvRCxvQkFBb0I7SUFDcEIsTUFBTTZELFlBQVl2QyxRQUNmZ0MsT0FBTyxDQUFDLFNBQVMsSUFBSSxpQkFBaUI7S0FDdENBLE9BQU8sQ0FBQyxTQUFTLElBQUksU0FBUztLQUM5QkEsT0FBTyxDQUFDLFFBQVEsS0FBSyxVQUFVO0tBQy9CSCxJQUFJO0lBRVAsTUFBTVcsY0FBY0QsVUFBVXpCLE1BQU0sR0FBRyxNQUNuQ3lCLFVBQVVFLFNBQVMsQ0FBQyxHQUFHLE9BQU8sUUFDOUJGO0lBRUosT0FBT0MsZUFBZSxDQUFDLElBQUksRUFBRTlELFNBQVMsZ0NBQWdDLENBQUM7QUFDekU7QUFFQSxlQUFlO0FBQ2YsZUFBZWEsb0JBQW9CZixJQUFxQjtJQUN0RCxNQUFNLEVBQUVFLFFBQVEsRUFBRUMsS0FBSyxFQUFFRyxhQUFhLEVBQUUsR0FBR047SUFFM0MsU0FBUztJQUNULE1BQU0sSUFBSWtFLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7SUFFakQsTUFBTUUsaUJBQWlCbEUsU0FBUyxDQUFDLEVBQUUsRUFBRUQsU0FBUyxLQUFLLENBQUM7SUFDcEQsTUFBTXNCLFVBQVUsQ0FBQyxFQUFFLEVBQUU2QyxlQUFlOzs7O0FBSXRDLEVBQUUvRCxnQkFBZ0IsZ0JBQWdCLEdBQUcsT0FBTyxFQUFFSixTQUFTOzs7OztHQUtwRCxFQUFFQSxTQUFTLGdCQUFnQixFQUFFQSxTQUFTOzs7Ozs7O0FBT3pDLEVBQUVBLFNBQVM7OztRQUdILEVBQUVBLFNBQVM7OztPQUdaLEVBQUVBLFNBQVM7OztLQUdiLEVBQUVBLFNBQVM7Ozs7Ozs7OztHQVNiLEVBQUVBLFNBQVM7Ozs7Ozs7O09BUVAsRUFBRUEsU0FBUzs7Ozs7Ozs7Ozs7ZUFXSCxFQUFFQSxTQUFTLGdCQUFnQixFQUFFQSxTQUFTOzs7Ozs7O0FBT3JELEVBQUVJLGdCQUFnQiw4Q0FBOEMsR0FBRzs7a0NBRWpDLENBQUM7SUFFakMsT0FBT1Qsa0ZBQVlBLENBQUNJLElBQUksQ0FBQztRQUN2QkUsT0FBT2tFO1FBQ1A3QyxTQUFTQTtRQUNUbUIsVUFBVSxDQUFDLEVBQUUwQixlQUFlLFVBQVUsRUFBRSxJQUFJVCxPQUFPQyxXQUFXLEdBQUcsQ0FBQztRQUNsRWhCLGdCQUFnQixDQUFDLElBQUksRUFBRTNDLFNBQVMsZ0NBQWdDLENBQUM7UUFDakU4QyxPQUFPO1lBQ0xzQixlQUFlO1lBQ2ZDLG1CQUFtQjtZQUNuQkMsY0FBYztRQUNoQjtRQUNBbkQsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tYmxvZy1tYW5hZ2VtZW50LXN5c3RlbS8uL2FwcC9hcGkvZ2VuZXJhdGUtYmxvZy9yb3V0ZS50cz8xNDAyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XHJcblxyXG5pbnRlcmZhY2UgR2VuZXJhdGVSZXF1ZXN0IHtcclxuICBrZXl3b3Jkczogc3RyaW5nO1xyXG4gIHRpdGxlPzogc3RyaW5nO1xyXG4gIHByb21wdDogc3RyaW5nO1xyXG4gIGxhbmd1YWdlOiBzdHJpbmc7XHJcbiAgc2VyaWVzQ29udGV4dD86IHN0cmluZztcclxufVxyXG5cclxuaW50ZXJmYWNlIFF3ZW5SZXNwb25zZSB7XHJcbiAgY2hvaWNlczogQXJyYXk8e1xyXG4gICAgbWVzc2FnZToge1xyXG4gICAgICBjb250ZW50OiBzdHJpbmc7XHJcbiAgICB9O1xyXG4gIH0+O1xyXG4gIHVzYWdlPzoge1xyXG4gICAgcHJvbXB0X3Rva2VuczogbnVtYmVyO1xyXG4gICAgY29tcGxldGlvbl90b2tlbnM6IG51bWJlcjtcclxuICAgIHRvdGFsX3Rva2VuczogbnVtYmVyO1xyXG4gIH07XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XHJcbiAgdHJ5IHtcclxuICAgIC8vIOehruS/neivt+axguS9k+ato+ehruino+aekFxyXG4gICAgY29uc3QgYm9keTogR2VuZXJhdGVSZXF1ZXN0ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XHJcbiAgICBjb25zdCB7IGtleXdvcmRzLCB0aXRsZSwgcHJvbXB0LCBsYW5ndWFnZSwgc2VyaWVzQ29udGV4dCB9ID0gYm9keTtcclxuXHJcbiAgICAvLyDpqozor4Hlv4XpnIDlj4LmlbBcclxuICAgIGlmICgha2V5d29yZHMgfHwgIXByb21wdCkge1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXHJcbiAgICAgICAgeyBlcnJvcjogJ+e8uuWwkeW/hemcgOWPguaVsO+8mmtleXdvcmRzIOWSjCBwcm9tcHQnIH0sXHJcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XHJcbiAgICAgICk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8g5qOA5p+lQVBJ5a+G6ZKlXHJcbiAgICBjb25zdCBhcGlLZXkgPSBwcm9jZXNzLmVudi5RV0VOX0FQSV9LRVk7XHJcbiAgICBpZiAoIWFwaUtleSkge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ+acqumFjee9rlFXRU5fQVBJX0tFWe+8jOS9v+eUqOaooeaLn+aVsOaNricpO1xyXG4gICAgICByZXR1cm4gZ2VuZXJhdGVNb2NrQ29udGVudChib2R5KTtcclxuICAgIH1cclxuXHJcbiAgICAvLyDmnoTlu7rlj5HpgIHnu5lRd2Vu55qE5o+Q56S66K+NXHJcbiAgICBjb25zdCBzeXN0ZW1Qcm9tcHQgPSBg5L2g5piv5LiA5Liq5LiT5Lia55qE5Y2a5a6i5YaZ5omL77yM5pOF6ZW/5Yib5L2c6auY6LSo6YeP44CBU0VP5LyY5YyW55qE5Y2a5paH5YaF5a6544CC6K+35qC55o2u55So5oi35o+Q5L6b55qE5L+h5oGv55Sf5oiQ5LiA56+H5a6M5pW055qE5Y2a5paH44CCXHJcblxyXG7opoHmsYLvvJpcclxuMS4g5YaF5a656KaB5Y6f5Yib44CB5pyJ5Lu35YC844CB57uT5p6E5riF5pmwXHJcbjIuIOagh+mimOimgeWQuOW8leS6uuS4lOWMheWQq+WFs+mUruivjVxyXG4zLiDlhoXlrrnopoHljIXlkKvpgILlvZPnmoTmoIfpopjlsYLnuqfvvIhIMSwgSDIsIEgz562J77yJXHJcbjQuIOmAguW9k+S9v+eUqG1hcmtkb3du5qC85byPXHJcbjUuIOWGheWuuemVv+W6pumAguS4re+8iDEwMDAtMjAwMOWtl++8iVxyXG42LiDor63oqIDvvJoke2xhbmd1YWdlID09PSAnemgnID8gJ+S4reaWhycgOiBsYW5ndWFnZSA9PT0gJ2VuJyA/ICdFbmdsaXNoJyA6IGxhbmd1YWdlfVxyXG5cclxuJHtzZXJpZXNDb250ZXh0ID8gYOezu+WIl+S4iuS4i+aWh++8mlxcbiR7c2VyaWVzQ29udGV4dH1cXG7or7fnoa7kv53mnKzmlofkuI7ns7vliJflhbbku5bmlofnq6Dkv53mjIHov57otK/mgKfjgIJcXG5gIDogJyd9XHJcblxyXG7or7fnm7TmjqXov5Tlm57ljZrmloflhoXlrrnvvIzkuI3opoHljIXlkKvpop3lpJbnmoTor7TmmI7jgIJgO1xyXG5cclxuICAgIGNvbnN0IHVzZXJQcm9tcHQgPSBgJHtwcm9tcHR9XHJcblxyXG7lhbPplK7or43vvJoke2tleXdvcmRzfVxyXG4ke3RpdGxlID8gYOW7uuiuruagh+mimO+8miR7dGl0bGV9YCA6ICcnfVxyXG5cclxu6K+355Sf5oiQ5LiA56+H5YWz5LqOXCIke2tleXdvcmRzfVwi55qE5Y2a5paH44CCYDtcclxuXHJcbiAgICAvLyDosIPnlKhRd2VuIEFQSSAo5YW85a655qih5byPKVxyXG4gICAgY29uc3QgcmVxdWVzdEJvZHkgPSBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgIG1vZGVsOiAncXdlbi10dXJibycsXHJcbiAgICAgIG1lc3NhZ2VzOiBbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgcm9sZTogJ3N5c3RlbScsXHJcbiAgICAgICAgICBjb250ZW50OiBzeXN0ZW1Qcm9tcHRcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHJvbGU6ICd1c2VyJyxcclxuICAgICAgICAgIGNvbnRlbnQ6IHVzZXJQcm9tcHRcclxuICAgICAgICB9XHJcbiAgICAgIF0sXHJcbiAgICAgIHRlbXBlcmF0dXJlOiAwLjcsXHJcbiAgICAgIHRvcF9wOiAwLjgsXHJcbiAgICAgIG1heF90b2tlbnM6IDIwMDBcclxuICAgIH0pO1xyXG5cclxuICAgIGNvbnN0IHF3ZW5SZXNwb25zZSA9IGF3YWl0IGZldGNoKCdodHRwczovL2Rhc2hzY29wZS5hbGl5dW5jcy5jb20vY29tcGF0aWJsZS1tb2RlL3YxL2NoYXQvY29tcGxldGlvbnMnLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7YXBpS2V5fWAsXHJcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgfSxcclxuICAgICAgYm9keTogcmVxdWVzdEJvZHlcclxuICAgIH0pO1xyXG5cclxuICAgIGlmICghcXdlblJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHF3ZW5SZXNwb25zZS50ZXh0KCk7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1F3ZW4gQVBJ6ZSZ6K+vOicsIGVycm9yVGV4dCk7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihgUXdlbiBBUEnosIPnlKjlpLHotKU6ICR7cXdlblJlc3BvbnNlLnN0YXR1c30gJHtlcnJvclRleHR9YCk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgcXdlbkRhdGE6IFF3ZW5SZXNwb25zZSA9IGF3YWl0IHF3ZW5SZXNwb25zZS5qc29uKCk7XHJcbiAgICBcclxuICAgIGlmICghcXdlbkRhdGEuY2hvaWNlcyB8fCBxd2VuRGF0YS5jaG9pY2VzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1F3ZW4gQVBJ6L+U5Zue5pWw5o2u5qC85byP6ZSZ6K+vJyk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZ2VuZXJhdGVkQ29udGVudCA9IHF3ZW5EYXRhLmNob2ljZXNbMF0ubWVzc2FnZS5jb250ZW50O1xyXG5cclxuICAgIC8vIOS7jueUn+aIkOeahOWGheWuueS4reaPkOWPluagh+mimOWSjOato+aWh1xyXG4gICAgY29uc3QgeyBleHRyYWN0ZWRUaXRsZSwgY29udGVudCB9ID0gZXh0cmFjdFRpdGxlQW5kQ29udGVudChnZW5lcmF0ZWRDb250ZW50LCBrZXl3b3JkcywgdGl0bGUpO1xyXG5cclxuICAgIC8vIOeUn+aIkFNFT+S/oeaBr1xyXG4gICAgY29uc3Qgc2VvVGl0bGUgPSBnZW5lcmF0ZVNFT1RpdGxlKGV4dHJhY3RlZFRpdGxlLCBrZXl3b3Jkcyk7XHJcbiAgICBjb25zdCBzZW9EZXNjcmlwdGlvbiA9IGdlbmVyYXRlU0VPRGVzY3JpcHRpb24oY29udGVudCwga2V5d29yZHMpO1xyXG5cclxuICAgIGNvbnN0IHJlc3BvbnNlID0ge1xyXG4gICAgICB0aXRsZTogZXh0cmFjdGVkVGl0bGUsXHJcbiAgICAgIGNvbnRlbnQ6IGNvbnRlbnQsXHJcbiAgICAgIHNlb1RpdGxlOiBzZW9UaXRsZSxcclxuICAgICAgc2VvRGVzY3JpcHRpb246IHNlb0Rlc2NyaXB0aW9uLFxyXG4gICAgICB1c2FnZTogcXdlbkRhdGEudXNhZ2UsXHJcbiAgICAgIG1vZGVsOiAncXdlbi10dXJibydcclxuICAgIH07XHJcblxyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHJlc3BvbnNlKTtcclxuXHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ+WNmuaWh+eUn+aIkOmUmeivrzonLCBlcnJvcik7XHJcblxyXG4gICAgLy8g5qOA5p+l5piv5ZCm5piv57yW56CB55u45YWz6ZSZ6K+vXHJcbiAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvciAmJiAoXHJcbiAgICAgIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ0J5dGVTdHJpbmcnKSB8fFxyXG4gICAgICBlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdjaGFyYWN0ZXInKSB8fFxyXG4gICAgICBlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdncmVhdGVyIHRoYW4gMjU1JylcclxuICAgICkpIHtcclxuICAgICAgY29uc29sZS5sb2coJ+ajgOa1i+WIsOe8lueggemUmeivr++8jOS9v+eUqOaooeaLn+aVsOaNricpO1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IGJvZHk6IEdlbmVyYXRlUmVxdWVzdCA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xyXG4gICAgICAgIHJldHVybiBnZW5lcmF0ZU1vY2tDb250ZW50KGJvZHkpO1xyXG4gICAgICB9IGNhdGNoIHtcclxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXHJcbiAgICAgICAgICB7IGVycm9yOiAn6K+35rGC5pWw5o2u5qC85byP6ZSZ6K+vJyB9LFxyXG4gICAgICAgICAgeyBzdGF0dXM6IDQwMCB9XHJcbiAgICAgICAgKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOWmguaenEFQSeiwg+eUqOWksei0pe+8jOWbnumAgOWIsOaooeaLn+aVsOaNrlxyXG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgJiYgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnQVBJJykpIHtcclxuICAgICAgY29uc29sZS5sb2coJ0FQSeiwg+eUqOWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNricpO1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IGJvZHk6IEdlbmVyYXRlUmVxdWVzdCA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xyXG4gICAgICAgIHJldHVybiBnZW5lcmF0ZU1vY2tDb250ZW50KGJvZHkpO1xyXG4gICAgICB9IGNhdGNoIHtcclxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXHJcbiAgICAgICAgICB7IGVycm9yOiAn6K+35rGC5pWw5o2u5qC85byP6ZSZ6K+vJyB9LFxyXG4gICAgICAgICAgeyBzdGF0dXM6IDQwMCB9XHJcbiAgICAgICAgKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcclxuICAgICAgeyBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn55Sf5oiQ5Y2a5paH5aSx6LSlJyB9LFxyXG4gICAgICB7IHN0YXR1czogNTAwIH1cclxuICAgICk7XHJcbiAgfVxyXG59XHJcblxyXG4vLyDku47nlJ/miJDlhoXlrrnkuK3mj5Dlj5bmoIfpopjlkozmraPmlodcclxuZnVuY3Rpb24gZXh0cmFjdFRpdGxlQW5kQ29udGVudChnZW5lcmF0ZWRDb250ZW50OiBzdHJpbmcsIGtleXdvcmRzOiBzdHJpbmcsIHN1Z2dlc3RlZFRpdGxlPzogc3RyaW5nKSB7XHJcbiAgY29uc3QgbGluZXMgPSBnZW5lcmF0ZWRDb250ZW50LnRyaW0oKS5zcGxpdCgnXFxuJyk7XHJcbiAgbGV0IGV4dHJhY3RlZFRpdGxlID0gc3VnZ2VzdGVkVGl0bGUgfHwgYOWFs+S6jiR7a2V5d29yZHN955qE5a6M5pW05oyH5Y2XYDtcclxuICBsZXQgY29udGVudCA9IGdlbmVyYXRlZENvbnRlbnQ7XHJcblxyXG4gIC8vIOWwneivleS7juesrOS4gOihjOaPkOWPluagh+mimFxyXG4gIGlmIChsaW5lc1swXSAmJiAobGluZXNbMF0uc3RhcnRzV2l0aCgnIycpIHx8IGxpbmVzWzBdLmluY2x1ZGVzKGtleXdvcmRzKSkpIHtcclxuICAgIGV4dHJhY3RlZFRpdGxlID0gbGluZXNbMF0ucmVwbGFjZSgvXiMrXFxzKi8sICcnKS50cmltKCk7XHJcbiAgICBjb250ZW50ID0gbGluZXMuc2xpY2UoMSkuam9pbignXFxuJykudHJpbSgpO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHsgZXh0cmFjdGVkVGl0bGUsIGNvbnRlbnQgfTtcclxufVxyXG5cclxuLy8g55Sf5oiQU0VP5qCH6aKYXHJcbmZ1bmN0aW9uIGdlbmVyYXRlU0VPVGl0bGUodGl0bGU6IHN0cmluZywga2V5d29yZHM6IHN0cmluZyk6IHN0cmluZyB7XHJcbiAgY29uc3QgeWVhciA9IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKTtcclxuICBpZiAodGl0bGUuaW5jbHVkZXMoeWVhci50b1N0cmluZygpKSkge1xyXG4gICAgcmV0dXJuIHRpdGxlO1xyXG4gIH1cclxuICByZXR1cm4gYCR7dGl0bGV9IC0g5LiT5Lia5oyH5Y2XIHwgJHt5ZWFyfWA7XHJcbn1cclxuXHJcbi8vIOeUn+aIkFNFT+aPj+i/sFxyXG5mdW5jdGlvbiBnZW5lcmF0ZVNFT0Rlc2NyaXB0aW9uKGNvbnRlbnQ6IHN0cmluZywga2V5d29yZHM6IHN0cmluZyk6IHN0cmluZyB7XHJcbiAgLy8g5LuO5YaF5a655Lit5o+Q5Y+W5YmNMjAw5Liq5a2X56ym5L2c5Li65o+P6L+wXHJcbiAgY29uc3QgcGxhaW5UZXh0ID0gY29udGVudFxyXG4gICAgLnJlcGxhY2UoLyMrXFxzL2csICcnKSAvLyDnp7vpmaRtYXJrZG93buagh+mimOagh+iusFxyXG4gICAgLnJlcGxhY2UoL1xcKlxcKi9nLCAnJykgLy8g56e76Zmk5Yqg57KX5qCH6K6wXHJcbiAgICAucmVwbGFjZSgvXFxuKy9nLCAnICcpIC8vIOabv+aNouaNouihjOS4uuepuuagvFxyXG4gICAgLnRyaW0oKTtcclxuICBcclxuICBjb25zdCBkZXNjcmlwdGlvbiA9IHBsYWluVGV4dC5sZW5ndGggPiAxNjAgXHJcbiAgICA/IHBsYWluVGV4dC5zdWJzdHJpbmcoMCwgMTU3KSArICcuLi4nXHJcbiAgICA6IHBsYWluVGV4dDtcclxuICAgIFxyXG4gIHJldHVybiBkZXNjcmlwdGlvbiB8fCBg6K+m57uG5LqG6KejJHtrZXl3b3Jkc33nmoTmpoLlv7XjgIHlupTnlKjlkozmnIDkvbPlrp7ot7XjgILkuJPkuJrnmoTmioDmnK/mjIfljZfvvIzluK7liqnmgqjlv6vpgJ/mjozmj6Hnm7jlhbPnn6Xor4bjgIJgO1xyXG59XHJcblxyXG4vLyDnlJ/miJDmqKHmi5/lhoXlrrnvvIjlpIfnlKjmlrnmoYjvvIlcclxuYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVNb2NrQ29udGVudChib2R5OiBHZW5lcmF0ZVJlcXVlc3QpIHtcclxuICBjb25zdCB7IGtleXdvcmRzLCB0aXRsZSwgc2VyaWVzQ29udGV4dCB9ID0gYm9keTtcclxuICBcclxuICAvLyDmqKHmi5/nvZHnu5zlu7bov59cclxuICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpO1xyXG5cclxuICBjb25zdCBnZW5lcmF0ZWRUaXRsZSA9IHRpdGxlIHx8IGDlhbPkuo4ke2tleXdvcmRzfeeahOWujOaVtOaMh+WNl2A7XHJcbiAgY29uc3QgY29udGVudCA9IGAjICR7Z2VuZXJhdGVkVGl0bGV9XHJcblxyXG4jIyDnroDku4tcclxuXHJcbiR7c2VyaWVzQ29udGV4dCA/ICfln7rkuo7mnKzns7vliJfliY3pnaLnmoTlhoXlrrnvvIwnIDogJyd95pys5paH5bCG5rex5YWl5o6i6K6oJHtrZXl3b3Jkc33nm7jlhbPnmoTph43opoHmpoLlv7Xlkozlrp7ot7Xmlrnms5XjgIJcclxuXHJcbiMjIOS4u+imgeWGheWuuVxyXG5cclxuIyMjIDEuIOWfuuehgOamguW/tVxyXG7lnKjkuobop6Mke2tleXdvcmRzfeS5i+WJje+8jOaIkeS7rOmcgOimgeaOjOaPoeS4gOS6m+WfuuehgOamguW/teOAgiR7a2V5d29yZHN95L2c5Li65b2T5YmN5oqA5pyv6aKG5Z+f55qE6YeN6KaB57uE5oiQ6YOo5YiG77yM5YW35pyJ5Lul5LiL54m554K577yaXHJcblxyXG4tIOmrmOaViOaAp++8muiDveWkn+aYvuiRl+aPkOWNh+W3peS9nOaViOeOh1xyXG4tIOWPr+aJqeWxleaAp++8muaUr+aMgeeBtea0u+eahOaJqeWxleWSjOWumuWItlxyXG4tIOaYk+eUqOaAp++8muaPkOS+m+WPi+WlveeahOeUqOaIt+S9k+mqjFxyXG5cclxuIyMjIDIuIOWunui3teW6lOeUqFxyXG4ke2tleXdvcmRzfeWcqOWunumZheW6lOeUqOS4reacieedgOW5v+azm+eahOeUqOmAlO+8mlxyXG5cclxuKirlupTnlKjlnLrmma/kuIDvvJoqKlxyXG7lnKjkvIHkuJrnuqflupTnlKjkuK3vvIwke2tleXdvcmRzfeWPr+S7peW4ruWKqeWboumYn+abtOWlveWcsOWNj+S9nOWSjOeuoeeQhumhueebruOAglxyXG5cclxuKirlupTnlKjlnLrmma/kuozvvJoqKlxyXG7lr7nkuo7kuKrkurrnlKjmiLfvvIwke2tleXdvcmRzfeaPkOS+m+S6huS+v+aNt+eahOino+WGs+aWueahiOadpeWkhOeQhuaXpeW4uOS7u+WKoeOAglxyXG5cclxuIyMjIDMuIOacgOS9s+Wunui3tVxyXG7ku6XkuIvmmK/kvb/nlKgke2tleXdvcmRzfeeahOS4gOS6m+acgOS9s+Wunui3te+8mlxyXG5cclxuMS4gKirop4TliJLlhYjooYwqKu+8muWcqOW8gOWni+S5i+WJje+8jOWItuWumuivpue7hueahOWunuaWveiuoeWIklxyXG4yLiAqKumAkOatpeaOqOi/myoq77ya6YeH55So6L+t5Luj55qE5pa55byP77yM6YCQ5q2l5a6M5ZaE5Yqf6IO9XHJcbjMuICoq5oyB57ut5LyY5YyWKirvvJrmoLnmja7kvb/nlKjlj43ppojvvIzkuI3mlq3mlLnov5vlkozkvJjljJZcclxuNC4gKirlm6LpmJ/ljY/kvZwqKu+8muehruS/neWboumYn+aIkOWRmOmDveiDveeGn+e7g+S9v+eUqOebuOWFs+W3peWFt1xyXG41LiAqKuaWh+aho+iusOW9lSoq77ya57u05oqk5a6M5ZaE55qE5paH5qGj77yM5L6/5LqO5ZCO57ut57u05oqkXHJcblxyXG4jIyMgNC4g5rOo5oSP5LqL6aG5XHJcbuWcqOS9v+eUqCR7a2V5d29yZHN96L+H56iL5Lit77yM6ZyA6KaB5rOo5oSP5Lul5LiL5Yeg54K577yaXHJcblxyXG4tIOaVsOaNruWuieWFqO+8muehruS/neaVj+aEn+S/oeaBr+W+l+WIsOWmpeWWhOS/neaKpFxyXG4tIOaAp+iDveS8mOWMlu+8muWumuacn+ajgOafpeWSjOS8mOWMluezu+e7n+aAp+iDvVxyXG4tIOWkh+S7veetlueVpe+8muW7uueri+WujOWWhOeahOaVsOaNruWkh+S7veacuuWItlxyXG5cclxuIyMg5a6e5pa95bu66K6uXHJcblxyXG7kuLrkuobmm7Tlpb3lnLDlupTnlKgke2tleXdvcmRzfe+8jOW7uuiuruaMieeFp+S7peS4i+atpemqpOi/m+ihjO+8mlxyXG5cclxuMS4gKirpnIDmsYLliIbmnpAqKu+8muaYjuehruWFt+S9k+eahOS4muWKoemcgOaxguWSjOebruagh1xyXG4yLiAqKuaKgOacr+mAieWeiyoq77ya6YCJ5oup5ZCI6YCC55qE5oqA5pyv5qCI5ZKM5bel5YW3XHJcbjMuICoq5Y6f5Z6L5byA5Y+RKirvvJrlv6vpgJ/mnoTlu7rljp/lnovpqozor4Hlj6/ooYzmgKdcclxuNC4gKirmraPlvI/lvIDlj5EqKu+8muWfuuS6juWOn+Wei+i/m+ihjOato+W8j+eahOW8gOWPkeW3peS9nFxyXG41LiAqKua1i+ivlemDqOe9sioq77ya5YWF5YiG5rWL6K+V5ZCO6L+b6KGM55Sf5Lqn546v5aKD6YOo572yXHJcbjYuICoq6L+Q57u055uR5o6nKirvvJrlu7rnq4vlrozlloTnmoTov5Dnu7Tlkoznm5HmjqfkvZPns7tcclxuXHJcbiMjIOaAu+e7k1xyXG5cclxu6YCa6L+H5pys5paH55qE5LuL57uN77yM5oiR5Lus5rex5YWl5LqG6Kej5LqGJHtrZXl3b3Jkc33nmoTmoLjlv4PmpoLlv7XjgIHlrp7ot7XlupTnlKjlkozmnIDkvbPlrp7ot7XjgIIke2tleXdvcmRzfeS9nOS4uuS4gOS4quW8uuWkp+eahOW3peWFt++8jOiDveWkn+S4uuaIkeS7rOeahOW3peS9nOWSjOeUn+a0u+W4puadpeaYvuiRl+eahOS+v+WIqeOAglxyXG5cclxu5Zyo5a6e6ZmF5bqU55So5Lit77yM5bu66K6u77yaXHJcbi0g5qC55o2u6Ieq6Lqr6ZyA5rGC6YCJ5oup5ZCI6YCC55qE5pa55qGIXHJcbi0g6YeN6KeG5Zui6Zif5Z+56K6t5ZKM55+l6K+G5YiG5LqrXHJcbi0g5L+d5oyB5oqA5pyv5pu05paw5ZKM5oyB57ut5a2m5LmgXHJcblxyXG4ke3Nlcmllc0NvbnRleHQgPyAnXFxu5Zyo5LiL5LiA56+H5paH56ug5Lit77yM5oiR5Lus5bCG57un57ut5o6i6K6oJHtrZXl3b3Jkc33nmoTpq5jnuqflupTnlKjlkozmnKrmnaXlj5HlsZXotovlir/jgIInIDogJyd9XHJcblxyXG7luIzmnJvov5nkupvlhoXlrrnlr7nmgqjmnInmiYDluK7liqnjgILlpoLmnpzmgqjmnInku7vkvZXpl67popjmiJblu7rorq7vvIzmrKLov47pmo/ml7bkuqTmtYHorqjorrrjgIJgO1xyXG5cclxuICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xyXG4gICAgdGl0bGU6IGdlbmVyYXRlZFRpdGxlLFxyXG4gICAgY29udGVudDogY29udGVudCxcclxuICAgIHNlb1RpdGxlOiBgJHtnZW5lcmF0ZWRUaXRsZX0gLSDkuJPkuJrmjIfljZcgfCAke25ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKX1gLFxyXG4gICAgc2VvRGVzY3JpcHRpb246IGDor6bnu4bkuobop6Mke2tleXdvcmRzfeeahOamguW/teOAgeW6lOeUqOWSjOacgOS9s+Wunui3teOAguS4k+S4mueahOaKgOacr+aMh+WNl++8jOW4ruWKqeaCqOW/q+mAn+aOjOaPoeebuOWFs+efpeivhuOAgmAsXHJcbiAgICB1c2FnZToge1xyXG4gICAgICBwcm9tcHRfdG9rZW5zOiAxMDAsXHJcbiAgICAgIGNvbXBsZXRpb25fdG9rZW5zOiA4MDAsXHJcbiAgICAgIHRvdGFsX3Rva2VuczogOTAwXHJcbiAgICB9LFxyXG4gICAgbW9kZWw6ICdtb2NrLWdlbmVyYXRvcidcclxuICB9KTtcclxufSJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJQT1NUIiwicmVxdWVzdCIsImJvZHkiLCJqc29uIiwia2V5d29yZHMiLCJ0aXRsZSIsInByb21wdCIsImxhbmd1YWdlIiwic2VyaWVzQ29udGV4dCIsImVycm9yIiwic3RhdHVzIiwiYXBpS2V5IiwicHJvY2VzcyIsImVudiIsIlFXRU5fQVBJX0tFWSIsImNvbnNvbGUiLCJ3YXJuIiwiZ2VuZXJhdGVNb2NrQ29udGVudCIsInN5c3RlbVByb21wdCIsInVzZXJQcm9tcHQiLCJyZXF1ZXN0Qm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJtb2RlbCIsIm1lc3NhZ2VzIiwicm9sZSIsImNvbnRlbnQiLCJ0ZW1wZXJhdHVyZSIsInRvcF9wIiwibWF4X3Rva2VucyIsInF3ZW5SZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsIm9rIiwiZXJyb3JUZXh0IiwidGV4dCIsIkVycm9yIiwicXdlbkRhdGEiLCJjaG9pY2VzIiwibGVuZ3RoIiwiZ2VuZXJhdGVkQ29udGVudCIsIm1lc3NhZ2UiLCJleHRyYWN0ZWRUaXRsZSIsImV4dHJhY3RUaXRsZUFuZENvbnRlbnQiLCJzZW9UaXRsZSIsImdlbmVyYXRlU0VPVGl0bGUiLCJzZW9EZXNjcmlwdGlvbiIsImdlbmVyYXRlU0VPRGVzY3JpcHRpb24iLCJyZXNwb25zZSIsInVzYWdlIiwiaW5jbHVkZXMiLCJsb2ciLCJzdWdnZXN0ZWRUaXRsZSIsImxpbmVzIiwidHJpbSIsInNwbGl0Iiwic3RhcnRzV2l0aCIsInJlcGxhY2UiLCJzbGljZSIsImpvaW4iLCJ5ZWFyIiwiRGF0ZSIsImdldEZ1bGxZZWFyIiwidG9TdHJpbmciLCJwbGFpblRleHQiLCJkZXNjcmlwdGlvbiIsInN1YnN0cmluZyIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImdlbmVyYXRlZFRpdGxlIiwicHJvbXB0X3Rva2VucyIsImNvbXBsZXRpb25fdG9rZW5zIiwidG90YWxfdG9rZW5zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/generate-blog/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-blog%2Froute&page=%2Fapi%2Fgenerate-blog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-blog%2Froute.ts&appDir=E%3A%5Cauto-blog%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cauto-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();