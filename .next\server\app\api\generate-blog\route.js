"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-blog/route";
exports.ids = ["app/api/generate-blog/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-blog%2Froute&page=%2Fapi%2Fgenerate-blog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-blog%2Froute.ts&appDir=E%3A%5Cauto-blog%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cauto-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-blog%2Froute&page=%2Fapi%2Fgenerate-blog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-blog%2Froute.ts&appDir=E%3A%5Cauto-blog%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cauto-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_auto_blog_app_api_generate_blog_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/generate-blog/route.ts */ \"(rsc)/./app/api/generate-blog/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-blog/route\",\n        pathname: \"/api/generate-blog\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-blog/route\"\n    },\n    resolvedPagePath: \"E:\\\\auto-blog\\\\app\\\\api\\\\generate-blog\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_auto_blog_app_api_generate_blog_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/generate-blog/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-blog%2Froute&page=%2Fapi%2Fgenerate-blog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-blog%2Froute.ts&appDir=E%3A%5Cauto-blog%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cauto-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/generate-blog/route.ts":
/*!****************************************!*\
  !*** ./app/api/generate-blog/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { keywords, title, prompt, language, seriesContext } = body;\n        // 验证必需参数\n        if (!keywords || !prompt) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"缺少必需参数：keywords 和 prompt\"\n            }, {\n                status: 400\n            });\n        }\n        // 检查API密钥\n        const apiKey = process.env.QWEN_API_KEY;\n        if (!apiKey) {\n            console.warn(\"未配置QWEN_API_KEY，使用模拟数据\");\n            return generateMockContent(body);\n        }\n        // 构建发送给Qwen的提示词\n        const systemPrompt = `你是一个专业的博客写手，擅长创作高质量、SEO优化的博文内容。请根据用户提供的信息生成一篇完整的博文。\r\n\r\n要求：\r\n1. 内容要原创、有价值、结构清晰\r\n2. 标题要吸引人且包含关键词\r\n3. 内容要包含适当的标题层级（H1, H2, H3等）\r\n4. 适当使用markdown格式\r\n5. 内容长度适中（1000-2000字）\r\n6. 语言：${language === \"zh\" ? \"中文\" : language === \"en\" ? \"English\" : language}\r\n\r\n${seriesContext ? `系列上下文：\\n${seriesContext}\\n请确保本文与系列其他文章保持连贯性。\\n` : \"\"}\r\n\r\n请直接返回博文内容，不要包含额外的说明。`;\n        const userPrompt = `${prompt}\r\n\r\n关键词：${keywords}\r\n${title ? `建议标题：${title}` : \"\"}\r\n\r\n请生成一篇关于\"${keywords}\"的博文。`;\n        // 调用Qwen API\n        const qwenResponse = await fetch(\"https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation\", {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": `Bearer ${apiKey}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                model: \"qwen-turbo\",\n                input: {\n                    messages: [\n                        {\n                            role: \"system\",\n                            content: systemPrompt\n                        },\n                        {\n                            role: \"user\",\n                            content: userPrompt\n                        }\n                    ]\n                },\n                parameters: {\n                    temperature: 0.7,\n                    top_p: 0.8,\n                    max_tokens: 2000,\n                    result_format: \"message\"\n                }\n            })\n        });\n        if (!qwenResponse.ok) {\n            const errorText = await qwenResponse.text();\n            console.error(\"Qwen API错误:\", errorText);\n            throw new Error(`Qwen API调用失败: ${qwenResponse.status} ${errorText}`);\n        }\n        const qwenData = await qwenResponse.json();\n        if (!qwenData.choices || qwenData.choices.length === 0) {\n            throw new Error(\"Qwen API返回数据格式错误\");\n        }\n        const generatedContent = qwenData.choices[0].message.content;\n        // 从生成的内容中提取标题和正文\n        const { extractedTitle, content } = extractTitleAndContent(generatedContent, keywords, title);\n        // 生成SEO信息\n        const seoTitle = generateSEOTitle(extractedTitle, keywords);\n        const seoDescription = generateSEODescription(content, keywords);\n        const response = {\n            title: extractedTitle,\n            content: content,\n            seoTitle: seoTitle,\n            seoDescription: seoDescription,\n            usage: qwenData.usage,\n            model: \"qwen-turbo\"\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response);\n    } catch (error) {\n        console.error(\"博文生成错误:\", error);\n        // 如果API调用失败，回退到模拟数据\n        if (error instanceof Error && error.message.includes(\"API\")) {\n            console.log(\"API调用失败，使用模拟数据\");\n            return generateMockContent(await request.json());\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error instanceof Error ? error.message : \"生成博文失败\"\n        }, {\n            status: 500\n        });\n    }\n}\n// 从生成内容中提取标题和正文\nfunction extractTitleAndContent(generatedContent, keywords, suggestedTitle) {\n    const lines = generatedContent.trim().split(\"\\n\");\n    let extractedTitle = suggestedTitle || `关于${keywords}的完整指南`;\n    let content = generatedContent;\n    // 尝试从第一行提取标题\n    if (lines[0] && (lines[0].startsWith(\"#\") || lines[0].includes(keywords))) {\n        extractedTitle = lines[0].replace(/^#+\\s*/, \"\").trim();\n        content = lines.slice(1).join(\"\\n\").trim();\n    }\n    return {\n        extractedTitle,\n        content\n    };\n}\n// 生成SEO标题\nfunction generateSEOTitle(title, keywords) {\n    const year = new Date().getFullYear();\n    if (title.includes(year.toString())) {\n        return title;\n    }\n    return `${title} - 专业指南 | ${year}`;\n}\n// 生成SEO描述\nfunction generateSEODescription(content, keywords) {\n    // 从内容中提取前200个字符作为描述\n    const plainText = content.replace(/#+\\s/g, \"\") // 移除markdown标题标记\n    .replace(/\\*\\*/g, \"\") // 移除加粗标记\n    .replace(/\\n+/g, \" \") // 替换换行为空格\n    .trim();\n    const description = plainText.length > 160 ? plainText.substring(0, 157) + \"...\" : plainText;\n    return description || `详细了解${keywords}的概念、应用和最佳实践。专业的技术指南，帮助您快速掌握相关知识。`;\n}\n// 生成模拟内容（备用方案）\nasync function generateMockContent(body) {\n    const { keywords, title, seriesContext } = body;\n    // 模拟网络延迟\n    await new Promise((resolve)=>setTimeout(resolve, 1000));\n    const generatedTitle = title || `关于${keywords}的完整指南`;\n    const content = `# ${generatedTitle}\r\n\r\n## 简介\r\n\r\n${seriesContext ? \"基于本系列前面的内容，\" : \"\"}本文将深入探讨${keywords}相关的重要概念和实践方法。\r\n\r\n## 主要内容\r\n\r\n### 1. 基础概念\r\n在了解${keywords}之前，我们需要掌握一些基础概念。${keywords}作为当前技术领域的重要组成部分，具有以下特点：\r\n\r\n- 高效性：能够显著提升工作效率\r\n- 可扩展性：支持灵活的扩展和定制\r\n- 易用性：提供友好的用户体验\r\n\r\n### 2. 实践应用\r\n${keywords}在实际应用中有着广泛的用途：\r\n\r\n**应用场景一：**\r\n在企业级应用中，${keywords}可以帮助团队更好地协作和管理项目。\r\n\r\n**应用场景二：**\r\n对于个人用户，${keywords}提供了便捷的解决方案来处理日常任务。\r\n\r\n### 3. 最佳实践\r\n以下是使用${keywords}的一些最佳实践：\r\n\r\n1. **规划先行**：在开始之前，制定详细的实施计划\r\n2. **逐步推进**：采用迭代的方式，逐步完善功能\r\n3. **持续优化**：根据使用反馈，不断改进和优化\r\n4. **团队协作**：确保团队成员都能熟练使用相关工具\r\n5. **文档记录**：维护完善的文档，便于后续维护\r\n\r\n### 4. 注意事项\r\n在使用${keywords}过程中，需要注意以下几点：\r\n\r\n- 数据安全：确保敏感信息得到妥善保护\r\n- 性能优化：定期检查和优化系统性能\r\n- 备份策略：建立完善的数据备份机制\r\n\r\n## 实施建议\r\n\r\n为了更好地应用${keywords}，建议按照以下步骤进行：\r\n\r\n1. **需求分析**：明确具体的业务需求和目标\r\n2. **技术选型**：选择合适的技术栈和工具\r\n3. **原型开发**：快速构建原型验证可行性\r\n4. **正式开发**：基于原型进行正式的开发工作\r\n5. **测试部署**：充分测试后进行生产环境部署\r\n6. **运维监控**：建立完善的运维和监控体系\r\n\r\n## 总结\r\n\r\n通过本文的介绍，我们深入了解了${keywords}的核心概念、实践应用和最佳实践。${keywords}作为一个强大的工具，能够为我们的工作和生活带来显著的便利。\r\n\r\n在实际应用中，建议：\r\n- 根据自身需求选择合适的方案\r\n- 重视团队培训和知识分享\r\n- 保持技术更新和持续学习\r\n\r\n${seriesContext ? \"\\n在下一篇文章中，我们将继续探讨${keywords}的高级应用和未来发展趋势。\" : \"\"}\r\n\r\n希望这些内容对您有所帮助。如果您有任何问题或建议，欢迎随时交流讨论。`;\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        title: generatedTitle,\n        content: content,\n        seoTitle: `${generatedTitle} - 专业指南 | ${new Date().getFullYear()}`,\n        seoDescription: `详细了解${keywords}的概念、应用和最佳实践。专业的技术指南，帮助您快速掌握相关知识。`,\n        usage: {\n            prompt_tokens: 100,\n            completion_tokens: 800,\n            total_tokens: 900\n        },\n        model: \"mock-generator\"\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/generate-blog/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-blog%2Froute&page=%2Fapi%2Fgenerate-blog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-blog%2Froute.ts&appDir=E%3A%5Cauto-blog%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cauto-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();