"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-footnote";
exports.ids = ["vendor-chunks/micromark-extension-gfm-footnote"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/html.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-footnote/dev/lib/html.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultBackLabel: () => (/* binding */ defaultBackLabel),\n/* harmony export */   gfmFootnoteHtml: () => (/* binding */ gfmFootnoteHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @import {HtmlOptions as Options} from 'micromark-extension-gfm-footnote'\n * @import {HtmlExtension} from 'micromark-util-types'\n */ \n\n\nconst own = {}.hasOwnProperty;\n/** @type {Options} */ const emptyOptions = {};\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Default label.\n */ function defaultBackLabel(referenceIndex, rereferenceIndex) {\n    return \"Back to reference \" + (referenceIndex + 1) + (rereferenceIndex > 1 ? \"-\" + rereferenceIndex : \"\");\n}\n/**\n * Create an extension for `micromark` to support GFM footnotes when\n * serializing to HTML.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (optional).\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM footnotes when serializing to HTML.\n */ function gfmFootnoteHtml(options) {\n    const config = options || emptyOptions;\n    const label = config.label || \"Footnotes\";\n    const labelTagName = config.labelTagName || \"h2\";\n    const labelAttributes = config.labelAttributes === null || config.labelAttributes === undefined ? 'class=\"sr-only\"' : config.labelAttributes;\n    const backLabel = config.backLabel || defaultBackLabel;\n    const clobberPrefix = config.clobberPrefix === null || config.clobberPrefix === undefined ? \"user-content-\" : config.clobberPrefix;\n    return {\n        enter: {\n            gfmFootnoteDefinition () {\n                const stack = this.getData(\"tightStack\");\n                stack.push(false);\n            },\n            gfmFootnoteDefinitionLabelString () {\n                this.buffer();\n            },\n            gfmFootnoteCallString () {\n                this.buffer();\n            }\n        },\n        exit: {\n            gfmFootnoteDefinition () {\n                let definitions = this.getData(\"gfmFootnoteDefinitions\");\n                const footnoteStack = this.getData(\"gfmFootnoteDefinitionStack\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(footnoteStack, \"expected `footnoteStack`\");\n                const tightStack = this.getData(\"tightStack\");\n                const current = footnoteStack.pop();\n                const value = this.resume();\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(current, \"expected to be in a footnote\");\n                if (!definitions) {\n                    this.setData(\"gfmFootnoteDefinitions\", definitions = {});\n                }\n                if (!own.call(definitions, current)) definitions[current] = value;\n                tightStack.pop();\n                this.setData(\"slurpOneLineEnding\", true);\n                // “Hack” to prevent a line ending from showing up if we’re in a definition in\n                // an empty list item.\n                this.setData(\"lastWasTag\");\n            },\n            gfmFootnoteDefinitionLabelString (token) {\n                let footnoteStack = this.getData(\"gfmFootnoteDefinitionStack\");\n                if (!footnoteStack) {\n                    this.setData(\"gfmFootnoteDefinitionStack\", footnoteStack = []);\n                }\n                footnoteStack.push((0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token)));\n                this.resume() // Drop the label.\n                ;\n                this.buffer() // Get ready for a value.\n                ;\n            },\n            gfmFootnoteCallString (token) {\n                let calls = this.getData(\"gfmFootnoteCallOrder\");\n                let counts = this.getData(\"gfmFootnoteCallCounts\");\n                const id = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token));\n                /** @type {number} */ let counter;\n                this.resume();\n                if (!calls) this.setData(\"gfmFootnoteCallOrder\", calls = []);\n                if (!counts) this.setData(\"gfmFootnoteCallCounts\", counts = {});\n                const index = calls.indexOf(id);\n                const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__.sanitizeUri)(id.toLowerCase());\n                if (index === -1) {\n                    calls.push(id);\n                    counts[id] = 1;\n                    counter = calls.length;\n                } else {\n                    counts[id]++;\n                    counter = index + 1;\n                }\n                const reuseCounter = counts[id];\n                this.tag('<sup><a href=\"#' + clobberPrefix + \"fn-\" + safeId + '\" id=\"' + clobberPrefix + \"fnref-\" + safeId + (reuseCounter > 1 ? \"-\" + reuseCounter : \"\") + '\" data-footnote-ref=\"\" aria-describedby=\"footnote-label\">' + String(counter) + \"</a></sup>\");\n            },\n            null () {\n                const calls = this.getData(\"gfmFootnoteCallOrder\") || [];\n                const counts = this.getData(\"gfmFootnoteCallCounts\") || {};\n                const definitions = this.getData(\"gfmFootnoteDefinitions\") || {};\n                let index = -1;\n                if (calls.length > 0) {\n                    this.lineEndingIfNeeded();\n                    this.tag('<section data-footnotes=\"\" class=\"footnotes\"><' + labelTagName + ' id=\"footnote-label\"' + (labelAttributes ? \" \" + labelAttributes : \"\") + \">\");\n                    this.raw(this.encode(label));\n                    this.tag(\"</\" + labelTagName + \">\");\n                    this.lineEndingIfNeeded();\n                    this.tag(\"<ol>\");\n                }\n                while(++index < calls.length){\n                    // Called definitions are always defined.\n                    const id = calls[index];\n                    const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__.sanitizeUri)(id.toLowerCase());\n                    let referenceIndex = 0;\n                    /** @type {Array<string>} */ const references = [];\n                    while(++referenceIndex <= counts[id]){\n                        references.push('<a href=\"#' + clobberPrefix + \"fnref-\" + safeId + (referenceIndex > 1 ? \"-\" + referenceIndex : \"\") + '\" data-footnote-backref=\"\" aria-label=\"' + this.encode(typeof backLabel === \"string\" ? backLabel : backLabel(index, referenceIndex)) + '\" class=\"data-footnote-backref\">↩' + (referenceIndex > 1 ? \"<sup>\" + referenceIndex + \"</sup>\" : \"\") + \"</a>\");\n                    }\n                    const reference = references.join(\" \");\n                    let injected = false;\n                    this.lineEndingIfNeeded();\n                    this.tag('<li id=\"' + clobberPrefix + \"fn-\" + safeId + '\">');\n                    this.lineEndingIfNeeded();\n                    this.tag(definitions[id].replace(/<\\/p>(?:\\r?\\n|\\r)?$/, function($0) {\n                        injected = true;\n                        return \" \" + reference + $0;\n                    }));\n                    if (!injected) {\n                        this.lineEndingIfNeeded();\n                        this.tag(reference);\n                    }\n                    this.lineEndingIfNeeded();\n                    this.tag(\"</li>\");\n                }\n                if (calls.length > 0) {\n                    this.lineEndingIfNeeded();\n                    this.tag(\"</ol>\");\n                    this.lineEndingIfNeeded();\n                    this.tag(\"</section>\");\n                }\n            }\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js":
/*!*************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmFootnote: () => (/* binding */ gfmFootnote)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Event, Exiter, Extension, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */ \n\n\n\n\n\nconst indent = {\n    tokenize: tokenizeIndent,\n    partial: true\n};\n// To do: micromark should support a `_hiddenGfmFootnoteSupport`, which only\n// affects label start (image).\n// That will let us drop `tokenizePotentialGfmFootnote*`.\n// It currently has a `_hiddenFootnoteSupport`, which affects that and more.\n// That can be removed when `micromark-extension-footnote` is archived.\n/**\n * Create an extension for `micromark` to enable GFM footnote syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to\n *   enable GFM footnote syntax.\n */ function gfmFootnote() {\n    /** @type {Extension} */ return {\n        document: {\n            [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: {\n                name: \"gfmFootnoteDefinition\",\n                tokenize: tokenizeDefinitionStart,\n                continuation: {\n                    tokenize: tokenizeDefinitionContinuation\n                },\n                exit: gfmFootnoteDefinitionEnd\n            }\n        },\n        text: {\n            [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: {\n                name: \"gfmFootnoteCall\",\n                tokenize: tokenizeGfmFootnoteCall\n            },\n            [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket]: {\n                name: \"gfmPotentialFootnoteCall\",\n                add: \"after\",\n                tokenize: tokenizePotentialGfmFootnoteCall,\n                resolveTo: resolveToPotentialGfmFootnoteCall\n            }\n        }\n    };\n}\n// To do: remove after micromark update.\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizePotentialGfmFootnoteCall(effects, ok, nok) {\n    const self = this;\n    let index = self.events.length;\n    const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = []);\n    /** @type {Token} */ let labelStart;\n    // Find an opening.\n    while(index--){\n        const token = self.events[index][1];\n        if (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelImage) {\n            labelStart = token;\n            break;\n        }\n        // Exit if we’ve walked far enough.\n        if (token.type === \"gfmFootnoteCall\" || token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelLink || token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.label || token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.image || token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.link) {\n            break;\n        }\n    }\n    return start;\n    /**\n   * @type {State}\n   */ function start(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket, \"expected `]`\");\n        if (!labelStart || !labelStart._balanced) {\n            return nok(code);\n        }\n        const id = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(self.sliceSerialize({\n            start: labelStart.end,\n            end: self.now()\n        }));\n        if (id.codePointAt(0) !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret || !defined.includes(id.slice(1))) {\n            return nok(code);\n        }\n        effects.enter(\"gfmFootnoteCallLabelMarker\");\n        effects.consume(code);\n        effects.exit(\"gfmFootnoteCallLabelMarker\");\n        return ok(code);\n    }\n}\n// To do: remove after micromark update.\n/** @type {Resolver} */ function resolveToPotentialGfmFootnoteCall(events, context) {\n    let index = events.length;\n    /** @type {Token | undefined} */ let labelStart;\n    // Find an opening.\n    while(index--){\n        if (events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelImage && events[index][0] === \"enter\") {\n            labelStart = events[index][1];\n            break;\n        }\n    }\n    (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(labelStart, \"expected `labelStart` to resolve\");\n    // Change the `labelImageMarker` to a `data`.\n    events[index + 1][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data;\n    events[index + 3][1].type = \"gfmFootnoteCallLabelMarker\";\n    // The whole (without `!`):\n    /** @type {Token} */ const call = {\n        type: \"gfmFootnoteCall\",\n        start: Object.assign({}, events[index + 3][1].start),\n        end: Object.assign({}, events[events.length - 1][1].end)\n    };\n    // The `^` marker\n    /** @type {Token} */ const marker = {\n        type: \"gfmFootnoteCallMarker\",\n        start: Object.assign({}, events[index + 3][1].end),\n        end: Object.assign({}, events[index + 3][1].end)\n    };\n    // Increment the end 1 character.\n    marker.end.column++;\n    marker.end.offset++;\n    marker.end._bufferIndex++;\n    /** @type {Token} */ const string = {\n        type: \"gfmFootnoteCallString\",\n        start: Object.assign({}, marker.end),\n        end: Object.assign({}, events[events.length - 1][1].start)\n    };\n    /** @type {Token} */ const chunk = {\n        type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkString,\n        contentType: \"string\",\n        start: Object.assign({}, string.start),\n        end: Object.assign({}, string.end)\n    };\n    /** @type {Array<Event>} */ const replacement = [\n        // Take the `labelImageMarker` (now `data`, the `!`)\n        events[index + 1],\n        events[index + 2],\n        [\n            \"enter\",\n            call,\n            context\n        ],\n        // The `[`\n        events[index + 3],\n        events[index + 4],\n        // The `^`.\n        [\n            \"enter\",\n            marker,\n            context\n        ],\n        [\n            \"exit\",\n            marker,\n            context\n        ],\n        // Everything in between.\n        [\n            \"enter\",\n            string,\n            context\n        ],\n        [\n            \"enter\",\n            chunk,\n            context\n        ],\n        [\n            \"exit\",\n            chunk,\n            context\n        ],\n        [\n            \"exit\",\n            string,\n            context\n        ],\n        // The ending (`]`, properly parsed and labelled).\n        events[events.length - 2],\n        events[events.length - 1],\n        [\n            \"exit\",\n            call,\n            context\n        ]\n    ];\n    events.splice(index, events.length - index + 1, ...replacement);\n    return events;\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeGfmFootnoteCall(effects, ok, nok) {\n    const self = this;\n    const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = []);\n    let size = 0;\n    /** @type {boolean} */ let data;\n    // Note: the implementation of `markdown-rs` is different, because it houses\n    // core *and* extensions in one project.\n    // Therefore, it can include footnote logic inside `label-end`.\n    // We can’t do that, but luckily, we can parse footnotes in a simpler way than\n    // needed for labels.\n    return start;\n    /**\n   * Start of footnote label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function start(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, \"expected `[`\");\n        effects.enter(\"gfmFootnoteCall\");\n        effects.enter(\"gfmFootnoteCallLabelMarker\");\n        effects.consume(code);\n        effects.exit(\"gfmFootnoteCallLabelMarker\");\n        return callStart;\n    }\n    /**\n   * After `[`, at `^`.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */ function callStart(code) {\n        if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret) return nok(code);\n        effects.enter(\"gfmFootnoteCallMarker\");\n        effects.consume(code);\n        effects.exit(\"gfmFootnoteCallMarker\");\n        effects.enter(\"gfmFootnoteCallString\");\n        effects.enter(\"chunkString\").contentType = \"string\";\n        return callData;\n    }\n    /**\n   * In label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function callData(code) {\n        if (// Too long.\n        size > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.linkReferenceSizeMax || // Closing brace with nothing.\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket && !data || // Space or tab is not supported by GFM for some reason.\n        // `\\n` and `[` not being supported makes sense.\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n            return nok(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n            effects.exit(\"chunkString\");\n            const token = effects.exit(\"gfmFootnoteCallString\");\n            if (!defined.includes((0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(self.sliceSerialize(token)))) {\n                return nok(code);\n            }\n            effects.enter(\"gfmFootnoteCallLabelMarker\");\n            effects.consume(code);\n            effects.exit(\"gfmFootnoteCallLabelMarker\");\n            effects.exit(\"gfmFootnoteCall\");\n            return ok;\n        }\n        if (!(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n            data = true;\n        }\n        size++;\n        effects.consume(code);\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ? callEscape : callData;\n    }\n    /**\n   * On character after escape.\n   *\n   * ```markdown\n   * > | a [^b\\c] d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */ function callEscape(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n            effects.consume(code);\n            size++;\n            return callData;\n        }\n        return callData(code);\n    }\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeDefinitionStart(effects, ok, nok) {\n    const self = this;\n    const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = []);\n    /** @type {string} */ let identifier;\n    let size = 0;\n    /** @type {boolean | undefined} */ let data;\n    return start;\n    /**\n   * Start of GFM footnote definition.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */ function start(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, \"expected `[`\");\n        effects.enter(\"gfmFootnoteDefinition\")._container = true;\n        effects.enter(\"gfmFootnoteDefinitionLabel\");\n        effects.enter(\"gfmFootnoteDefinitionLabelMarker\");\n        effects.consume(code);\n        effects.exit(\"gfmFootnoteDefinitionLabelMarker\");\n        return labelAtMarker;\n    }\n    /**\n   * In label, at caret.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */ function labelAtMarker(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret) {\n            effects.enter(\"gfmFootnoteDefinitionMarker\");\n            effects.consume(code);\n            effects.exit(\"gfmFootnoteDefinitionMarker\");\n            effects.enter(\"gfmFootnoteDefinitionLabelString\");\n            effects.enter(\"chunkString\").contentType = \"string\";\n            return labelInside;\n        }\n        return nok(code);\n    }\n    /**\n   * In label.\n   *\n   * > 👉 **Note**: `cmark-gfm` prevents whitespace from occurring in footnote\n   * > definition labels.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function labelInside(code) {\n        if (// Too long.\n        size > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.linkReferenceSizeMax || // Closing brace with nothing.\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket && !data || // Space or tab is not supported by GFM for some reason.\n        // `\\n` and `[` not being supported makes sense.\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n            return nok(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n            effects.exit(\"chunkString\");\n            const token = effects.exit(\"gfmFootnoteDefinitionLabelString\");\n            identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(self.sliceSerialize(token));\n            effects.enter(\"gfmFootnoteDefinitionLabelMarker\");\n            effects.consume(code);\n            effects.exit(\"gfmFootnoteDefinitionLabelMarker\");\n            effects.exit(\"gfmFootnoteDefinitionLabel\");\n            return labelAfter;\n        }\n        if (!(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n            data = true;\n        }\n        size++;\n        effects.consume(code);\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ? labelEscape : labelInside;\n    }\n    /**\n   * After `\\`, at a special character.\n   *\n   * > 👉 **Note**: `cmark-gfm` currently does not support escaped brackets:\n   * > <https://github.com/github/cmark-gfm/issues/240>\n   *\n   * ```markdown\n   * > | [^a\\*b]: c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function labelEscape(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n            effects.consume(code);\n            size++;\n            return labelInside;\n        }\n        return labelInside(code);\n    }\n    /**\n   * After definition label.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function labelAfter(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) {\n            effects.enter(\"definitionMarker\");\n            effects.consume(code);\n            effects.exit(\"definitionMarker\");\n            if (!defined.includes(identifier)) {\n                defined.push(identifier);\n            }\n            // Any whitespace after the marker is eaten, forming indented code\n            // is not possible.\n            // No space is also fine, just like a block quote marker.\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, whitespaceAfter, \"gfmFootnoteDefinitionWhitespace\");\n        }\n        return nok(code);\n    }\n    /**\n   * After definition prefix.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */ function whitespaceAfter(code) {\n        // `markdown-rs` has a wrapping token for the prefix that is closed here.\n        return ok(code);\n    }\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeDefinitionContinuation(effects, ok, nok) {\n    /// Start of footnote definition continuation.\n    ///\n    /// ```markdown\n    ///   | [^a]: b\n    /// > |     c\n    ///     ^\n    /// ```\n    //\n    // Either a blank line, which is okay, or an indented thing.\n    return effects.check(micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.blankLine, ok, effects.attempt(indent, ok, nok));\n}\n/** @type {Exiter} */ function gfmFootnoteDefinitionEnd(effects) {\n    effects.exit(\"gfmFootnoteDefinition\");\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeIndent(effects, ok, nok) {\n    const self = this;\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, afterPrefix, \"gfmFootnoteDefinitionIndent\", micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize + 1);\n    /**\n   * @type {State}\n   */ function afterPrefix(code) {\n        const tail = self.events[self.events.length - 1];\n        return tail && tail[1].type === \"gfmFootnoteDefinitionIndent\" && tail[2].sliceSerialize(tail[1], true).length === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize ? ok(code) : nok(code);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js\n");

/***/ })

};
;