"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/markdown-table";
exports.ids = ["vendor-chunks/markdown-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/markdown-table/index.js":
/*!**********************************************!*\
  !*** ./node_modules/markdown-table/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   markdownTable: () => (/* binding */ markdownTable)\n/* harmony export */ });\n// To do: next major: remove.\n/**\n * @typedef {Options} MarkdownTableOptions\n *   Configuration.\n */ /**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [alignDelimiters=true]\n *   Whether to align the delimiters (default: `true`);\n *   they are aligned by default:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   Pass `false` to make them staggered:\n *\n *   ```markdown\n *   | Alpha | B |\n *   | - | - |\n *   | C | Delta |\n *   ```\n * @property {ReadonlyArray<string | null | undefined> | string | null | undefined} [align]\n *   How to align columns (default: `''`);\n *   one style for all columns or styles for their respective columns;\n *   each style is either `'l'` (left), `'r'` (right), or `'c'` (center);\n *   other values are treated as `''`, which doesn’t place the colon in the\n *   alignment row but does align left;\n *   *only the lowercased first character is used, so `Right` is fine.*\n * @property {boolean | null | undefined} [delimiterEnd=true]\n *   Whether to end each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B\n *   | ----- | -----\n *   | C     | Delta\n *   ```\n * @property {boolean | null | undefined} [delimiterStart=true]\n *   Whether to begin each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are starting delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no starting delimiters:\n *\n *   ```markdown\n *   Alpha | B     |\n *   ----- | ----- |\n *   C     | Delta |\n *   ```\n * @property {boolean | null | undefined} [padding=true]\n *   Whether to add a space of padding between delimiters and cells\n *   (default: `true`).\n *\n *   When `true`, there is padding:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there is no padding:\n *\n *   ```markdown\n *   |Alpha|B    |\n *   |-----|-----|\n *   |C    |Delta|\n *   ```\n * @property {((value: string) => number) | null | undefined} [stringLength]\n *   Function to detect the length of table cell content (optional);\n *   this is used when aligning the delimiters (`|`) between table cells;\n *   full-width characters and emoji mess up delimiter alignment when viewing\n *   the markdown source;\n *   to fix this, you can pass this function,\n *   which receives the cell content and returns its “visible” size;\n *   note that what is and isn’t visible depends on where the text is displayed.\n *\n *   Without such a function, the following:\n *\n *   ```js\n *   markdownTable([\n *     ['Alpha', 'Bravo'],\n *     ['中文', 'Charlie'],\n *     ['👩‍❤️‍👩', 'Delta']\n *   ])\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo |\n *   | - | - |\n *   | 中文 | Charlie |\n *   | 👩‍❤️‍👩 | Delta |\n *   ```\n *\n *   With [`string-width`](https://github.com/sindresorhus/string-width):\n *\n *   ```js\n *   import stringWidth from 'string-width'\n *\n *   markdownTable(\n *     [\n *       ['Alpha', 'Bravo'],\n *       ['中文', 'Charlie'],\n *       ['👩‍❤️‍👩', 'Delta']\n *     ],\n *     {stringLength: stringWidth}\n *   )\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo   |\n *   | ----- | ------- |\n *   | 中文  | Charlie |\n *   | 👩‍❤️‍👩    | Delta   |\n *   ```\n */ /**\n * @param {string} value\n *   Cell value.\n * @returns {number}\n *   Cell size.\n */ function defaultStringLength(value) {\n    return value.length;\n}\n/**\n * Generate a markdown\n * ([GFM](https://docs.github.com/en/github/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables))\n * table.\n *\n * @param {ReadonlyArray<ReadonlyArray<string | null | undefined>>} table\n *   Table data (matrix of strings).\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Result.\n */ function markdownTable(table, options) {\n    const settings = options || {};\n    // To do: next major: change to spread.\n    const align = (settings.align || []).concat();\n    const stringLength = settings.stringLength || defaultStringLength;\n    /** @type {Array<number>} Character codes as symbols for alignment per column. */ const alignments = [];\n    /** @type {Array<Array<string>>} Cells per row. */ const cellMatrix = [];\n    /** @type {Array<Array<number>>} Sizes of each cell per row. */ const sizeMatrix = [];\n    /** @type {Array<number>} */ const longestCellByColumn = [];\n    let mostCellsPerRow = 0;\n    let rowIndex = -1;\n    // This is a superfluous loop if we don’t align delimiters, but otherwise we’d\n    // do superfluous work when aligning, so optimize for aligning.\n    while(++rowIndex < table.length){\n        /** @type {Array<string>} */ const row = [];\n        /** @type {Array<number>} */ const sizes = [];\n        let columnIndex = -1;\n        if (table[rowIndex].length > mostCellsPerRow) {\n            mostCellsPerRow = table[rowIndex].length;\n        }\n        while(++columnIndex < table[rowIndex].length){\n            const cell = serialize(table[rowIndex][columnIndex]);\n            if (settings.alignDelimiters !== false) {\n                const size = stringLength(cell);\n                sizes[columnIndex] = size;\n                if (longestCellByColumn[columnIndex] === undefined || size > longestCellByColumn[columnIndex]) {\n                    longestCellByColumn[columnIndex] = size;\n                }\n            }\n            row.push(cell);\n        }\n        cellMatrix[rowIndex] = row;\n        sizeMatrix[rowIndex] = sizes;\n    }\n    // Figure out which alignments to use.\n    let columnIndex = -1;\n    if (typeof align === \"object\" && \"length\" in align) {\n        while(++columnIndex < mostCellsPerRow){\n            alignments[columnIndex] = toAlignment(align[columnIndex]);\n        }\n    } else {\n        const code = toAlignment(align);\n        while(++columnIndex < mostCellsPerRow){\n            alignments[columnIndex] = code;\n        }\n    }\n    // Inject the alignment row.\n    columnIndex = -1;\n    /** @type {Array<string>} */ const row = [];\n    /** @type {Array<number>} */ const sizes = [];\n    while(++columnIndex < mostCellsPerRow){\n        const code = alignments[columnIndex];\n        let before = \"\";\n        let after = \"\";\n        if (code === 99 /* `c` */ ) {\n            before = \":\";\n            after = \":\";\n        } else if (code === 108 /* `l` */ ) {\n            before = \":\";\n        } else if (code === 114 /* `r` */ ) {\n            after = \":\";\n        }\n        // There *must* be at least one hyphen-minus in each alignment cell.\n        let size = settings.alignDelimiters === false ? 1 : Math.max(1, longestCellByColumn[columnIndex] - before.length - after.length);\n        const cell = before + \"-\".repeat(size) + after;\n        if (settings.alignDelimiters !== false) {\n            size = before.length + size + after.length;\n            if (size > longestCellByColumn[columnIndex]) {\n                longestCellByColumn[columnIndex] = size;\n            }\n            sizes[columnIndex] = size;\n        }\n        row[columnIndex] = cell;\n    }\n    // Inject the alignment row.\n    cellMatrix.splice(1, 0, row);\n    sizeMatrix.splice(1, 0, sizes);\n    rowIndex = -1;\n    /** @type {Array<string>} */ const lines = [];\n    while(++rowIndex < cellMatrix.length){\n        const row = cellMatrix[rowIndex];\n        const sizes = sizeMatrix[rowIndex];\n        columnIndex = -1;\n        /** @type {Array<string>} */ const line = [];\n        while(++columnIndex < mostCellsPerRow){\n            const cell = row[columnIndex] || \"\";\n            let before = \"\";\n            let after = \"\";\n            if (settings.alignDelimiters !== false) {\n                const size = longestCellByColumn[columnIndex] - (sizes[columnIndex] || 0);\n                const code = alignments[columnIndex];\n                if (code === 114 /* `r` */ ) {\n                    before = \" \".repeat(size);\n                } else if (code === 99 /* `c` */ ) {\n                    if (size % 2) {\n                        before = \" \".repeat(size / 2 + 0.5);\n                        after = \" \".repeat(size / 2 - 0.5);\n                    } else {\n                        before = \" \".repeat(size / 2);\n                        after = before;\n                    }\n                } else {\n                    after = \" \".repeat(size);\n                }\n            }\n            if (settings.delimiterStart !== false && !columnIndex) {\n                line.push(\"|\");\n            }\n            if (settings.padding !== false && // Don’t add the opening space if we’re not aligning and the cell is\n            // empty: there will be a closing space.\n            !(settings.alignDelimiters === false && cell === \"\") && (settings.delimiterStart !== false || columnIndex)) {\n                line.push(\" \");\n            }\n            if (settings.alignDelimiters !== false) {\n                line.push(before);\n            }\n            line.push(cell);\n            if (settings.alignDelimiters !== false) {\n                line.push(after);\n            }\n            if (settings.padding !== false) {\n                line.push(\" \");\n            }\n            if (settings.delimiterEnd !== false || columnIndex !== mostCellsPerRow - 1) {\n                line.push(\"|\");\n            }\n        }\n        lines.push(settings.delimiterEnd === false ? line.join(\"\").replace(/ +$/, \"\") : line.join(\"\"));\n    }\n    return lines.join(\"\\n\");\n}\n/**\n * @param {string | null | undefined} [value]\n *   Value to serialize.\n * @returns {string}\n *   Result.\n */ function serialize(value) {\n    return value === null || value === undefined ? \"\" : String(value);\n}\n/**\n * @param {string | null | undefined} value\n *   Value.\n * @returns {number}\n *   Alignment.\n */ function toAlignment(value) {\n    const code = typeof value === \"string\" ? value.codePointAt(0) : 0;\n    return code === 67 /* `C` */  || code === 99 /* `c` */  ? 99 /* `c` */  : code === 76 /* `L` */  || code === 108 /* `l` */  ? 108 /* `l` */  : code === 82 /* `R` */  || code === 114 /* `r` */  ? 114 /* `r` */  : 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/markdown-table/index.js\n");

/***/ })

};
;