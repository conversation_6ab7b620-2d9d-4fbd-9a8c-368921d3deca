"use client";

import React, { useState } from 'react';
import { cn } from "@/lib/utils";
import { 
  FileText, 
  Users, 
  MessageSquare, 
  Database, 
  Settings, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Send,
  Globe,
  Tag,
  User,
  Zap,
  Search,
  Filter,
  MoreHorizontal,
  ChevronDown,
  Star,
  Calendar,
  BarChart3,
  TrendingUp,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, Di<PERSON><PERSON>eader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface BlogPost {
  id: string;
  title: string;
  content: string;
  language: string;
  seoTitle: string;
  seoDescription: string;
  tags: string[];
  series?: string;
  authorId: string;
  status: 'draft' | 'published' | 'scheduled';
  createdAt: string;
  publishedAt?: string;
}

interface Author {
  id: string;
  name: string;
  email: string;
  bio: string;
  avatar?: string;
  createdAt: string;
}

interface Prompt {
  id: string;
  name: string;
  content: string;
  category: string;
  isActive: boolean;
  createdAt: string;
}

interface Project {
  id: string;
  name: string;
  description: string;
  database: string;
  isActive: boolean;
  createdAt: string;
}

const SEOBlogManagementSystem = () => {
  const [activeTab, setActiveTab] = useState('generate');
  const [selectedProject, setSelectedProject] = useState('project-1');
  
  // Sample data
  const [blogPosts] = useState<BlogPost[]>([
    {
      id: '1',
      title: 'Getting Started with React Hooks',
      content: 'React Hooks revolutionized how we write React components...',
      language: 'en',
      seoTitle: 'React Hooks Tutorial - Complete Guide 2024',
      seoDescription: 'Learn React Hooks with practical examples and best practices',
      tags: ['react', 'hooks', 'javascript'],
      series: 'React Fundamentals',
      authorId: 'author-1',
      status: 'published',
      createdAt: '2024-01-15',
      publishedAt: '2024-01-16'
    },
    {
      id: '2',
      title: 'Advanced TypeScript Patterns',
      content: 'TypeScript offers powerful type system features...',
      language: 'en',
      seoTitle: 'Advanced TypeScript Patterns for Better Code',
      seoDescription: 'Master advanced TypeScript patterns and improve your code quality',
      tags: ['typescript', 'patterns', 'programming'],
      authorId: 'author-2',
      status: 'draft',
      createdAt: '2024-01-20'
    }
  ]);

  const [authors] = useState<Author[]>([
    {
      id: 'author-1',
      name: 'John Doe',
      email: '<EMAIL>',
      bio: 'Senior Frontend Developer with 5+ years experience',
      createdAt: '2024-01-01'
    },
    {
      id: 'author-2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      bio: 'Full-stack developer and technical writer',
      createdAt: '2024-01-02'
    }
  ]);

  const [prompts] = useState<Prompt[]>([
    {
      id: 'prompt-1',
      name: 'Technical Blog Post',
      content: 'Write a comprehensive technical blog post about {topic}. Include practical examples, best practices, and real-world applications.',
      category: 'Technical',
      isActive: true,
      createdAt: '2024-01-01'
    },
    {
      id: 'prompt-2',
      name: 'Tutorial Guide',
      content: 'Create a step-by-step tutorial for {topic}. Make it beginner-friendly with clear explanations and code examples.',
      category: 'Tutorial',
      isActive: true,
      createdAt: '2024-01-02'
    }
  ]);

  const [projects] = useState<Project[]>([
    {
      id: 'project-1',
      name: 'Tech Blog',
      description: 'Main technology blog with programming tutorials',
      database: 'tech_blog_db',
      isActive: true,
      createdAt: '2024-01-01'
    },
    {
      id: 'project-2',
      name: 'Marketing Blog',
      description: 'Marketing and business content',
      database: 'marketing_blog_db',
      isActive: false,
      createdAt: '2024-01-05'
    }
  ]);

  // Form states
  const [generateForm, setGenerateForm] = useState({
    keywords: '',
    title: '',
    language: 'en',
    seoTitle: '',
    seoDescription: '',
    tags: '',
    series: '',
    authorId: '',
    promptId: ''
  });

  const [isGenerating, setIsGenerating] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [createType, setCreateType] = useState<'author' | 'prompt'>('author');

  const handleGenerate = async () => {
    setIsGenerating(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 3000));
    setIsGenerating(false);
    // Reset form or show success message
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      published: 'default',
      draft: 'secondary',
      scheduled: 'outline'
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status}
      </Badge>
    );
  };

  const stats = {
    totalPosts: blogPosts.length,
    publishedPosts: blogPosts.filter(p => p.status === 'published').length,
    draftPosts: blogPosts.filter(p => p.status === 'draft').length,
    totalAuthors: <AUTHORS>
    activePrompts: prompts.filter(p => p.isActive).length
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Zap className="h-8 w-8 text-primary" />
                <h1 className="text-2xl font-bold text-foreground">SEO Blog AI</h1>
              </div>
              <Badge variant="outline">v2.0</Badge>
            </div>
            
            <div className="flex items-center gap-4">
              <Select value={selectedProject} onValueChange={setSelectedProject}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {projects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      <div className="flex items-center gap-2">
                        <div className={cn(
                          "w-2 h-2 rounded-full",
                          project.isActive ? "bg-green-500" : "bg-gray-400"
                        )} />
                        {project.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Stats Dashboard */}
      <div className="container mx-auto px-6 py-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Posts</p>
                  <p className="text-2xl font-bold">{stats.totalPosts}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Published</p>
                  <p className="text-2xl font-bold">{stats.publishedPosts}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Drafts</p>
                  <p className="text-2xl font-bold">{stats.draftPosts}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Authors</p>
                  <p className="text-2xl font-bold">{stats.totalAuthors}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-teal-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Active Prompts</p>
                  <p className="text-2xl font-bold">{stats.activePrompts}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Generate
            </TabsTrigger>
            <TabsTrigger value="manage" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Manage Posts
            </TabsTrigger>
            <TabsTrigger value="prompts" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Prompts
            </TabsTrigger>
            <TabsTrigger value="authors" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Authors
            </TabsTrigger>
            <TabsTrigger value="projects" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              Projects
            </TabsTrigger>
          </TabsList>

          {/* Blog Generation Tab */}
          <TabsContent value="generate" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Generate Blog Post
                </CardTitle>
                <CardDescription>
                  Create SEO-optimized blog posts using AI with customizable prompts and settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="keywords">Keywords *</Label>
                      <Input
                        id="keywords"
                        placeholder="Enter keywords separated by commas"
                        value={generateForm.keywords}
                        onChange={(e) => setGenerateForm(prev => ({ ...prev, keywords: e.target.value }))}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="title">Article Title</Label>
                      <Input
                        id="title"
                        placeholder="Leave empty for auto-generation"
                        value={generateForm.title}
                        onChange={(e) => setGenerateForm(prev => ({ ...prev, title: e.target.value }))}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="language">Language</Label>
                      <Select value={generateForm.language} onValueChange={(value) => setGenerateForm(prev => ({ ...prev, language: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="es">Spanish</SelectItem>
                          <SelectItem value="fr">French</SelectItem>
                          <SelectItem value="de">German</SelectItem>
                          <SelectItem value="zh">Chinese</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor="tags">Tags</Label>
                      <Input
                        id="tags"
                        placeholder="Enter tags separated by commas"
                        value={generateForm.tags}
                        onChange={(e) => setGenerateForm(prev => ({ ...prev, tags: e.target.value }))}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="seo-title">SEO Title</Label>
                      <Input
                        id="seo-title"
                        placeholder="Auto-generated if empty"
                        value={generateForm.seoTitle}
                        onChange={(e) => setGenerateForm(prev => ({ ...prev, seoTitle: e.target.value }))}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="seo-description">SEO Description</Label>
                      <Textarea
                        id="seo-description"
                        placeholder="Auto-generated if empty"
                        value={generateForm.seoDescription}
                        onChange={(e) => setGenerateForm(prev => ({ ...prev, seoDescription: e.target.value }))}
                        rows={3}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="series">Blog Series</Label>
                      <Input
                        id="series"
                        placeholder="Optional: Part of a series"
                        value={generateForm.series}
                        onChange={(e) => setGenerateForm(prev => ({ ...prev, series: e.target.value }))}
                      />
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="author">Author</Label>
                    <Select value={generateForm.authorId} onValueChange={(value) => setGenerateForm(prev => ({ ...prev, authorId: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select author" />
                      </SelectTrigger>
                      <SelectContent>
                        {authors.map(author => (
                          <SelectItem key={author.id} value={author.id}>
                            {author.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="prompt">Prompt Template</Label>
                    <Select value={generateForm.promptId} onValueChange={(value) => setGenerateForm(prev => ({ ...prev, promptId: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select prompt" />
                      </SelectTrigger>
                      <SelectContent>
                        {prompts.filter(p => p.isActive).map(prompt => (
                          <SelectItem key={prompt.id} value={prompt.id}>
                            {prompt.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button 
                    onClick={handleGenerate} 
                    disabled={isGenerating || !generateForm.keywords}
                    className="min-w-32"
                  >
                    {isGenerating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        Generate Post
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Blog Management Tab */}
          <TabsContent value="manage" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Blog Posts</CardTitle>
                    <CardDescription>Manage your blog posts and content</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button variant="outline" size="sm">
                      <Search className="h-4 w-4 mr-2" />
                      Search
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Author</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Tags</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {blogPosts.map(post => (
                      <TableRow key={post.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{post.title}</p>
                            <p className="text-sm text-muted-foreground">{post.seoTitle}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          {authors.find(a => a.id === post.authorId)?.name}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(post.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {post.tags.slice(0, 2).map(tag => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {post.tags.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{post.tags.length - 2}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{post.createdAt}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Send className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Prompts Tab */}
          <TabsContent value="prompts" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Prompt Templates</CardTitle>
                    <CardDescription>Manage AI prompts for content generation</CardDescription>
                  </div>
                  <Button onClick={() => { setCreateType('prompt'); setShowCreateDialog(true); }}>
                    <Plus className="h-4 w-4 mr-2" />
                    New Prompt
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {prompts.map(prompt => (
                    <Card key={prompt.id}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{prompt.name}</CardTitle>
                          <div className="flex items-center gap-2">
                            <Badge variant={prompt.isActive ? "default" : "secondary"}>
                              {prompt.isActive ? "Active" : "Inactive"}
                            </Badge>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <Badge variant="outline">{prompt.category}</Badge>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground mb-4">
                          {prompt.content.substring(0, 120)}...
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-muted-foreground">
                            Created {prompt.createdAt}
                          </span>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Authors Tab */}
          <TabsContent value="authors" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Authors</CardTitle>
                    <CardDescription>Manage author profiles and information</CardDescription>
                  </div>
                  <Button onClick={() => { setCreateType('author'); setShowCreateDialog(true); }}>
                    <Plus className="h-4 w-4 mr-2" />
                    New Author
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {authors.map(author => (
                    <Card key={author.id}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                            <User className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">{author.name}</CardTitle>
                            <p className="text-sm text-muted-foreground">{author.email}</p>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground mb-4">
                          {author.bio}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-muted-foreground">
                            Joined {author.createdAt}
                          </span>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Projects Tab */}
          <TabsContent value="projects" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Projects</CardTitle>
                    <CardDescription>Manage different blog projects and databases</CardDescription>
                  </div>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    New Project
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {projects.map(project => (
                    <Card key={project.id}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={cn(
                              "w-3 h-3 rounded-full",
                              project.isActive ? "bg-green-500" : "bg-gray-400"
                            )} />
                            <CardTitle className="text-lg">{project.name}</CardTitle>
                          </div>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground mb-2">
                          {project.description}
                        </p>
                        <p className="text-xs text-muted-foreground mb-4">
                          Database: {project.database}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-muted-foreground">
                            Created {project.createdAt}
                          </span>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Database className="h-4 w-4 mr-1" />
                              Sync
                            </Button>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Create Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Create New {createType === 'author' ? 'Author' : 'Prompt'}
            </DialogTitle>
            <DialogDescription>
              {createType === 'author' 
                ? 'Add a new author profile to your blog system'
                : 'Create a new prompt template for AI content generation'
              }
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {createType === 'author' ? (
              <>
                <div>
                  <Label htmlFor="author-name">Name</Label>
                  <Input id="author-name" placeholder="Author name" />
                </div>
                <div>
                  <Label htmlFor="author-email">Email</Label>
                  <Input id="author-email" type="email" placeholder="<EMAIL>" />
                </div>
                <div>
                  <Label htmlFor="author-bio">Bio</Label>
                  <Textarea id="author-bio" placeholder="Author biography" rows={3} />
                </div>
              </>
            ) : (
              <>
                <div>
                  <Label htmlFor="prompt-name">Name</Label>
                  <Input id="prompt-name" placeholder="Prompt name" />
                </div>
                <div>
                  <Label htmlFor="prompt-category">Category</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="technical">Technical</SelectItem>
                      <SelectItem value="tutorial">Tutorial</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="news">News</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="prompt-content">Prompt Content</Label>
                  <Textarea 
                    id="prompt-content" 
                    placeholder="Enter your prompt template. Use {topic} for dynamic content."
                    rows={4}
                  />
                </div>
              </>
            )}
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowCreateDialog(false)}>
              Create {createType === 'author' ? 'Author' : 'Prompt'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SEOBlogManagementSystem;